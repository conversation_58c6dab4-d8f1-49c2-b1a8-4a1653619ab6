application.secureUrl="http://dev-vpc.socure.com"
facebook.longlived.accesstoken.url="https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token"
facebook.appid.url="https://graph.facebook.com/app?access_token="

future.util.wait.duration=50

#Jetty server related config
server {
  port = 5000
  apiTimeoutInMins = 10
  minthreads = 50
  maxthreads = 500
  threadPoolName = "jettyThreadPool"
  enable.gc.metrics = false
}

ecbsv.firing.rule {
  inclusion-triggers-numericals {
    EXVAL.100021=1
    EXVAL.100040=1
    EXVAL.100062=1
    EXVAL.100097=1
    EXVAL.100098=1
    EXVAL.100054=1
    EXVAL.100063=1
    EXVAL.100095=1
    EXVAL.100100=1
    EXVAL.100089=1
    EXVAL.100025=1
    EXVAL.100061=1
    EXVAL.100087=1
    EXVAL.100085=1
    EXVAL.100078=1
    EXVAL.100019=1
  }
  inclusion-triggers-categoricals {
  }
  exclusion-triggers-numericals {
    EXVAL.100069=1
    EXVAL.100071=1
    EXVAL.100009=1
    EXVAL.100003=1
    EXVAL.100203=.99
    EXVAL.100027=1
    EXVAL.100086=1
    EXVAL.100112=1
    EXVAL.100096=1
  }
  exclusion-triggers-categoricals {
  }
  composite-trigger {
  composite-trigger1=[EXVAL.100097,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger2=[EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger3=[EXVAL.100021,EXVAL.100040,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger4=[EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger5=[EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger6=[EXVAL.100097,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger7=[EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger8=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger9=[EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger10=[EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger11=[EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger12=[EXVAL.100021,EXVAL.100040,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger13=[EXVAL.100097,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger14=[EXVAL.100097,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger15=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger16=[EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger17=[EXVAL.100097,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger18=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger19=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger20=[EXVAL.100097,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger21=[EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger22=[EXVAL.100097,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger23=[EXVAL.100097,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger24=[EXVAL.100097,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger25=[EXVAL.100021,EXVAL.100040,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger26=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger27=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger28=[EXVAL.100097,EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger29=[EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger30=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger31=[EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger32=[EXVAL.100097,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger33=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger34=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger35=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger36=[EXVAL.100097,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger37=[EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger38=[EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger39=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger40=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger41=[EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger42=[EXVAL.100097,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger43=[EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger44=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger45=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger46=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger47=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger48=[EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger49=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger50=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger51=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger52=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger53=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger54=[EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger55=[EXVAL.100097,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger56=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger57=[EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger58=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger59=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger60=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger61=[EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger62=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger63=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger64=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger65=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger66=[EXVAL.100097,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger67=[EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger68=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger69=[EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger70=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger71=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger72=[EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger73=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger74=[EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger75=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger76=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger77=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger78=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger79=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger80=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger81=[EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger82=[EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger83=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger84=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger85=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger86=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger87=[EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger88=[EXVAL.100097,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger89=[EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger90=[EXVAL.100054,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger91=[EXVAL.100021,EXVAL.100040,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger92=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger93=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger94=[EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger95=[EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger96=[EXVAL.100097,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger97=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger98=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger99=[EXVAL.100098,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger100=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger101=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger102=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger103=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger104=[EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger105=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger106=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger107=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger108=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger109=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger110=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger111=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger112=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger113=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger114=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger115=[EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger116=[EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger117=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger118=[EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger119=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger120=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger121=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger122=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger123=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger124=[EXVAL.100097,EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger125=[EXVAL.100097,EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger126=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger127=[EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger128=[EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger129=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger130=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger131=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger132=[EXVAL.100097,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger133=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger134=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger135=[EXVAL.100097,EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger136=[EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger137=[EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger138=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger139=[EXVAL.100097,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger140=[EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger141=[EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger142=[EXVAL.100097,EXVAL.100054,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger143=[EXVAL.100097,EXVAL.100098,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger144=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger145=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger146=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger147=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger148=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger149=[EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger150=[EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger151=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger152=[EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger153=[EXVAL.100097,EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger154=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger155=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100095,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger156=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger157=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger158=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger159=[EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger160=[EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger161=[EXVAL.100097,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger162=[EXVAL.100097,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger163=[EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger164=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger165=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger166=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger167=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger168=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger169=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger170=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger171=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger172=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger173=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger174=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger175=[EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger176=[EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger177=[EXVAL.100097,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger178=[EXVAL.100097,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger179=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger180=[EXVAL.100062,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger181=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger182=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger183=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger184=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger185=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger186=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger187=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger188=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger189=[EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger190=[EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger191=[EXVAL.100098,EXVAL.100054,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger192=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger193=[EXVAL.100097,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger194=[EXVAL.100097,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger195=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger196=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger197=[EXVAL.100097,EXVAL.100054,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger198=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger199=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger200=[EXVAL.100062,EXVAL.100069,EXVAL.100071,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger201=[EXVAL.100062,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger202=[EXVAL.100021,EXVAL.100040,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger203=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger204=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger205=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger206=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger207=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger208=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger209=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger210=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger211=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger212=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger213=[EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger214=[EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger215=[EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger216=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger217=[EXVAL.100097,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger218=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger219=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger220=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger221=[EXVAL.100097,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger222=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger223=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger224=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger225=[EXVAL.100021,EXVAL.100040,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger226=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger227=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger228=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger229=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger230=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger231=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger232=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger233=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger234=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger235=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger236=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger237=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger238=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger239=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger240=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger241=[EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger242=[EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger243=[EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger244=[EXVAL.100098,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger245=[EXVAL.100098,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger246=[EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger247=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger248=[EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger249=[EXVAL.100097,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger250=[EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger251=[EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger252=[EXVAL.100097,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger253=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger254=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger255=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger256=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger257=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger258=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger259=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger260=[EXVAL.100097,EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger261=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger262=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger263=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger264=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger265=[EXVAL.100097,EXVAL.100098,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger266=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger267=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger268=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger269=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger270=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger271=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger272=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger273=[EXVAL.100021,EXVAL.100040,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger274=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger275=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger276=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger277=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger278=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger279=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger280=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger281=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger282=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger283=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger284=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger285=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger286=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger287=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger288=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger289=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger290=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger291=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger292=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger293=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger294=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger295=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger296=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger297=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger298=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger299=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger300=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger301=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger302=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger303=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger304=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger305=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger306=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger307=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger308=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger309=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger310=[EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger311=[EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger312=[EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger313=[EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger314=[EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger315=[EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger316=[EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger317=[EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger318=[EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger319=[EXVAL.100098,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger320=[EXVAL.100098,EXVAL.100054,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger321=[EXVAL.100097,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger322=[EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger323=[EXVAL.100097,EXVAL.100089,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger324=[EXVAL.100097,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger325=[EXVAL.100097,EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger326=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger327=[EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger328=[EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger329=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger330=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger331=[EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger332=[EXVAL.100097,EXVAL.100054,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger333=[EXVAL.100097,EXVAL.100054,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger334=[EXVAL.100097,EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger335=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger336=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger337=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger338=[EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger339=[EXVAL.100097,EXVAL.100098,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger340=[EXVAL.100097,EXVAL.100098,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger341=[EXVAL.100097,EXVAL.100098,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger342=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger343=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger344=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger345=[EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger346=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger347=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger348=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger349=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger350=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger351=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger352=[EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger353=[EXVAL.100062,EXVAL.100069,EXVAL.100071,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger354=[EXVAL.100021,EXVAL.100040,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger355=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger356=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger357=[EXVAL.100021,EXVAL.100040,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger358=[EXVAL.100021,EXVAL.100040,EXVAL.100100,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger359=[EXVAL.100021,EXVAL.100040,EXVAL.100095,EXVAL.100100,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger360=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger361=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger362=[EXVAL.100021,EXVAL.100040,EXVAL.100063,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger363=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger364=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger365=[EXVAL.100021,EXVAL.100040,EXVAL.100054,EXVAL.100095,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger366=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger367=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger368=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger369=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger370=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger371=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger372=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger373=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger374=[EXVAL.100021,EXVAL.100040,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger375=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger376=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger377=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100100,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger378=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger379=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100095,EXVAL.100100,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger380=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger381=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger382=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger383=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger384=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger385=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger386=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger387=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger388=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger389=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger390=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger391=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger392=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100063,EXVAL.100095,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger393=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger394=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100095,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger395=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100025,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger396=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger397=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100100,EXVAL.100061,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger398=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100100,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger399=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100054,EXVAL.100063,EXVAL.100100,EXVAL.100025,EXVAL.100061,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger400=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger401=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger402=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger403=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger404=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger405=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger406=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100089,EXVAL.100025,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger407=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100100,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger408=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger409=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100095,EXVAL.100100,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger410=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger411=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100089,EXVAL.100025,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger412=[EXVAL.100021,EXVAL.100040,EXVAL.100097,EXVAL.100098,EXVAL.100054,EXVAL.100063,EXVAL.100095,EXVAL.100087,EXVAL.100069,EXVAL.100071,EXVAL.100019,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  composite-trigger413=[EXVAL.100021,EXVAL.100040,EXVAL.100062,EXVAL.100069,EXVAL.100071,EXVAL.100003,EXVAL.100009,EXVAL.100003,EXVAL.100203,EXVAL.100027,EXVAL.100086,EXVAL.100112,EXVAL.100078,EXVAL.100096]
  }
  inclusion-triggers-decision-logic-rulecodes {
    GLOBAL.910074=1
    GLOBAL.910075=1
    GLOBAL.910076=1
    GLOBAL.910077=1
    GLOBAL.900027=1
    GLOBAL.900030=1
    GLOBAL.910003=1
    GLOBAL.910036=1
    GLOBAL.900001=1
    GLOBAL.900002=1
    GLOBAL.900005=1
    GLOBAL.910010=1
    GLOBAL.910037=1
    GLOBAL.900032=1
    GLOBAL.910038=1
    GLOBAL.910012=1
    GLOBAL.910033=1
    GLOBAL.910013=1
    GLOBAL.910014=1
    GLOBAL.900015=1
    GLOBAL.900021=1
    GLOBAL.910023=1
    GLOBAL.900025=1
    GLOBAL.900026=1
    GLOBAL.910035=1
    GLOBAL.900011=1
    GLOBAL.900035=1
  }
  implicit-ecbsv-trigger-rulecodes {
    GLOBAL.910090=1
    GLOBAL.300314=1
    GLOBAL.900030=1
    GLOBAL.900021=1
    GLOBAL.910014=1
    GLOBAL.910012=1
    GLOBAL.910009=1
    GLOBAL.900005=1
    GLOBAL.900002=1
    GLOBAL.900001=1
    GLOBAL.910003=1
    GLOBAL.900027=1
  }
# use below config to control ecbsv trigger rulecodes based on account flag
#  implicit-ecbsv-trigger-rulecode-accountflag-map {
#  }
}


# Model Management DB config
mmdb.driver="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
mmdb.jdbcUrl="jdbc-secretsmanager:mysql://model-management-dev.cluster-c8dcsxvsf1es.us-east-1.rds.amazonaws.com:3306/model_management"
mmdb.dataSourceName=model-management-db
mmdb.user="rds-model-management-dev-883f8c-app"
mmdb.maxIdleTime=900
mmdb.maxConnectionAge=3600
mmdb.forceUseNamedDriverClass="true"

mmdb.maxPoolSize=50
mmdb.minPoolSize=3
mmdb.initialPoolSize=3
mmdb.testConnectionOnCheckIn="true"
mmdb.testConnectionOnCheckOut="false"
mmdb.idleConnectionTestPeriod=20

mmdb.useNewDB="true"
mmdb.modelIdSize=10

model.association.cache.ttl.expire="1 minutes"
model.association.cache.ttl.refresh="30 seconds"

# Mysql db configuration
db.driver="com.mysql.cj.jdbc.Driver"
db.url="************************************************************************************************************************************************************************************************************************************************************************************"
db.username="socure"
db.password="""ENC(7CIxsHzprWib3CUmVAUhqCP3YbEfKUPdQSoRfRPnltBiH9APM4OWlrU=)"""
db.defaultAutoCommit="true"

#Hibernate pool
hibernate.c3p0.initialPoolSize=10
hibernate.c3p0.min_size=10
hibernate.c3p0.max_size=25

hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
hibernate.c3p0.timeout=100
hibernate.c3p0.max_statements=0
hibernate.c3p0.idle_test_period=90

hibernate.c3p0.unreturnedConnectionTimeout=10
hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
hibernate.c3p0.acquireincrement=8
hibernate.c3p0.maxconnectionage=3600
hibernate.c3p0.validationQuery="SELECT 1"
hibernate.c3p0.testOnBorrow="true"
hibernate.c3p0.numHelperThreads=200
hibernate.c3p0.automaticTestTable="tbl_hibernate_c3p0TestTable"
hibernate.c3p0.testOnCheckin=true

#Hibernate JConsole
hibernate.generate_statistics="true"

#Memcache Servers
memcache.servers="localhost:11211"
opt.timeout=500

#Static resources like js, css, images url from s3 bucket
static.resource.url="https://dev-static.socure.com.s3.amazonaws.com"

#Debug Settings
debugMode="true"

#Scoring Cache Properties
#1 day(in seconds)
authscore.memcached.timeout=300
#1 day(in seconds)
authscore.low_confidence.memcached.timeout=300
#1 day(in seconds)
authscore.high_confidence.memcached.timeout=300
# The timeout(in days) for the scoring of a user. This is used for the second level cache.
authscore.last_scanned_days.cache.timeout=2

#Facebook throttling mail ID
fb.throttling.mail.to="Engineering <<EMAIL>>"

#Factual Credentials
factual.oauth.key="g1tCPVR41NlU96STdd5kw1LnYzHkCd5aAGoBfY3w"
factual.oauth.secret="GMHuY7NpNcNjnioQCepzhOSqKjynMaSZhLAXrot3"

#Scoring Component Memcache
component.email.memcached.timeout=15552000
component.form.memcached.timeout=15552000
component.fb.memcached.timeout=15552000
component.pipl.email.memcached.timeout=15552000
component.pipl.address.memcached.timeout=15552000
component.gplus.memcached.timeout=15552000
component.twitter.memcached.timeout=15552000
component.fullcontact.memcached.timeout=15552000
component.yahoo.memcached.timeout=15552000

#Scoring Component Database
db.component.email.timeout=15552000
db.component.form.timeout=15552000
db.component.fb.timeout=15552000
db.component.pipl.email.timeout=15552000
db.component.pipl.address.timeout=15552000
db.component.gplus.timeout=15552000
db.component.twitter.timeout=15552000
db.component.fullcontact.timeout=15552000
db.yahoo.memcached.timeout=15552000

#==============FullContact Integration===============
fullcontact.apikey="""ENC(SlpIktolbOAUPLvRNnq1u9lWcOHj++zVskSLMn/6tb3y3aGhsE+wPIba9ByE4kligSbzKPYspdRe5+0O13qGi1iQcieZgo45cA0GeS1D1pYbjI6AjXcc9KN4R6T7kuGq)"""
fullcontact.webhook.base.url="https://dev-vpc.socure.com/"

#============== Admin Specific Properties =================
application.authScore.baseUrl="https://dev.socure.com"
application.logoutUrl="http://dev-vpc-dashboard.socure.com.s3-website-us-east-1.amazonaws.com/"
application.activation.url="https://admindev.socure.com/activate/"
application.forget.pass.url="https://admindev.socure.com/reset_password/"

#static resources
marketing.url="http://dev-www.socure.com/"

#============== Admin Specific Properties =================

#==== Fraud Score Service API =======
fraud.score.service="http://socure-fraudscore-stg1v.us-east-1.elasticbeanstalk.com/fraudscore/generic/1"

#============= Entity Image ==============
#In Seconds
cache.image.timeout=86400
#============= Entity Image ==============

#============= Hibernate Search ==============
hibernate.search.fuzzy.threshold=18
#============= Hibernate Search ==============


#=============== Thread pool configuration ==================
thread.pool.executor.pool.size=400
thread.pool.executor.max.pool.size=700
thread.pool.executor.queue.capacity=0
thread.pool.executor.keep.alive.seconds=1
#=============== Thread pool configuration ==================

#============= SLA ROLE =====================
component.default.timeout=4

default.sla.fmval.timeout=4
default.sla.bmevl.timeout=4
default.sla.fcval.timeout=4
default.sla.yoval.timeout=4
default.sla.pival.timeout=4
default.sla.paval.timeout=4
default.sla.boval.tiemout=4
default.sla.lival.timeout=4
default.sla.gpval.timeout=4
default.sla.fbval.timeout=5
default.sla.twval.timeout=5
default.sla.nsval.timeout=10
default.sla.asavl.timeout=4
default.sla.kycofacval.timeout=10
default.sla.api.v2.scheduler.timeout ="5000"
default.sla.api.v2.overall.timeout ="10000"
default.sla.api.system.overall.timeout=10200

#=======================================
documentverification.sla.api.v2.scheduler.timeout=300000
documentverification.sla.api.v2.overall.timeout=450000
documentverification.sla.api.system.overall.timeout=5000000
#=======================================

highperformace.sla.fmval.timeout=2
highperformace.sla.bmevl.timeout=2
highperformace.sla.fcval.timeout=2
highperformace.sla.yoval.timeout=2
highperformace.sla.pival.timeout=2
highperformace.sla.paval.timeout=2
highperformace.sla.boval.tiemout=2
highperformace.sla.lival.timeout=2
highperformace.sla.gpval.timeout=2
highperformace.sla.fbval.timeout=3
highperformace.sla.twval.timeout=2
highperformace.sla.asavl.timeout=2
highperformace.sla.kycofacval.timeout=2

highperformace.sla.api.v2.scheduler.timeout ="2200"
highperformace.sla.api.v2.overall.timeout ="2700"
highperformace.sla.api.system.overall.timeout=2800
#====================================================

api.v2.overall.timeunit="MILLISECONDS"
api.v2.scheduler.timeunit="MILLISECONDS"
api.system.overall.timeunit="MILLISECONDS"

unacceptible.component.internal.error.threshold=2.0
unacceptible.component.timeout.threshold=2.0
fraudscore.overall.timeout.ms=2500
syntheticscore.overall.timeout.ms=2500
ecbsv.implicit.timeout.ms=2000
fpfscore.overall.timeout.ms=2000
SEARCH_LEVEL_DEPTH=1
#=======================================

#=============== PIPL Credentials ============
pipl.key="""ENC(3t484/ed/V8G7xbW7buNXxPe/fGkuUTdTSs0BLiMXtOOueb3CDrjVH6+wVP2E+f6ouhR2op4bsc=)"""
pipl.premium.key="""ENC(VaZSw4A7OY3MGgq+liHQNTIoXQZRnZHLp6yqaGNN8oQlQwZUfqo6bQDJ2ws2W/3a86bx9NvtuXE=)"""
pipl.match.criteria="matching"
pipl.partial.threshold=5
pipl.source.threshold=0.8
#=============== PIPL Credentials ============

#============= Decision Plugin ==============
decision.service.endpoint="https://decision-service.webapps.us-east-1.product-dev.socure.link"
decision.service.endpoint2="https://decision-service.webapps.us-east-1.product-dev.socure.link"
decision.service.groupName="UXServicesRamp"
decision.service.flagName="DecisionService_Ramp"
decision.service.timeout.ms=250

#=============== Decision Plugin ============

#============= EMS Plugin ==============
entity.monitoring {
  endpoint="https://entity-monitoring-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://entity-monitoring-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "entity-monitoring-service/dev/hmac-0e1497"
    secret.refresh.interval = 5000
    realm="Socure"
    version = "1.0"
  }
  monitors.max.page.size=100
}
#=============== EMS Plugin ============

#=============== PIPL PLUGIN============
pipl.domain="api.pipl.com"
pipl.timeout=1000
pipl.fields.key="""ENC(3t484/ed/V8G7xbW7buNXxPe/fGkuUTdTSs0BLiMXtOOueb3CDrjVH6+wVP2E+f6ouhR2op4bsc=)"""
pipl.fields.premium_key="""ENC(VaZSw4A7OY3MGgq+liHQNTIoXQZRnZHLp6yqaGNN8oQlQwZUfqo6bQDJ2ws2W/3a86bx9NvtuXE=)"""
pipl.partialMatchThreshold=5
pipl.fields.pretty="true"
pipl.fields.minimum_probability=0.9
pipl.fields.minimum_match=0.0
pipl.fields.show_sources="matching"
pipl.fields.hide_sponsored="false"
pipl.fields.live_feeds="false"
pipl.fields.callback=""""""""
pipl.filterCriteria.all=0.5
#===========================

#=============== DSTK ============
data.science.toolkit.url="http://dstk-dev.us-east-vpc.socure.be"
data.science.toolkit.fetch.timeout=1500
data.science.toolkit.fetch.timeunit="MILLISECONDS"
data.science.toolkit.sleep.duration.ms=20
#=============== DSTK ============

#=============== DSTK plugin ============
dstk.domain="dstk-dev.us-east-vpc.socure.be"
dstk.geoUri="street2coordinates"
#=============== DSTK ============

#============= Entity Resolution Filter Config =============#
default.er.component.filter.name=0.5
default.er.component.filter.email=0.5
default.er.component.filter.address=0.5
default.er.component.filter.geocode=0.5
default.er.component.filter.companyname=0.5
default.er.component.filter.nationalid=0.5
default.er.component.filter.dob=0.5
default.er.component.filter.mobilenumber=0.5
default.er.component.filter.username=0.5
default.er.component.filter.gender=0.5
default.er.component.filter.image=0.5
#============= Entity Resolution Filter Config =============#

#======TPAudit Stats========================#
aws.tp.stats.bucket="thirdparty-stats-dev-************-us-east-1"
#=======TPAudit Stats=======================#


#============= Bulkrun Config ===================
staticurl.super.admin="https://dev-superadmin-vpc-dashboard.socure.com.s3.amazonaws.com"
isexternal.app="true"

#============= Neustar ===================
neustar.username="socureprod"
neustar.password="""ENC(yxIF4DHY6rRMcc9GWDvxS+uo3RLueRxyzwpdThTGLlUKni/lPUvcYQ==)"""
neustar.serviceid=9215401837
neustar.url="https://webgwy.targusinfo.com/access/query"

#============= Neustar Alternate Key ===================
neustar.username_alternate="socureRTprod"
neustar.password_alternate="""ENC(pt6EbrIIX0aoiUKhMj9Xdf+492X1kjvc0b1+TM24d3utnZnusyJ4Cg==)"""
neustar.serviceid_alternate=6044001733
neustar.url_alternate="https://webgwy.targusinfo.com/access/query"

#============= Whitepages ===================
whitepages.url="https://api.ekata.com/3.0/identity_check.json"
whitepages.key="""ENC(xV3hhizBlucPLCOZGHSGiNq7P6CGjd/7iizV1b4JLba41AP4L4lpqgJCs0mThfUw0BMchMevo0aFb1MpRe4jQY9ZHW8D6gk=)"""

#============= Whitepages Alternate Key ===================
whitepages.url_alternate="https://api.ekata.com/3.0/identity_check.json"
whitepages.key_alternate="""ENC(GhCcMjAkrzxNt5BTCAdA5Ym/2KzQ7mEjek1MjMDRs8fQBQZ+awBvABwpSFyt06wGZoflZAvyDAaxyAzVnuqtOaMwtNiAzVI=)"""

#====fullcontact data plugin=====
fullcontact.domain="https://api.fullcontact.com"
fullcontact.timeout=3000
fullcontact.fields.apiKey="""ENC(NeUV7E30eSfZb+9mMq5OZasLT1lIOfXCN+oXYl5TfnvWEF5gPiuuH3rh9p81UONU)"""
fullcontact.fields.webhookUrl=""""https://dev-vpc.socure.com/""""

fullcontactv3.domain="https://api.fullcontact.com"
fullcontactv3.timeout=3000
fullcontactv3.fields.apiKey="zetnP6AN3GOd5OdY4VieWlfTt2Vy9HXa"
fullcontactv3.fields.webhookUrl=""""https://dev-vpc.socure.com/""""

#============= Perceive Plugin=============
perceive.getscoreUri="/percieve/erscore"
perceive.host="https://perceivedev.socure.com"
perceive.histogramUri="/perceive/computehistogram"
perceive.imageUri="/perceive/image"
perceive.terminateUri="/perceive/terminate"

# Individual Component Timeouts - Default SLA

default.sla.worker.EMAIL_VAL.timeout=5000
default.sla.worker.BMEVL.timeout=5000
default.sla.worker.FORM_VAL.timeout=5000
default.sla.worker.FB_VAL.timeout=5000
default.sla.worker.PIPL_EMAIL_VAL.timeout=5000
default.sla.worker.PIPL_ADDRESS_VAL.timeout=5000
default.sla.worker.GPLUS_VAL.timeout=5000
default.sla.worker.PIPL_EMAIL_VAL_NOT_SPONS.timeout=5000
default.sla.worker.PIPL_ADDRESS_VAL_NOT_SPONS.timeout=5000
default.sla.worker.FB_VAL_ID.timeout=5000
default.sla.worker.TW_VAL.timeout=5000
default.sla.worker.TW_VAL_ID.timeout=5000
default.sla.worker.LN_VAL.timeout=5000
default.sla.worker.LN_VAL_ID.timeout=5000
default.sla.worker.TOWER_DATA_VAL.timeout=5000
default.sla.worker.FULL_CONTACT_VAL.timeout=5000
default.sla.worker.FCVAL_V3.timeout=5000
default.sla.worker.FULL_CONTACT_VAL_PHONE.timeout=5000
default.sla.worker.PP_VAL.timeout=5000
default.sla.worker.FB_VAL_DB.timeout=1000
default.sla.worker.EMAIL_FORM_VAL.timeout=5000
default.sla.worker.WLVAL.timeout=5000
default.sla.worker.COMPLY_WLVAL.timeout=5000
default.sla.worker.InternalWatchlistPrivate.timeout=5000
default.sla.worker.InternalEntityMonitoring.timeout=5000
default.sla.worker.SSVAL.timeout=5000
default.sla.worker.KYC_VAL.timeout=5000
default.sla.worker.ACVAL.timeout=5000
default.sla.worker.IMVAL.timeout=5000
default.sla.worker.LIANL.timeout=5000
default.sla.worker.PYVAL.timeout=5000
default.sla.worker.PNANL.timeout=5000
default.sla.worker.XNANL.timeout=5000
default.sla.worker.PB_VAL.timeout=5000
default.sla.worker.WPVAL.timeout=5000
default.sla.worker.WPIVL.timeout=5000
default.sla.worker.NSVAL.timeout=5000
default.sla.worker.VDANL.timeout=5000
default.sla.worker.LXVAL.timeout=5000
default.sla.worker.INANL.timeout=5000
default.sla.worker.PCVAL.timeout=5000
default.sla.worker.FVANL.timeout=5000
default.sla.worker.PNVAL.timeout=4000
default.sla.worker.FBANL.timeout=4000
default.sla.worker.TWANL.timeout=4000
default.sla.worker.GPANL.timeout=4000
default.sla.worker.AUVAL.timeout=4000
default.sla.worker.IFVAL.timeout=4000
default.sla.worker.HDVAL.timeout=4000
default.sla.worker.BLVAL.timeout=1000
default.sla.worker.TIVAL.timeout=4000
default.sla.worker.IGVAL.timeout=4000
default.sla.worker.TSVAL.timeout=4000
default.sla.worker.IDVVL.timeout=4000
default.sla.worker.MLAVL.timeout=4000
default.sla.worker.TSSVL.timeout=4000
default.sla.worker.ALVAL.timeout=4000
default.sla.worker.TDVAL.timeout=4000
default.sla.worker.AIVAL.timeout=4000
default.sla.worker.FALVL.timeout=4000
default.sla.worker.CVVAL.timeout=4000
default.sla.worker.PFIVL.timeout=4000
default.sla.worker.PFVVL.timeout=4000
default.sla.worker.DFPVL.timeout=1800
default.sla.worker.VEIVL.timeout=1000
default.sla.worker.EXVAL.timeout=4000
default.sla.worker.EXXVL.timeout=4000
default.sla.worker.VRVAL.timeout=4000
default.sla.worker.SMSVL.timeout=1000
default.sla.worker.EXPVL.timeout=4000
default.sla.worker.EXAVL.timeout=4000
default.sla.worker.EXSVL.timeout=4000
default.sla.worker.EXEVL.timeout=4000
default.sla.worker.SSEVL.timeout=4000
default.sla.worker.TDEVL.timeout=4000
default.sla.worker.IFEVL.timeout=4000
default.sla.worker.IFAVL.timeout=4000
default.sla.worker.IFPVL.timeout=4000
default.sla.worker.IFIVL.timeout=4000
default.sla.worker.DEVAL.timeout=1000
default.sla.worker.ECBSV.timeout=1000
default.sla.worker.PRVAL.timeout=4000
default.sla.worker.RCSVC.timeout=4000
default.sla.worker.FPSVC_FRAUD.timeout=4000
default.sla.worker.IDRND.timeout=4000
default.sla.worker.SECVL.timeout=4000
default.sla.worker.SRVVL.timeout=4000
default.sla.worker.FCEVL.timeout=4000
default.sla.worker.FCEVL_SOCIAL_PROFILES.timeout=4000
default.sla.worker.NSRVL.timeout=4000
default.sla.worker.ASAVL.timeout=4000
default.sla.worker.NSIVL.timeout=4000
default.sla.worker.NSGVL.timeout=4000
default.sla.worker.ATTMVL.timeout=4000
default.sla.worker.ENFORMION.timeout=4000
default.sla.worker.VCAVL.timeout=4000
default.sla.worker.VNAVL.timeout=4000
default.sla.worker.VCEVL.timeout=4000
default.sla.worker.VNEVL.timeout=4000
default.sla.worker.VCPVL.timeout=4000
default.sla.worker.VNPVL.timeout=4000
default.sla.worker.VCIVL.timeout=4000
default.sla.worker.VNIVL.timeout=4000
default.sla.worker.VCSVL.timeout=4000
default.sla.worker.VCRVL.timeout=4000
default.sla.worker.VNRVL.timeout=4000
default.sla.worker.AID6VAL.timeout=4000
default.sla.worker.BBVAL.timeout=4000
default.sla.worker.CVSVC.timeout=4000
default.sla.worker.AISVC.timeout=4000
default.sla.worker.BNYVL.timeout=4000
default.sla.worker.MBTVL.timeout=4000
default.sla.worker.TRICE.timeout=4000
default.sla.worker.EXNVL.timeout=4000
default.sla.worker.SYVAL.timeout=4000
default.sla.worker.ENPVL.timeout=4000
default.sla.worker.VCDVL.timeout=4000
default.sla.worker.VAIVL.timeout=4000
default.sla.worker.SOCVL.timeout=1000
default.sla.worker.CVASVC.timeout=1000
default.sla.worker.VACAVL.timeout=4000
default.sla.worker.VANAVL.timeout=4000
default.sla.worker.VACEVL.timeout=4000
default.sla.worker.VANEVL.timeout=4000
default.sla.worker.VACPVL.timeout=4000
default.sla.worker.VANPVL.timeout=4000
default.sla.worker.VACIVL.timeout=4000
default.sla.worker.VANIVL.timeout=4000
default.sla.worker.VACSVL.timeout=4000
default.sla.worker.VACDVL.timeout=4000
default.sla.worker.VAAIVL.timeout=4000
default.sla.worker.LSNVL.timeout=4000
default.sla.worker.GA_SVC.timeout=4000
default.sla.worker.MELVL.timeout=4000
default.sla.worker.MDVAL.timeout=4000
default.sla.worker.KYCSVC.timeout=4000
default.sla.worker.DZVAL.timeout=4000
default.sla.worker.FPFVL.timeout=4000
default.sla.worker.NPLVL.timeout=4000
default.sla.worker.FAVAL.timeout=4000
default.sla.worker.CGVAL.timeout=4000
default.sla.worker.ARVAL.timeout=4000
default.sla.worker.EPVAL.timeout=4000
default.sla.worker.S4SVC.timeout=4000
default.sla.worker.OTWVL.timeout=4000
default.sla.worker.SCPVL.timeout=4000
default.sla.worker.TWDVL.timeout=4000
default.sla.worker.ENSVL.timeout=4000
default.sla.worker.EFXUK.timeout=4000
default.sla.worker.EDMVL.timeout=4000
default.sla.worker.TWIVL.timeout=4000
default.sla.worker.CONVL.timeout=4000
default.sla.worker.MNIVL.timeout=4000
default.sla.worker.RCSVC_INT.timeout=4000
default.sla.worker.IMIVL.timeout=4000

# Individual Component Timeouts - High Performance SLA
highperformace.sla.worker.FCVAL_V3.timeout=2200
highperformace.sla.worker.BMEVL.timeout=2200
highperformace.sla.worker.MLAVL.timeout=2200
highperformace.sla.worker.IDVVL.timeout=2200
highperformace.sla.worker.EMAIL_VAL.timeout=2200
highperformace.sla.worker.FORM_VAL.timeout=2200
highperformace.sla.worker.FB_VAL.timeout=2200
highperformace.sla.worker.PIPL_EMAIL_VAL.timeout=2200
highperformace.sla.worker.PIPL_ADDRESS_VAL.timeout=2200
highperformace.sla.worker.GPLUS_VAL.timeout=2200
highperformace.sla.worker.PIPL_EMAIL_VAL_NOT_SPONS.timeout=2200
highperformace.sla.worker.PIPL_ADDRESS_VAL_NOT_SPONS.timeout=2200
highperformace.sla.worker.FB_VAL_ID.timeout=2200
highperformace.sla.worker.TW_VAL.timeout=2200
highperformace.sla.worker.TW_VAL_ID.timeout=2200
highperformace.sla.worker.LN_VAL.timeout=2200
highperformace.sla.worker.LN_VAL_ID.timeout=2200
highperformace.sla.worker.TOWER_DATA_VAL.timeout=2200
highperformace.sla.worker.FULL_CONTACT_VAL.timeout=2200
highperformace.sla.worker.FULL_CONTACT_VAL_PHONE.timeout=2200
highperformace.sla.worker.PP_VAL.timeout=2200
highperformace.sla.worker.FB_VAL_DB.timeout=1000
highperformace.sla.worker.EMAIL_FORM_VAL.timeout=2200
highperformace.sla.worker.WLVAL.timeout=2200
highperformace.sla.worker.COMPLY_WLVAL.timeout=2200
highperformace.sla.worker.InternalWatchlistPrivate.timeout=2200
highperformace.sla.worker.InternalEntityMonitoring.timeout=2200
highperformace.sla.worker.SSVAL.timeout=2200
highperformace.sla.worker.KYC_VAL.timeout=2200
highperformace.sla.worker.ACVAL.timeout=2200
highperformace.sla.worker.IMVAL.timeout=2200
highperformace.sla.worker.LIANL.timeout=2200
highperformace.sla.worker.PYVAL.timeout=2200
highperformace.sla.worker.PNANL.timeout=2200
highperformace.sla.worker.XNANL.timeout=2200
highperformace.sla.worker.PB_VAL.timeout=2200
highperformace.sla.worker.WPVAL.timeout=2200
highperformace.sla.worker.WPIVL.timeout=2200

highperformace.sla.worker.NSVAL.timeout=2200
highperformace.sla.worker.VDANL.timeout=2200
highperformace.sla.worker.LXVAL.timeout=2200
highperformace.sla.worker.INANL.timeout=2200
highperformace.sla.worker.PCVAL.timeout=2200
highperformace.sla.worker.FVANL.timeout=2200
highperformace.sla.worker.PNVAL.timeout=2200
highperformace.sla.worker.FBANL.timeout=2200
highperformace.sla.worker.TWANL.timeout=2200
highperformace.sla.worker.GPANL.timeout=2200
highperformace.sla.worker.AUVAL.timeout=2200
highperformace.sla.worker.IFVAL.timeout=2200
highperformace.sla.worker.HDVAL.timeout=2200
highperformace.sla.worker.BLVAL.timeout=1000
highperformace.sla.worker.TIVAL.timeout=2400
highperformace.sla.worker.IGVAL.timeout=2400
highperformace.sla.worker.TSVAL.timeout=2400

highperformace.sla.worker.TSSVL.timeout=2400
highperformace.sla.worker.ALVAL.timeout=2400
highperformace.sla.worker.TDVAL.timeout=2400
highperformace.sla.worker.AIVAL.timeout=2400
highperformace.sla.worker.FALVL.timeout=2400
highperformace.sla.worker.CVVAL.timeout=2400
highperformace.sla.worker.PFIVL.timeout=2400
highperformace.sla.worker.PFVVL.timeout=2400
highperformace.sla.worker.DFPVL.timeout=1800
highperformace.sla.worker.VEIVL.timeout=1000
highperformace.sla.worker.EXVAL.timeout=2400
highperformace.sla.worker.EXXVL.timeout=2400
highperformace.sla.worker.VRVAL.timeout=2400
highperformace.sla.worker.SMSVL.timeout=1000
highperformace.sla.worker.EXPVL.timeout=2400
highperformace.sla.worker.EXAVL.timeout=2400
highperformace.sla.worker.EXSVL.timeout=2400
highperformace.sla.worker.EXEVL.timeout=2400
highperformace.sla.worker.SSEVL.timeout=2400
highperformace.sla.worker.TDEVL.timeout=2400
highperformace.sla.worker.IFEVL.timeout=2400
highperformace.sla.worker.IFAVL.timeout=2400
highperformace.sla.worker.IFPVL.timeout=2400
highperformace.sla.worker.IFIVL.timeout=2400
highperformace.sla.worker.DEVAL.timeout=1000
highperformace.sla.worker.ECBSV.timeout=1000
highperformace.sla.worker.PRVAL.timeout=2400
highperformace.sla.worker.RCSVC.timeout=2400
highperformace.sla.worker.FPSVC_FRAUD.timeout=2400
highperformace.sla.worker.IDRND.timeout=2400
highperformace.sla.worker.SECVL.timeout=2400
highperformace.sla.worker.SRVVL.timeout=2400
highperformace.sla.worker.FCEVL.timeout=2400
highperformace.sla.worker.FCEVL_SOCIAL_PROFILES.timeout=2400
highperformace.sla.worker.NSRVL.timeout=2400
highperformace.sla.worker.ASAVL.timeout=2400
highperformace.sla.worker.NSIVL.timeout=2400
highperformace.sla.worker.NSGVL.timeout=2400
highperformace.sla.worker.ATTMVL.timeout=2400
highperformace.sla.worker.ENFORMION.timeout=2400
highperformace.sla.worker.VCAVL.timeout=2400
highperformace.sla.worker.VNAVL.timeout=2400
highperformace.sla.worker.VCEVL.timeout=2400
highperformace.sla.worker.VNEVL.timeout=2400
highperformace.sla.worker.VCPVL.timeout=2400
highperformace.sla.worker.VNPVL.timeout=2400
highperformace.sla.worker.VCIVL.timeout=2400
highperformace.sla.worker.VNIVL.timeout=2400
highperformace.sla.worker.VCSVL.timeout=2400
highperformace.sla.worker.VCRVL.timeout=2400
highperformace.sla.worker.VNRVL.timeout=2400
highperformace.sla.worker.AID6VAL.timeout=2400
highperformace.sla.worker.BBVAL.timeout=2400
highperformace.sla.worker.CVSVC.timeout=2400
highperformace.sla.worker.AISVC.timeout=2400
highperformace.sla.worker.BNYVL.timeout=2400
highperformace.sla.worker.MBTVL.timeout=2400
highperformace.sla.worker.TRICE.timeout=2400
highperformace.sla.worker.EXNVL.timeout=2400
highperformace.sla.worker.SYVAL.timeout=2400
highperformace.sla.worker.ENPVL.timeout=2400
highperformace.sla.worker.VCDVL.timeout=2400
highperformace.sla.worker.VAIVL.timeout=2400
highperformace.sla.worker.SOCVL.timeout=1000
highperformace.sla.worker.CVASVC.timeout=1000
highperformace.sla.worker.CVASVC.timeout=2400
highperformace.sla.worker.VACAVL.timeout=2400
highperformace.sla.worker.VANAVL.timeout=2400
highperformace.sla.worker.VACEVL.timeout=2400
highperformace.sla.worker.VANEVL.timeout=2400
highperformace.sla.worker.VACPVL.timeout=2400
highperformace.sla.worker.VANPVL.timeout=2400
highperformace.sla.worker.VACIVL.timeout=2400
highperformace.sla.worker.VANIVL.timeout=2400
highperformace.sla.worker.VACSVL.timeout=2400
highperformace.sla.worker.VACDVL.timeout=2400
highperformace.sla.worker.VAAIVL.timeout=2400
highperformace.sla.worker.LSNVL.timeout=2400
highperformace.sla.worker.GA_SVC.timeout=2400
highperformace.sla.worker.MELVL.timeout=2400
highperformace.sla.worker.MDVAL.timeout=2400
highperformace.sla.worker.KYCSVC.timeout=2400
highperformace.sla.worker.DZVAL.timeout=2400
highperformace.sla.worker.FPFVL.timeout=2400
highperformace.sla.worker.NPLVL.timeout=2400
highperformace.sla.worker.FAVAL.timeout=2400
highperformace.sla.worker.CGVAL.timeout=4000
highperformace.sla.worker.ARVAL.timeout=4000
highperformace.sla.worker.EPVAL.timeout=4000
highperformace.sla.worker.S4SVC.timeout=4000
highperformace.sla.worker.OTWVL.timeout=2400
highperformace.sla.worker.SCPVL.timeout=2400
highperformace.sla.worker.TWDVL.timeout=2400
highperformace.sla.worker.ENSVL.timeout=2400
highperformace.sla.worker.EFXUK.timeout=2400
highperformace.sla.worker.EDMVL.timeout=2400
highperformace.sla.worker.TWIVL.timeout=2400
highperformace.sla.worker.CONVL.timeout=2400
highperformace.sla.worker.MNIVL.timeout=2400
highperformace.sla.worker.RCSVC_INT.timeout=2400
highperformace.sla.worker.IMIVL.timeout=2400


# Extreme Performance timeout configurations
extreme.performance.sla {
  api.v2.scheduler.timeout ="1200"
  api.v2.overall.timeout ="1550"
  api.system.overall.timeout=1550

  worker {
    MLAVL.timeout=1100
    BMEVL.timeout=1100
    IDVVL.timeout=1100
    FCVAL_V3.timeout=5000
    EMAIL_VAL.timeout=1100
    FORM_VAL.timeout=1100
    FB_VAL.timeout=1100
    PIPL_EMAIL_VAL.timeout=1100
    PIPL_ADDRESS_VAL.timeout=1100
    GPLUS_VAL.timeout=1100
    PIPL_EMAIL_VAL_NOT_SPONS.timeout=1100
    PIPL_ADDRESS_VAL_NOT_SPONS.timeout=1100
    FB_VAL_ID.timeout=1100
    TW_VAL.timeout=1100
    TW_VAL_ID.timeout=1100
    LN_VAL.timeout=1100
    LN_VAL_ID.timeout=1100
    TOWER_DATA_VAL.timeout=1100
    FULL_CONTACT_VAL.timeout=1100
    FULL_CONTACT_VAL_PHONE.timeout=1100
    PP_VAL.timeout=1100
    FB_VAL_DB.timeout=1100
    EMAIL_FORM_VAL.timeout=1100
    WLVAL.timeout=1100
    COMPLY_WLVAL.timeout=1100
    InternalWatchlistPrivate.timeout=1100
    InternalEntityMonitoring.timeout=1100
    SSVAL.timeout=1100
    KYC_VAL.timeout=1100
    ACVAL.timeout=1100
    IMVAL.timeout=1100
    LIANL.timeout=1100
    PYVAL.timeout=1100
    PNANL.timeout=1100
    XNANL.timeout=1100
    PB_VAL.timeout=1100
    WPVAL.timeout=1100
    WPIVL.timeout=1100
    NSVAL.timeout=1100
    VDANL.timeout=1100
    LXVAL.timeout=1100
    INANL.timeout=1100
    PCVAL.timeout=1100
    FVANL.timeout=1100
    PNVAL.timeout=1100
    FBANL.timeout=1100
    TWANL.timeout=1100
    GPANL.timeout=1100
    AUVAL.timeout=1100
    IFVAL.timeout=1100
    HDVAL.timeout=1100
    BLVAL.timeout=1100
    TIVAL.timeout=1100
    IGVAL.timeout=1100
    TSVAL.timeout=1100
    TSSVL.timeout=1100
    ALVAL.timeout=1100
    TDVAL.timeout=1100
    AIVAL.timeout=1100
    FALVL.timeout=1100
    CVVAL.timeout=1100
    PFIVL.timeout=1100
    PFVVL.timeout=1100
    DFPVL.timeout=1800
    VEIVL.timeout=1000
    EXVAL.timeout=1100
    EXXVL.timeout=1100
    VRVAL.timeout=1100
    SMSVL.timeout=1000
    EXPVL.timeout=1100
    EXAVL.timeout=1100
    EXSVL.timeout=1100
    EXEVL.timeout=1100
    SSEVL.timeout=1100
    TDEVL.timeout=1100
    IFEVL.timeout=1100
    IFAVL.timeout=1100
    IFPVL.timeout=1100
    IFIVL.timeout=1100
    DEVAL.timeout=1000
    ECBSV.timeout=1000
    PRVAL.timeout=1100
    RCSVC.timeout=1100
    FPSVC_FRAUD.timeout=1100
    IDRND.timeout=1100
    SECVL.timeout=1100
    SRVVL.timeout=1100
    FCEVL.timeout=1100
    FCEVL_SOCIAL_PROFILES.timeout=1100
    NSRVL.timeout=1100
    ASAVL.timeout=1100
    NSIVL.timeout=1100
    NSGVL.timeout=1100
    ATTMVL.timeout=1100
    ENFORMION.timeout=1100
    VCAVL.timeout=1100
    VNAVL.timeout=1100
    VCEVL.timeout=1100
    VNEVL.timeout=1100
    VCPVL.timeout=1100
    VNPVL.timeout=1100
    VCIVL.timeout=1100
    VNIVL.timeout=1100
    VCSVL.timeout=1100
    VCRVL.timeout=1100
    VNRVL.timeout=1100
    AID6VAL.timeout=1100
    BBVAL.timeout=1100
    CVSVC.timeout=1100
    AISVC.timeout=1100
    BNYVL.timeout=1100
    MBTVL.timeout=1100
    TRICE.timeout=1100
    EXNVL.timeout=1100
    SYVAL.timeout=1100
    ENPVL.timeout=1100
    VCDVL.timeout=1100
    VAIVL.timeout=1100
    SOCVL.timeout=1000
    CVASVC.timeout=1100
    VACAVL.timeout=1100
    VANAVL.timeout=1100
    VACEVL.timeout=1100
    VANEVL.timeout=1100
    VACPVL.timeout=1100
    VANPVL.timeout=1100
    VACIVL.timeout=1100
    VANIVL.timeout=1100
    VACSVL.timeout=1100
    VACDVL.timeout=1100
    VAAIVL.timeout=1100
    LSNVL.timeout=1100
    GA_SVC.timeout=1100
    MELVL.timeout=1100
    MDVAL.timeout=1100
    KYCSVC.timeout=1100
    DZVAL.timeout=1100
    EFXUK.timeout=1100
    FPFVL.timeout=1100
    NPLVL.timeout=1100
    FAVAL.timeout=1100
    CGVAL.timeout=4000
EPVAL.timeout=4000
S4SVC.timeout=4000
    ARVAL.timeout=1100
    OTWVL.timeout=1100
    SCPVL.timeout=1100
    TWDVL.timeout=1100
ENSVL.timeout=1100
    EDMVL.timeout=1100
    TWIVL.timeout=1100
    CONVL.timeout=1100
    MNIVL.timeout=1100
    RCSVC_INT.timeout=1100
    IMIVL.timeout=1100
  }

  # I don't think the following is used, but retaining, just to make sure
  fmval.timeout=2
  fcval.timeout=2
  yoval.timeout=2
  pival.timeout=2
  paval.timeout=2
  boval.tiemout=2
  lival.timeout=2
  gpval.timeout=2
  fbval.timeout=2
  twval.timeout=2
  asavl.timeout=2
  kycofacval.timeout=2
}

# Document Verification Component Timeouts -  SLA
documentverification.sla.worker.FCVAL_V3.timeout=5000
documentverification.sla.worker.BMEVL.timeout=5000
documentverification.sla.worker.EMAIL_VAL.timeout=5000
documentverification.sla.worker.FORM_VAL.timeout=5000
documentverification.sla.worker.FB_VAL.timeout=5000
documentverification.sla.worker.PIPL_EMAIL_VAL.timeout=5000
documentverification.sla.worker.PIPL_ADDRESS_VAL.timeout=5000
documentverification.sla.worker.GPLUS_VAL.timeout=5000
documentverification.sla.worker.PIPL_EMAIL_VAL_NOT_SPONS.timeout=5000
documentverification.sla.worker.PIPL_ADDRESS_VAL_NOT_SPONS.timeout=5000
documentverification.sla.worker.FB_VAL_ID.timeout=5000
documentverification.sla.worker.TW_VAL.timeout=5000
documentverification.sla.worker.TW_VAL_ID.timeout=5000
documentverification.sla.worker.LN_VAL.timeout=5000
documentverification.sla.worker.LN_VAL_ID.timeout=5000
documentverification.sla.worker.TOWER_DATA_VAL.timeout=5000
documentverification.sla.worker.FULL_CONTACT_VAL.timeout=5000
documentverification.sla.worker.FULL_CONTACT_VAL_PHONE.timeout=5000
documentverification.sla.worker.PP_VAL.timeout=5000
documentverification.sla.worker.FB_VAL_DB.timeout=2500
documentverification.sla.worker.EMAIL_FORM_VAL.timeout=5000
documentverification.sla.worker.WLVAL.timeout=5000
documentverification.sla.worker.COMPLY_WLVAL.timeout=5000
documentverification.sla.worker.InternalWatchlistPrivate.timeout=5000
documentverification.sla.worker.InternalEntityMonitoring.timeout=5000
documentverification.sla.worker.SSVAL.timeout=5000
documentverification.sla.worker.KYC_VAL.timeout=5000
documentverification.sla.worker.ACVAL.timeout=5000
documentverification.sla.worker.IMVAL.timeout=5000
documentverification.sla.worker.LIANL.timeout=5000
documentverification.sla.worker.PYVAL.timeout=5000
documentverification.sla.worker.PNANL.timeout=5000
documentverification.sla.worker.XNANL.timeout=5000
documentverification.sla.worker.PB_VAL.timeout=5000
documentverification.sla.worker.WPVAL.timeout=5000
documentverification.sla.worker.WPIVL.timeout=5000
documentverification.sla.worker.NSVAL.timeout=5000
documentverification.sla.worker.VDANL.timeout=5000
documentverification.sla.worker.LXVAL.timeout=5000
documentverification.sla.worker.INANL.timeout=5000
documentverification.sla.worker.PCVAL.timeout=5000
documentverification.sla.worker.FVANL.timeout=5000
documentverification.sla.worker.PNVAL.timeout=4000
documentverification.sla.worker.FBANL.timeout=4000
documentverification.sla.worker.TWANL.timeout=4000
documentverification.sla.worker.GPANL.timeout=4000
documentverification.sla.worker.AUVAL.timeout=45000
documentverification.sla.worker.IFVAL.timeout=4000
documentverification.sla.worker.HDVAL.timeout=4000
documentverification.sla.worker.BLVAL.timeout=1000
documentverification.sla.worker.TIVAL.timeout=4000
documentverification.sla.worker.IGVAL.timeout=4000
documentverification.sla.worker.TSVAL.timeout=4000
documentverification.sla.worker.IDVVL.timeout=4000
documentverification.sla.worker.MLAVL.timeout=4000
documentverification.sla.worker.TSSVL.timeout=4000
documentverification.sla.worker.ALVAL.timeout=4000
documentverification.sla.worker.TDVAL.timeout=4000
documentverification.sla.worker.AIVAL.timeout=60000
documentverification.sla.worker.FALVL.timeout=20000
documentverification.sla.worker.CVVAL.timeout=20000
documentverification.sla.worker.PFIVL.timeout=4000
documentverification.sla.worker.PFVVL.timeout=4000
documentverification.sla.worker.DFPVL.timeout=1800
documentverification.sla.worker.VEIVL.timeout=1000
documentverification.sla.worker.EXVAL.timeout=4000
documentverification.sla.worker.EXXVL.timeout=4000
documentverification.sla.worker.VRVAL.timeout=4000
documentverification.sla.worker.SMSVL.timeout=1000
documentverification.sla.worker.EXPVL.timeout=4000
documentverification.sla.worker.EXAVL.timeout=4000
documentverification.sla.worker.EXSVL.timeout=4000
documentverification.sla.worker.EXEVL.timeout=4000
documentverification.sla.worker.SSEVL.timeout=4000
documentverification.sla.worker.TDEVL.timeout=4000
documentverification.sla.worker.IFEVL.timeout=4000
documentverification.sla.worker.IFAVL.timeout=4000
documentverification.sla.worker.IFPVL.timeout=4000
documentverification.sla.worker.IFIVL.timeout=4000
documentverification.sla.worker.DEVAL.timeout=1000
documentverification.sla.worker.ECBSV.timeout=1000
documentverification.sla.worker.PRVAL.timeout=4000
documentverification.sla.worker.RCSVC.timeout=4000
documentverification.sla.worker.FPSVC_FRAUD.timeout=4000
documentverification.sla.worker.IDRND.timeout=4000
documentverification.sla.worker.SECVL.timeout=4000
documentverification.sla.worker.SRVVL.timeout=4000
documentverification.sla.worker.FCEVL.timeout=4000
documentverification.sla.worker.FCEVL_SOCIAL_PROFILES.timeout=4000
documentverification.sla.worker.NSRVL.timeout=4000
documentverification.sla.worker.ASAVL.timeout=4000
documentverification.sla.worker.NSIVL.timeout=4000
documentverification.sla.worker.NSGVL.timeout=4000
documentverification.sla.worker.ATTMVL.timeout=4000
documentverification.sla.worker.ENFORMION.timeout=4000
documentverification.sla.worker.VCAVL.timeout=4000
documentverification.sla.worker.VNAVL.timeout=4000
documentverification.sla.worker.VCEVL.timeout=4000
documentverification.sla.worker.VNEVL.timeout=4000
documentverification.sla.worker.VCPVL.timeout=4000
documentverification.sla.worker.VNPVL.timeout=4000
documentverification.sla.worker.VCIVL.timeout=4000
documentverification.sla.worker.VNIVL.timeout=4000
documentverification.sla.worker.VCSVL.timeout=4000
documentverification.sla.worker.VCRVL.timeout=4000
documentverification.sla.worker.VNRVL.timeout=4000
documentverification.sla.worker.AID6VAL.timeout=60000
documentverification.sla.worker.BBVAL.timeout=60000
documentverification.sla.worker.CVSVC.timeout=4000
documentverification.sla.worker.AISVC.timeout=4000
documentverification.sla.worker.BNYVL.timeout=4000
documentverification.sla.worker.MBTVL.timeout=4000
documentverification.sla.worker.TRICE.timeout=4000
documentverification.sla.worker.EXNVL.timeout=4000
documentverification.sla.worker.SYVAL.timeout=4000
documentverification.sla.worker.ENPVL.timeout=4000
documentverification.sla.worker.VCDVL.timeout=4000
documentverification.sla.worker.VAIVL.timeout=4000
documentverification.sla.worker.SOCVL.timeout=1000
documentverification.sla.worker.CVASVC.timeout=4000
documentverification.sla.worker.VACAVL.timeout=4000
documentverification.sla.worker.VANAVL.timeout=4000
documentverification.sla.worker.VACEVL.timeout=4000
documentverification.sla.worker.VANEVL.timeout=4000
documentverification.sla.worker.VACPVL.timeout=4000
documentverification.sla.worker.VANPVL.timeout=4000
documentverification.sla.worker.VACIVL.timeout=4000
documentverification.sla.worker.VANIVL.timeout=4000
documentverification.sla.worker.VACSVL.timeout=4000
documentverification.sla.worker.VACDVL.timeout=4000
documentverification.sla.worker.VAAIVL.timeout=4000
documentverification.sla.worker.LSNVL.timeout=4000
documentverification.sla.worker.GA_SVC.timeout=4000
documentverification.sla.worker.MELVL.timeout=4000
documentverification.sla.worker.MDVAL.timeout=4000
documentverification.sla.worker.KYCSVC.timeout=30000
documentverification.sla.worker.DZVAL.timeout=4000
documentverification.sla.worker.FPFVL.timeout=4000
documentverification.sla.worker.NPLVL.timeout=4000
documentverification.sla.worker.FAVAL.timeout=4000
documentverification.sla.worker.CGVAL.timeout=4000
documentverification.sla.worker.ARVAL.timeout=4000
documentverification.sla.worker.EPVAL.timeout=4000
documentverification.sla.worker.S4SVC.timeout=4000
documentverification.sla.worker.OTWVL.timeout=4000
documentverification.sla.worker.SCPVL.timeout=4000
documentverification.sla.worker.TWDVL.timeout=4000
documentverification.sla.worker.ENSVL.timeout=4000
documentverification.sla.worker.EFXUK.timeout=4000
documentverification.sla.worker.EDMVL.timeout=4000
documentverification.sla.worker.TWIVL.timeout=4000
documentverification.sla.worker.CONVL.timeout=4000
documentverification.sla.worker.MNIVL.timeout=4000
documentverification.sla.worker.RCSVC_INT.timeout=4000
documentverification.sla.worker.IMIVL.timeout=4000

fv.calc.nameaddremail.codes=""""COVAL.400013,COVAL.400016""""
fv.calc.nameaddrphone.codes=""""COVAL.400010,COVAL.400016""""
fv.calc.nameaddrphoneemail.codes=""""COVAL.400010,COVAL.400013,COVAL.400016""""
fv.calc.namephoneemail.codes=""""COVAL.400010,COVAL.400013""""

tracer.serviceName="IdPlus"
tracer.endpoint="https://zipkin-dev-2020.us-east-vpc.socure.be"
tracer.enabled="false"
tracer.sample.always="false"
tracer.sample.rate=1.0
tracer.serverSpanStorage.ttl=20000

account.service {
  url="https://account-service.webapps.us-east-1.product-dev.socure.link"
  endpoint="https://account-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://account-service.webapps.us-east-1.product-dev.socure.link"
  groupName = "UXServicesRamp"
  flagName = "AccountService_Ramp"

  hmac {
    secret.key="""ENC(0N9PCWY230VslliWSziS4RcTzpom0UZiGhVwmgtyOjuBs+zeOJ5ujpBARRaJtKyGMtoDNe0EBKnXnJH/jGj8Xw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  payload.encryption.cache {
    max.size=500
    expiry.ttl="5 minutes"
    refresh.ttl="5 minutes"
    memcached.ttl="10 minutes"
  }

  dynamic.control.center {
    s3 {
      bucketName = "globalconfig-************-us-east-1"
    }
    memcached {
      host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
      port=11211
      ttl=86400
    }
    local {
      cache.timeout.minutes=2
    }
  }

}
#TTL in millisec - set to 1 minute
accountService.cache.enabled="true"
accountService.cache.ttl=60000
accountService.cache.fetchaccinfo.timeout.ms=200

#================= Model Config for IDPLUS version V3 #=================
#Legacy Fraud(V2)
datasci.model.defaults.v_3_0_0.fraud.url="http://h2o-predictor-service/predictors/predict/generic_us_30_woHD_gbm_20180502"
datasci.model.defaults.v_3_0_0.fraud.name="Fraud V3"
datasci.model.defaults.v_3_0_0.fraud.version="3.0"
datasci.model.defaults.v_3_0_0.fraud.identifier="Fraud_SigmaAll_XGB_DEV_EXA_EXE_EXP_EXS_EXX_FCE_FMV_NSG_NSI_NSR_SMS_SSE_TDE_VCA_VCE_VCI_VCP_VCS_VNA_VNE_VNP_20220311"
#This is left as empty json string because it's parameters need to be defined and as of now, all rules and reasons are passed
datasci.model.defaults.v_3_0_0.fraud.params="""{"NSGVL.100030":null,"EXPVL.300017":null,"DEVAL.100058":null,"VCPVL.220812":null,"VCSVL.220210":null,"FCEVL.100053":null,"VCIVL.230812":null,"NSRVL.100007":null,"VCEVL.210310":null,"NSGVL.100017":null,"DEVAL.100022":null,"NSIVL.100033":null,"VCPVL.240099":null,"SSEVL.100014":null,"EXEVL.100025":null,"NSRVL.300008":null,"NSRVL.100005":null,"TDEVL.100011":null,"TDEVL.200008":null,"VCEVL.210212":null,"NSIVL.100001":null,"EXSVL.200035":null,"VCIVL.220906":null,"NSRVL.200028":null,"FCEVL.100047":null,"VCPVL.220407":null,"VCIVL.230409":null,"EXPVL.200042":null,"EXPVL.200029":null,"EXPVL.300014":null,"NSRVL.100008":null,"FCEVL.100004":null,"DEVAL.100027":null,"DEVAL.100029":null,"FMVAL.600031":null,"EXPVL.200045":null,"FCEVL.100046":null,"NSGVL.100021":null,"EXEVL.100000":null,"VCPVL.220607":null,"VCPVL.210610":null,"VCPVL.250099":null,"VCAVL.220602":null,"TDEVL.100025":null,"TDEVL.100039":null,"NSRVL.100002":null,"FCEVL.100036":null,"VCEVL.200003":null,"VCIVL.250099":null,"EXPVL.200024":null,"FCEVL.100007":null,"NSGVL.100012":null,"EXSVL.200036":null,"VNEVL.210307":null,"NSRVL.100009":null,"SSEVL.100001":null,"EXEVL.100006":null,"VCAVL.220409":null,"EXEVL.100014":null,"SSEVL.100033":null,"VCPVL.220810":null,"VCEVL.220710":null,"VCPVL.220809":null,"VCEVL.220610":null,"VCIVL.220810":null,"VNEVL.210612":null,"EXEVL.100017":null,"DEVAL.100046":null,"NSIVL.100004":null,"FCEVL.100017":null,"TDEVL.100002":null,"EXEVL.100029":null,"EXPVL.200007":null,"NSRVL.100004":null,"EXSVL.200022":null,"VCEVL.200007":null,"SSEVL.100030":null,"VNPVL.200008":null,"FMVAL.900019":null,"EXAVL.100005":null,"FCEVL.100049":null,"VCEVL.200012":null,"VCPVL.220409":null,"EXSVL.300020":null,"DEVAL.100026":null,"TDEVL.500001":null,"EXEVL.100031":null,"TDEVL.100007":null,"EXEVL.100027":null,"NSGVL.100024":null,"VCIVL.220907":null,"VCAVL.220410":null,"SSEVL.100002":null,"EXPVL.100007":null,"DEVAL.100048":null,"EXSVL.200037":null,"VCAVL.200005":null,"FCEVL.100008":null,"NSRVL.200018":null,"NSRVL.200032":null,"DEVAL.100051":null,"NSRVL.200014":null,"VCEVL.220910":null,"VNPVL.200009":null,"VCEVL.220112":null,"EXEVL.100004":null,"FCEVL.100019":null,"EXPVL.200016":null,"FCEVL.100002":null,"NSRVL.100003":null,"VNAVL.200007":null,"VCAVL.200009":null,"VCEVL.240099":null,"EXEVL.100011":null,"FCEVL.100048":null,"EXPVL.100002":null,"EXAVL.100002":null,"SSEVL.100010":null,"EXPVL.300010":null,"VCPVL.200006":null,"TDEVL.200007":null,"NSRVL.200011":null,"VCPVL.220712":null,"TDEVL.100008":null,"EXEVL.100012":null,"DEVAL.100049":null,"VCPVL.220710":null,"FMVAL.300045":null,"VCIVL.200012":null,"VCIVL.220707":null,"EXPVL.200044":null,"TDEVL.100035":null,"EXPVL.200037":null,"VCAVL.200010":null,"VCEVL.220909":null,"VCPVL.220609":null,"VNPVL.210310":null,"VCAVL.200012":null,"EXPVL.300007":null,"VCEVL.220812":null,"EXPVL.100005":null,"EXSVL.200042":null,"EXPVL.300013":null,"NSIVL.100005":null,"VCPVL.220907":null,"VCPVL.200012":null,"SSEVL.100006":null,"VCEVL.220612":null,"NSGVL.100022":null,"FCEVL.100006":null,"EXEVL.100002":null,"SMSVL.100046":null,"EXPVL.200025":null,"TDEVL.100038":null,"SSEVL.100034":null,"NSIVL.100061":null,"FCEVL.100059":null,"EXPVL.300001":null,"DEVAL.100055":null,"EXPVL.300009":null,"EXSVL.200043":null,"DEVAL.100047":null,"DEVAL.100056":null,"EXPVL.200015":null,"VCPVL.200007":null,"VCIVL.230109":null,"EXPVL.200009":null,"NSIVL.100026":null,"VCEVL.220907":null,"VCEVL.200009":null,"VCSVL.220309":null,"EXPVL.200010":null,"NSRVL.300001":null,"TDEVL.100013":null,"NSGVL.100018":null,"VCIVL.230612":null,"VCPVL.220109":null,"NSRVL.200022":null,"EXEVL.100028":null,"SSEVL.100031":null,"TDEVL.100004":null,"VCPVL.210609":null,"DEVAL.100024":null,"VCEVL.250099":null,"VCEVL.200006":null,"EXSVL.300001":null,"FCEVL.100056":null,"EXEVL.100037":null,"EXPVL.200005":null,"TDEVL.100006":null,"NSRVL.100010":null,"VCEVL.200010":null,"VNAVL.210612":null,"VCAVL.250099":null,"VCSVL.200012":null,"FMVAL.900005":null,"VCAVL.200007":null,"VCIVL.220909":null,"EXEVL.100036":null,"VCPVL.200005":null,"VCEVL.220310":null,"FCEVL.100034":null,"FCEVL.100060":null,"FMVAL.300002":null,"FCEVL.100068":null,"VCEVL.230112":null,"VCPVL.200010":null,"VCEVL.230212":null,"EXXVL.100205":null,"EXPVL.200008":null,"NSRVL.100006":null,"VCPVL.220709":null,"NSRVL.200010":null,"VCSVL.250099":null,"NSRVL.300004":null,"SMSVL.100043":null,"EXPVL.200036":null,"VCSVL.240099":null,"EXPVL.200023":null,"VCIVL.221009":null,"VCEVL.220712":null,"EXPVL.200001":null,"NSRVL.300005":null,"EXSVL.200027":null,"FCEVL.100063":null,"TDEVL.100001":null}"""
datasci.model.defaults.v_3_0_0.fraud.model_type="h2o"
datasci.model.defaults.v_3_0_0.fraud.model_format="mojo"
datasci.model.defaults.v_3_0_0.fraud.quantile_mapping="""
quantile,score
0.999,0.97847443819046
0.998,0.971745312213897
0.997,0.965814709663391
0.996,0.960439682006836
0.995,0.954270780086517
0.994,0.950079560279846
0.993,0.944045722484588
0.992,0.938908874988556
0.991,0.935216963291168
0.99,0.930653393268585
0.989,0.925942420959472
0.988,0.921341717243194
0.987,0.916829228401184
0.986,0.912428140640258
0.985,0.906385719776153
0.984,0.902432382106781
0.983,0.898655951023101
0.982,0.894191563129425
0.981,0.890186727046966
0.98,0.885906815528869
0.979,0.882175266742706
0.978,0.878580391407012
0.977,0.874832034111023
0.976,0.872199475765228
0.975,0.868728041648864
0.974,0.863736033439636
0.973,0.859375894069671
0.972,0.855317294597625
0.971,0.851404309272766
0.97,0.84744507074356
0.969,0.843867063522338
0.968,0.839986205101013
0.967,0.836043059825897
0.966,0.832256197929382
0.965,0.828505456447601
0.964,0.824593663215637
0.963,0.820570290088653
0.962,0.81699138879776
0.961,0.812300860881805
0.96,0.808302342891693
0.959,0.8030646443367
0.958,0.79938119649887
0.957,0.795254707336425
0.956,0.791071236133575
0.955,0.787864089012146
0.954,0.78406947851181
0.953,0.779949724674224
0.952,0.775849997997283
0.951,0.772625923156738
0.95,0.769415438175201
0.949,0.766324341297149
0.948,0.763644933700561
0.947,0.76018750667572
0.946,0.757071256637573
0.945,0.753467917442321
0.944,0.750514328479766
0.943,0.747146070003509
0.942,0.744145929813385
0.941,0.741367161273956
0.94,0.7375990152359
0.939,0.734589993953704
0.938,0.731934785842895
0.937,0.729055821895599
0.936,0.72640174627304
0.935,0.723303198814392
0.934,0.719708442687988
0.933,0.716484308242797
0.932,0.712928116321563
0.931,0.709486484527587
0.93,0.706896841526031
0.929,0.70314222574234
0.928,0.699323415756225
0.927,0.695648908615112
0.926,0.692938387393951
0.925,0.690487444400787
0.924,0.687258064746856
0.923,0.683373034000396
0.922,0.68015432357788
0.921,0.677939653396606
0.92,0.674309074878692
0.919,0.670363187789917
0.918,0.666810512542724
0.917,0.663655400276184
0.916,0.660841047763824
0.915,0.657465100288391
0.914,0.654687941074371
0.913,0.650570273399353
0.912,0.647232353687286
0.911,0.644077360630035
0.91,0.640835583209991
0.909,0.638992726802825
0.908,0.635465562343597
0.907,0.632687091827392
0.906,0.630262732505798
0.905,0.626914978027343
0.904,0.623533904552459
0.903,0.62075388431549
0.902,0.618037641048431
0.901,0.614990055561065
0.9,0.611868560314178
0.899,0.608356177806854
0.898,0.604584515094757
0.897,0.601321518421173
0.896,0.597765326499939
0.895,0.594600737094879
0.894,0.591772317886352
0.893,0.589059591293335
0.892,0.586576402187347
0.891,0.583312034606933
0.89,0.581107795238494
0.889,0.578407227993011
0.888,0.575287282466888
0.887,0.57273519039154
0.886,0.570090234279632
0.885,0.567652523517608
0.884,0.564601242542266
0.883,0.562313199043273
0.882,0.559324026107788
0.881,0.556210815906524
0.88,0.553777813911438
0.879,0.551380455493927
0.878,0.548835575580596
0.877,0.545691430568695
0.876,0.543019592761993
0.875,0.540562570095062
0.874,0.537265658378601
0.873,0.534075379371643
0.872,0.531772792339325
0.871,0.528424024581909
0.87,0.526186943054199
0.869,0.523183286190033
0.868,0.520591795444488
0.867,0.517983078956604
0.866,0.514571666717529
0.865,0.511753439903259
0.864,0.509323835372924
0.863,0.506715834140777
0.862,0.50354117155075
0.861,0.501348733901977
0.86,0.498787730932235
0.859,0.496263980865478
0.858,0.493333429098129
0.857,0.490947514772415
0.856,0.48868703842163
0.855,0.485859096050262
0.854,0.482936769723892
0.853,0.480639785528183
0.852,0.477965384721756
0.851,0.475662440061569
0.85,0.472751885652542
0.849,0.469600766897201
0.848,0.46730899810791
0.847,0.464871287345886
0.846,0.462468713521957
0.845,0.459381431341171
0.844,0.457485109567642
0.843,0.454893350601196
0.842,0.452476292848587
0.841,0.450244605541229
0.84,0.447923451662063
0.839,0.445921063423156
0.838,0.443795382976532
0.837,0.441713601350784
0.836,0.439405918121337
0.835,0.436697959899902
0.834,0.433711349964141
0.833,0.431552976369857
0.832,0.428958147764205
0.831,0.426952332258224
0.83,0.425267308950424
0.829,0.423179984092712
0.828,0.42107143998146
0.827,0.418365895748138
0.826,0.416273534297943
0.825,0.414708286523818
0.824,0.412986814975738
0.823,0.41113057732582
0.822,0.40898197889328
0.821,0.406960666179657
0.82,0.404924392700195
0.819,0.402692824602127
0.818,0.400338768959045
0.817,0.397878438234329
0.816,0.395889610052108
0.815,0.393797665834426
0.814,0.3925341963768
0.813,0.390474587678909
0.812,0.388715177774429
0.811,0.386867254972457
0.81,0.384919941425323
0.809,0.383134186267852
0.808,0.381174087524414
0.807,0.379042714834213
0.806,0.377112418413162
0.805,0.375437051057815
0.804,0.373514175415039
0.803,0.371179640293121
0.802,0.369095504283905
0.801,0.367676794528961
0.8,0.366015464067459
0.799,0.364161521196365
0.798,0.362584501504898
0.797,0.361038565635681
0.796,0.358815163373947
0.795,0.357157200574874
0.794,0.355842053890228
0.793,0.353868722915649
0.792,0.352073222398757
0.791,0.350079089403152
0.79,0.348107904195785
0.789,0.346192061901092
0.788,0.344671934843063
0.787,0.343090027570724
0.786,0.341278642416
0.785,0.339652389287948
0.784,0.338432639837265
0.783,0.336814790964126
0.782,0.335542917251586
0.781,0.333979904651641
0.78,0.332597464323043
0.779,0.331269055604934
0.778,0.329567521810531
0.777,0.328173577785491
0.776,0.326488763093948
0.775,0.325158506631851
0.774,0.323793679475784
0.773,0.322898775339126
0.772,0.320875704288482
0.771,0.31897023320198
0.77,0.317773401737213
0.769,0.316441357135772
0.768,0.314858168363571
0.767,0.313407838344574
0.766,0.312248617410659
0.765,0.310779750347137
0.764,0.309608042240142
0.763,0.308220624923706
0.762,0.306564271450042
0.761,0.304979920387268
0.76,0.303548216819763
0.759,0.30226731300354
0.758,0.300798445940017
0.757,0.299306184053421
0.756,0.298058986663818
0.755,0.296859562397003
0.754,0.295485913753509
0.753,0.294040381908416
0.752,0.292682379484176
0.751,0.291246384382247
0.75,0.290153503417968
0.749,0.289091199636459
0.748,0.287590980529785
0.747,0.286517947912216
0.746,0.284918129444122
0.745,0.283515572547912
0.744,0.282181441783905
0.743,0.281165808439254
0.742,0.280020624399185
0.741,0.279192566871643
0.74,0.277875751256942
0.739,0.276473879814147
0.738,0.275535643100738
0.737,0.27446636557579
0.736,0.273227483034133
0.735,0.271908104419708
0.734,0.27094566822052
0.733,0.269573837518692
0.732,0.268289327621459
0.731,0.266798675060272
0.73,0.265823394060134
0.729,0.264413774013519
0.728,0.263504087924957
0.727,0.262809097766876
0.726,0.261851698160171
0.725,0.2608562707901
0.724,0.259750396013259
0.723,0.258673936128616
0.722,0.25758546590805
0.721,0.256366252899169
0.72,0.255137503147125
0.719,0.25399625301361
0.718,0.252953320741653
0.717,0.251751333475112
0.716,0.250629782676696
0.715,0.250215113162994
0.714,0.249992400407791
0.713,0.248895481228828
0.712,0.247797921299934
0.711,0.246849372982978
0.71,0.24596220254898
0.709,0.244891971349716
0.708,0.243931978940963
0.707,0.242683231830596
0.706,0.24163968861103
0.705,0.240935534238815
0.704,0.239998340606689
0.703,0.238993257284164
0.702,0.238051891326904
0.701,0.236994743347167
0.7,0.235809057950973
0.699,0.234879851341247
0.698,0.234017387032508
0.697,0.232999593019485
0.696,0.231968566775321
0.695,0.230909809470176
0.694,0.229899480938911
0.693,0.229417309165
0.692,0.228399410843849
0.691,0.227497845888137
0.69,0.22662878036499
0.689,0.225570753216743
0.688,0.224713906645774
0.687,0.224117919802665
0.686,0.223471000790596
0.685,0.222598254680633
0.684,0.221827328205108
0.683,0.220844760537147
0.682,0.219860225915908
0.681,0.218978807330131
0.68,0.218136131763458
0.679,0.217232659459114
0.678,0.216258496046066
0.677,0.21562048792839
0.676,0.214736208319664
0.675,0.213820070028305
0.674,0.212861239910125
0.673,0.211853086948394
0.672,0.211247399449348
0.671,0.210318639874458
0.67,0.209433630108833
0.669,0.208800315856933
0.668,0.208245769143104
0.667,0.207594826817512
0.666,0.206950828433036
0.665,0.206319913268089
0.664,0.205468609929084
0.663,0.204563826322555
0.662,0.203733369708061
0.661,0.203057318925857
0.66,0.202222526073455
0.659,0.201415583491325
0.658,0.200765818357467
0.657,0.199846193194389
0.656,0.199237406253814
0.655,0.198314189910888
0.654,0.197667181491851
0.653,0.196961909532547
0.652,0.196032389998435
0.651,0.195167437195777
0.65,0.19447274506092
0.649,0.193934351205825
0.648,0.192995443940162
0.647,0.192398220300674
0.646,0.191641598939895
0.645,0.190948456525802
0.644,0.190199166536331
0.643,0.189466446638107
0.642,0.188651874661445
0.641,0.187830805778503
0.64,0.187128856778144
0.639,0.186431497335433
0.638,0.18602792918682
0.637,0.185423135757446
0.636,0.184830918908119
0.635,0.184118330478668
0.634,0.183402314782142
0.633,0.1825962215662
0.632,0.182013273239135
0.631,0.18126505613327
0.63,0.180513277649879
0.629,0.179885312914848
0.628,0.179298102855682
0.627,0.178583711385726
0.626,0.177915588021278
0.625,0.177225351333618
0.624,0.176693305373191
0.623,0.176166653633117
0.622,0.175598606467247
0.621,0.174664735794067
0.62,0.174198418855667
0.619,0.173617169260978
0.618,0.173108845949172
0.617,0.172453463077545
0.616,0.171998649835586
0.615,0.171348452568054
0.614,0.170700132846832
0.613,0.170025736093521
0.612,0.169341340661048
0.611,0.168866008520126
0.61,0.168376609683036
0.609,0.167776569724082
0.608,0.16732782125473
0.607,0.166736856102943
0.606,0.166144475340843
0.605,0.165658459067344
0.604,0.165064483880996
0.603,0.164421558380126
0.602,0.163949519395828
0.601,0.163332089781761
0.6,0.162593871355056
0.599,0.162115931510925
0.598,0.16172082722187
0.597,0.161287680268287
0.596,0.160573080182075
0.595,0.160091757774353
0.594,0.159539505839347
0.593,0.159147083759307
0.592,0.158680081367492
0.591,0.157982915639877
0.59,0.157387807965278
0.589,0.156740069389343
0.588,0.156307488679885
0.587,0.155790999531745
0.586,0.15540088713169
0.585,0.154924690723419
0.584,0.154440656304359
0.583,0.154049918055534
0.582,0.153702393174171
0.581,0.153166323900222
0.58,0.152726784348487
0.579,0.152281820774078
0.578,0.151832655072212
0.577,0.151412203907966
0.576,0.150852918624877
0.575,0.150282740592956
0.574,0.149933233857154
0.573,0.149543449282646
0.572,0.149046555161476
0.571,0.148504018783569
0.57,0.148118153214454
0.569,0.147609964013099
0.568,0.147161915898323
0.567,0.146696358919143
0.566,0.146288648247718
0.565,0.145873442292213
0.564,0.145534425973892
0.563,0.145084992051124
0.562,0.144679248332977
0.561,0.144247069954872
0.56,0.143742680549621
0.559,0.143196135759353
0.558,0.142768234014511
0.557,0.142327710986137
0.556,0.141758531332016
0.555,0.141392722725868
0.554,0.140956252813339
0.553,0.140507146716117
0.552,0.14013560116291
0.551,0.139641106128692
0.55,0.139219984412193
0.549,0.138757392764091
0.548,0.138359785079956
0.547,0.137953832745552
0.546,0.137616023421287
0.545,0.137180998921394
0.544,0.136810287833213
0.543,0.136341720819473
0.542,0.136029869318008
0.541,0.13561175763607
0.54,0.135253846645355
0.539,0.134948343038558
0.538,0.134453564882278
0.537,0.133980259299278
0.536,0.133465439081192
0.535,0.133125156164169
0.534,0.132764518260955
0.533,0.132426887750625
0.532,0.131955936551094
0.531,0.131432026624679
0.53,0.131083548069
0.529,0.130711957812309
0.528,0.130366489291191
0.527,0.129940956830978
0.526,0.12954144179821
0.525,0.129142373800277
0.524,0.128794491291046
0.523,0.128363206982612
0.522,0.12797737121582
0.521,0.127519160509109
0.52,0.127051666378974
0.519,0.126677349209785
0.518,0.126197874546051
0.517,0.125870749354362
0.516,0.125380799174308
0.515,0.125061973929405
0.514,0.124732188880443
0.513,0.124269165098667
0.512,0.123825952410697
0.511,0.123361937701702
0.51,0.122929200530052
0.509,0.122540675103664
0.508,0.122242711484432
0.507,0.121898204088211
0.506,0.121564514935016
0.505,0.121218346059322
0.504,0.120834872126579
0.503,0.12053969502449
0.502,0.120301403105258
0.501,0.119828790426254
0.5,0.119414053857326
0.499,0.118997730314731
0.498,0.118665494024753
0.497,0.118286170065402
0.496,0.118022412061691
0.495,0.11767604202032
0.494,0.117267854511737
0.493,0.116905026137828
0.492,0.116635963320732
0.491,0.116259187459945
0.49,0.115953363478183
0.489,0.115698985755443
0.488,0.115325257182121
0.487,0.114987879991531
0.486,0.114699564874172
0.485,0.114392265677452
0.484,0.114089906215667
0.483,0.11378813534975
0.482,0.113545805215835
0.481,0.113257527351379
0.48,0.11304373294115
0.479,0.112775221467018
0.478,0.112464740872383
0.477,0.112147450447082
0.476,0.11185547709465
0.475,0.111586585640907
0.474,0.111141942441463
0.473,0.110818542540073
0.472,0.110469795763492
0.471,0.110182464122772
0.47,0.109859816730022
0.469,0.109518855810165
0.468,0.109132155776023
0.467,0.108874596655368
0.466,0.108642168343067
0.465,0.108402594923973
0.464,0.108035154640674
0.463,0.107790738344192
0.462,0.107560016214847
0.461,0.107276812195777
0.46,0.106926806271076
0.459,0.106667168438434
0.458,0.106403373181819
0.457,0.106004625558853
0.456,0.105649918317794
0.455,0.105424627661705
0.454,0.105129182338714
0.453,0.104756489396095
0.452,0.104457736015319
0.451,0.104196742177009
0.45,0.103866405785083
0.449,0.103637129068374
0.448,0.103287696838378
0.447,0.102959387004375
0.446,0.102763526141643
0.445,0.102440677583217
0.444,0.102134965360164
0.443,0.101871192455291
0.442,0.101644188165664
0.441,0.10130151361227
0.44,0.101035147905349
0.439,0.100766465067863
0.438,0.100557878613471
0.437,0.100352689623832
0.436,0.100074172019958
0.435,0.099809929728508
0.434,0.0994865819811821
0.433,0.0991902574896812
0.432,0.0989248007535934
0.431,0.0986595228314399
0.43,0.0983557477593422
0.429,0.0981161594390869
0.428,0.0979015678167343
0.427,0.0976119861006736
0.426,0.0973213613033294
0.425,0.097043938934803
0.424,0.096681535243988
0.423,0.096410684287548
0.422,0.0960988625884056
0.421,0.0958487018942833
0.42,0.0955444425344467
0.419,0.0953085273504257
0.418,0.0949417799711227
0.417,0.0946614220738411
0.416,0.0944513157010078
0.415,0.0941278412938118
0.414,0.0938981622457504
0.413,0.0935951769351959
0.412,0.0933089703321456
0.411,0.0930458754301071
0.41,0.0928493440151214
0.409,0.0925920605659484
0.408,0.0922827422618866
0.407,0.0920511111617088
0.406,0.0917731150984764
0.405,0.0915743485093116
0.404,0.0913489162921905
0.403,0.0911374390125274
0.402,0.0908915698528289
0.401,0.0906181633472442
0.4,0.0903869867324829
0.399,0.090134672820568
0.398,0.0898837521672248
0.397,0.0895671471953392
0.396,0.089367464184761
0.395,0.0891147404909133
0.394,0.0888006091117858
0.393,0.0885786637663841
0.392,0.0883685275912284
0.391,0.088064357638359
0.39,0.0877981185913086
0.389,0.0875773429870605
0.388,0.0873176455497741
0.387,0.0870533287525177
0.386,0.0868300572037696
0.385,0.0866064876317977
0.384,0.0863756164908409
0.383,0.0861750543117523
0.382,0.0859680995345115
0.381,0.0857058763504028
0.38,0.0854382216930389
0.379,0.0852694362401962
0.378,0.0850794464349746
0.377,0.0848165825009346
0.376,0.0844797492027282
0.375,0.0842426791787147
0.374,0.0840325206518173
0.373,0.0838419944047927
0.372,0.0836376696825027
0.371,0.0834376960992813
0.37,0.0832104012370109
0.369,0.082976296544075
0.368,0.0827497318387031
0.367,0.0825470834970474
0.366,0.0822997912764549
0.365,0.0820921286940574
0.364,0.0818523168563842
0.363,0.0816889330744743
0.362,0.0814376398921012
0.361,0.0812094509601593
0.36,0.0809713900089263
0.359,0.0807743519544601
0.358,0.0805432274937629
0.357,0.0803514569997787
0.356,0.0802172422409057
0.355,0.07997627556324
0.354,0.0797643586993217
0.353,0.0795691981911659
0.352,0.0793340355157852
0.351,0.0791080519556999
0.35,0.0789310559630394
0.349,0.0787220224738121
0.348,0.0784883722662925
0.347,0.0781805589795112
0.346,0.0779621079564094
0.345,0.0777482986450195
0.344,0.0775289759039878
0.343,0.0773408710956573
0.342,0.077117770910263
0.341,0.0768672451376915
0.34,0.0766624361276626
0.339,0.0764530748128891
0.338,0.0762771666049957
0.337,0.0760770887136459
0.336,0.0759086087346077
0.335,0.0756817013025283
0.334,0.0754313394427299
0.333,0.0752031132578849
0.332,0.0750592574477195
0.331,0.0747716575860977
0.33,0.0745342448353767
0.329,0.0743617340922355
0.328,0.0741189196705818
0.327,0.0739214047789573
0.326,0.0737044438719749
0.325,0.0734604075551033
0.324,0.0732890143990516
0.323,0.073095828294754
0.322,0.0728882551193237
0.321,0.0727107897400856
0.32,0.0725366994738578
0.319,0.0723161771893501
0.318,0.0720594525337219
0.317,0.0718859210610389
0.316,0.0717408433556556
0.315,0.0714980736374855
0.314,0.0712741389870643
0.313,0.0711294934153556
0.312,0.0709033831954002
0.311,0.0707208439707756
0.31,0.0705323740839958
0.309,0.0704065337777137
0.308,0.0701622068881988
0.307,0.0699501112103462
0.306,0.069788858294487
0.305,0.0695939809083938
0.304,0.0694384053349495
0.303,0.0691984444856643
0.302,0.0689552128314971
0.301,0.0687625557184219
0.3,0.06857281178236
0.299,0.0683726370334625
0.298,0.0681715831160545
0.297,0.0679775401949882
0.296,0.0677837803959846
0.295,0.0675428137183189
0.294,0.067375898361206
0.293,0.0671998411417007
0.292,0.0669546201825141
0.291,0.0667970031499862
0.29,0.0665652751922607
0.289,0.0663679763674736
0.288,0.0662039294838905
0.287,0.0660261139273643
0.286,0.0658619627356529
0.285,0.0656508803367614
0.284,0.0654277354478836
0.283,0.0652607902884483
0.282,0.0650535225868225
0.281,0.0648364499211311
0.28,0.0646314471960067
0.279,0.0644209682941436
0.278,0.0642310082912445
0.277,0.0639917775988578
0.276,0.0637845769524574
0.275,0.0635782480239868
0.274,0.0633603706955909
0.273,0.0632059574127197
0.272,0.0629890114068985
0.271,0.0628356486558914
0.27,0.0626711249351501
0.269,0.0624543763697147
0.268,0.0622798465192317
0.267,0.062133889645338
0.266,0.0619744807481765
0.265,0.0618040189146995
0.264,0.0615635365247726
0.263,0.0613899752497673
0.262,0.0611608996987342
0.261,0.0609481707215309
0.26,0.0607666261494159
0.259,0.060537926852703
0.258,0.0603854171931743
0.257,0.0602021217346191
0.256,0.0599901229143142
0.255,0.0598088130354881
0.254,0.0596752613782882
0.253,0.0595226436853408
0.252,0.0593667477369308
0.251,0.059216558933258
0.25,0.059047307819128
0.249,0.0588616468012332
0.248,0.0586870312690734
0.247,0.0584986805915832
0.246,0.0582945570349693
0.245,0.0580814741551876
0.244,0.0578885152935981
0.243,0.0577224940061569
0.242,0.0575704202055931
0.241,0.0574549213051795
0.24,0.0572738088667392
0.239,0.0570935979485511
0.238,0.0569323971867561
0.237,0.056771945208311
0.236,0.0566079206764698
0.235,0.0564739368855953
0.234,0.0562829673290252
0.233,0.0561212822794914
0.232,0.0559188239276409
0.231,0.0557141043245792
0.23,0.0554982870817184
0.229,0.0553208403289318
0.228,0.0551419220864772
0.227,0.0550264343619346
0.226,0.0548440217971801
0.225,0.0547031052410602
0.224,0.0545195825397968
0.223,0.0543304085731506
0.222,0.0541279464960098
0.221,0.0539634935557842
0.22,0.0538139753043651
0.219,0.0536875128746032
0.218,0.0535369776189327
0.217,0.0533483512699604
0.216,0.0532088838517665
0.215,0.0530115067958831
0.214,0.0528232790529727
0.213,0.0525721572339534
0.212,0.0524265468120574
0.211,0.0522026233375072
0.21,0.0520610362291336
0.209,0.0519045889377594
0.208,0.0517366603016853
0.207,0.0515705831348896
0.206,0.0513563193380832
0.205,0.0512333735823631
0.204,0.0511197298765182
0.203,0.0509326122701168
0.202,0.0507546439766883
0.201,0.0505578368902206
0.2,0.0503590740263462
0.199,0.0501621626317501
0.198,0.0500040017068386
0.197,0.0498391613364219
0.196,0.0496830455958843
0.195,0.049478281289339
0.194,0.0493209213018417
0.193,0.0491300635039806
0.192,0.0489830300211906
0.191,0.0488119646906852
0.19,0.0486339293420314
0.189,0.0484764464199543
0.188,0.0483524687588214
0.187,0.0482053980231285
0.186,0.0480359196662902
0.185,0.0478729084134101
0.184,0.047722227871418
0.183,0.0475204475224018
0.182,0.0474034212529659
0.181,0.0472524613142013
0.18,0.0471136048436164
0.179,0.0469165481626987
0.178,0.0467964485287666
0.177,0.0465980060398578
0.176,0.0464626140892505
0.175,0.046281773597002
0.174,0.0460874959826469
0.173,0.0458982214331626
0.172,0.045741282403469
0.171,0.0455622673034668
0.17,0.0453558750450611
0.169,0.0451575331389904
0.168,0.04501723498106
0.167,0.0448715947568416
0.166,0.0447948127985
0.165,0.0446000434458255
0.164,0.0444023087620735
0.163,0.0442365147173404
0.162,0.0440692529082298
0.161,0.0439240150153636
0.16,0.0437420643866062
0.159,0.0434899404644966
0.158,0.0433130636811256
0.157,0.043135829269886
0.156,0.0429556593298912
0.155,0.0428182780742645
0.154,0.042660791426897
0.153,0.0424828790128231
0.152,0.042290449142456
0.151,0.0421254448592662
0.15,0.0419845283031463
0.149,0.0418214797973632
0.148,0.0416888259351253
0.147,0.0415466763079166
0.146,0.0413598045706748
0.145,0.0411843210458755
0.144,0.0409775674343109
0.143,0.0408329404890537
0.142,0.0406677685678005
0.141,0.0404850505292415
0.14,0.0402735993266105
0.139,0.0400859452784061
0.138,0.0399495474994182
0.137,0.0398202650249004
0.136,0.0396158844232559
0.135,0.0394652001559734
0.134,0.0393234491348266
0.133,0.0391684211790561
0.132,0.0390079021453857
0.131,0.0387933999300003
0.13,0.0386237204074859
0.129,0.0384510084986686
0.128,0.0382864922285079
0.127,0.0381297767162323
0.126,0.0379446037113666
0.125,0.0377774536609649
0.124,0.0376096107065677
0.123,0.0374101810157299
0.122,0.0372377783060073
0.121,0.0370554104447364
0.12,0.0368800684809684
0.119,0.0367402844130992
0.118,0.036571804434061
0.117,0.0364319719374179
0.116,0.0362235009670257
0.115,0.0360506772994995
0.114,0.035931073129177
0.113,0.0357921309769153
0.112,0.035561341792345
0.111,0.0354091748595237
0.11,0.0352604053914547
0.109,0.0351253822445869
0.108,0.0349094942212104
0.107,0.034713290631771
0.106,0.0345600768923759
0.105,0.0344005674123764
0.104,0.0341973304748535
0.103,0.034006018191576
0.102,0.0338102467358112
0.101,0.0336834192276001
0.1,0.0334968343377113
0.099,0.0333187878131866
0.098,0.0331601127982139
0.097,0.0329369232058525
0.096,0.0327165946364402
0.095,0.0325088761746883
0.094,0.0323675088584423
0.093,0.0321594513952732
0.092,0.0320083796977996
0.091,0.0318422615528106
0.09,0.0316874980926513
0.089,0.0315368175506591
0.088,0.0313857719302177
0.087,0.0312468111515045
0.086,0.0311068873852491
0.085,0.0309462919831275
0.084,0.0307838208973407
0.083,0.0306214019656181
0.082,0.0304609350860118
0.081,0.0302903242409229
0.08,0.0301307141780853
0.079,0.0299656596034765
0.078,0.0297823660075664
0.077,0.0296245068311691
0.076,0.029445294290781
0.075,0.0292774830013513
0.074,0.0290841497480869
0.073,0.0289103258401155
0.072,0.028719337657094
0.071,0.0285128429532051
0.07,0.0283059887588024
0.069,0.0281143561005592
0.068,0.0279597174376249
0.067,0.0277798026800155
0.066,0.0276060868054628
0.065,0.0274153165519237
0.064,0.0273119751363992
0.063,0.0271125435829162
0.062,0.0268806964159011
0.061,0.0266905836760997
0.06,0.0265053622424602
0.059,0.0263178925961256
0.058,0.0261192601174116
0.057,0.0259504187852144
0.056,0.0257801432162523
0.055,0.02560356259346
0.054,0.0253967568278312
0.053,0.0252354517579078
0.052,0.0250491071492433
0.051,0.024835892021656
0.05,0.0246330257505178
0.049,0.0244286004453897
0.048,0.0242333654314279
0.047,0.0240776035934686
0.046,0.0238554626703262
0.045,0.023711510002613
0.044,0.0235477648675441
0.043,0.0233605429530143
0.042,0.0231760647147893
0.041,0.022863283753395
0.04,0.022694768384099
0.039,0.0224520917981863
0.038,0.0222039520740509
0.037,0.0219771284610033
0.036,0.0218534991145133
0.035,0.021628325805068
0.034,0.0214509144425392
0.033,0.0211755875498056
0.032,0.021051513031125
0.031,0.0208633355796337
0.03,0.0206362716853618
0.029,0.0203661937266588
0.028,0.0201535653322935
0.027,0.0199709795415401
0.026,0.0196477603167295
0.025,0.019316304475069
0.024,0.0189332962036132
0.023,0.0187167711555957
0.022,0.0184576082974672
0.021,0.0182719882577657
0.02,0.0180836785584688
0.019,0.0178364794701337
0.018,0.0175586380064487
0.017,0.0172259137034416
0.016,0.0169721469283103
0.015,0.0166507456451654
0.014,0.0162105113267898
0.013,0.0159360133111476
0.012,0.0155999613925814
0.011,0.0151478061452507
0.01,0.0148683059960603
0.009,0.0146070262417197
0.008,0.0141427172347903
0.007,0.0135072255507111
0.006,0.0131422383710742
0.005,0.0125411767512559
0.004,0.012156156823039
0.003,0.01137366425246
0.002,0.0103842560201883
0.001,0
"""

#Fraud - US(V3.0)
datasci.model.defaults.v_3_0_0.fraud_us.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/Fraud_SigmaAll_XGB_DEV_EXA_EXE_EXP_EXS_EXX_FCE_FMV_NSG_NSI_NSR_SMS_SSE_TDE_VCA_VCE_VCI_VCP_VCS_VNA_VNE_VNP_20220311"
datasci.model.defaults.v_3_0_0.fraud_us.name="Fraud Generic Model (US)"
datasci.model.defaults.v_3_0_0.fraud_us.version="3.0"
datasci.model.defaults.v_3_0_0.fraud_us.identifier="Fraud_SigmaAll_XGB_DEV_EXA_EXE_EXP_EXS_EXX_FCE_FMV_NSG_NSI_NSR_SMS_SSE_TDE_VCA_VCE_VCI_VCP_VCS_VNA_VNE_VNP_20220311"
datasci.model.defaults.v_3_0_0.fraud_us.params="""{"NSGVL.100030":null,"EXPVL.300017":null,"DEVAL.100058":null,"VCPVL.220812":null,"VCSVL.220210":null,"FCEVL.100053":null,"VCIVL.230812":null,"NSRVL.100007":null,"VCEVL.210310":null,"NSGVL.100017":null,"DEVAL.100022":null,"NSIVL.100033":null,"VCPVL.240099":null,"SSEVL.100014":null,"EXEVL.100025":null,"NSRVL.300008":null,"NSRVL.100005":null,"TDEVL.100011":null,"TDEVL.200008":null,"VCEVL.210212":null,"NSIVL.100001":null,"EXSVL.200035":null,"VCIVL.220906":null,"NSRVL.200028":null,"FCEVL.100047":null,"VCPVL.220407":null,"VCIVL.230409":null,"EXPVL.200042":null,"EXPVL.200029":null,"EXPVL.300014":null,"NSRVL.100008":null,"FCEVL.100004":null,"DEVAL.100027":null,"DEVAL.100029":null,"FMVAL.600031":null,"EXPVL.200045":null,"FCEVL.100046":null,"NSGVL.100021":null,"EXEVL.100000":null,"VCPVL.220607":null,"VCPVL.210610":null,"VCPVL.250099":null,"VCAVL.220602":null,"TDEVL.100025":null,"TDEVL.100039":null,"NSRVL.100002":null,"FCEVL.100036":null,"VCEVL.200003":null,"VCIVL.250099":null,"EXPVL.200024":null,"FCEVL.100007":null,"NSGVL.100012":null,"EXSVL.200036":null,"VNEVL.210307":null,"NSRVL.100009":null,"SSEVL.100001":null,"EXEVL.100006":null,"VCAVL.220409":null,"EXEVL.100014":null,"SSEVL.100033":null,"VCPVL.220810":null,"VCEVL.220710":null,"VCPVL.220809":null,"VCEVL.220610":null,"VCIVL.220810":null,"VNEVL.210612":null,"EXEVL.100017":null,"DEVAL.100046":null,"NSIVL.100004":null,"FCEVL.100017":null,"TDEVL.100002":null,"EXEVL.100029":null,"EXPVL.200007":null,"NSRVL.100004":null,"EXSVL.200022":null,"VCEVL.200007":null,"SSEVL.100030":null,"VNPVL.200008":null,"FMVAL.900019":null,"EXAVL.100005":null,"FCEVL.100049":null,"VCEVL.200012":null,"VCPVL.220409":null,"EXSVL.300020":null,"DEVAL.100026":null,"TDEVL.500001":null,"EXEVL.100031":null,"TDEVL.100007":null,"EXEVL.100027":null,"NSGVL.100024":null,"VCIVL.220907":null,"VCAVL.220410":null,"SSEVL.100002":null,"EXPVL.100007":null,"DEVAL.100048":null,"EXSVL.200037":null,"VCAVL.200005":null,"FCEVL.100008":null,"NSRVL.200018":null,"NSRVL.200032":null,"DEVAL.100051":null,"NSRVL.200014":null,"VCEVL.220910":null,"VNPVL.200009":null,"VCEVL.220112":null,"EXEVL.100004":null,"FCEVL.100019":null,"EXPVL.200016":null,"FCEVL.100002":null,"NSRVL.100003":null,"VNAVL.200007":null,"VCAVL.200009":null,"VCEVL.240099":null,"EXEVL.100011":null,"FCEVL.100048":null,"EXPVL.100002":null,"EXAVL.100002":null,"SSEVL.100010":null,"EXPVL.300010":null,"VCPVL.200006":null,"TDEVL.200007":null,"NSRVL.200011":null,"VCPVL.220712":null,"TDEVL.100008":null,"EXEVL.100012":null,"DEVAL.100049":null,"VCPVL.220710":null,"FMVAL.300045":null,"VCIVL.200012":null,"VCIVL.220707":null,"EXPVL.200044":null,"TDEVL.100035":null,"EXPVL.200037":null,"VCAVL.200010":null,"VCEVL.220909":null,"VCPVL.220609":null,"VNPVL.210310":null,"VCAVL.200012":null,"EXPVL.300007":null,"VCEVL.220812":null,"EXPVL.100005":null,"EXSVL.200042":null,"EXPVL.300013":null,"NSIVL.100005":null,"VCPVL.220907":null,"VCPVL.200012":null,"SSEVL.100006":null,"VCEVL.220612":null,"NSGVL.100022":null,"FCEVL.100006":null,"EXEVL.100002":null,"SMSVL.100046":null,"EXPVL.200025":null,"TDEVL.100038":null,"SSEVL.100034":null,"NSIVL.100061":null,"FCEVL.100059":null,"EXPVL.300001":null,"DEVAL.100055":null,"EXPVL.300009":null,"EXSVL.200043":null,"DEVAL.100047":null,"DEVAL.100056":null,"EXPVL.200015":null,"VCPVL.200007":null,"VCIVL.230109":null,"EXPVL.200009":null,"NSIVL.100026":null,"VCEVL.220907":null,"VCEVL.200009":null,"VCSVL.220309":null,"EXPVL.200010":null,"NSRVL.300001":null,"TDEVL.100013":null,"NSGVL.100018":null,"VCIVL.230612":null,"VCPVL.220109":null,"NSRVL.200022":null,"EXEVL.100028":null,"SSEVL.100031":null,"TDEVL.100004":null,"VCPVL.210609":null,"DEVAL.100024":null,"VCEVL.250099":null,"VCEVL.200006":null,"EXSVL.300001":null,"FCEVL.100056":null,"EXEVL.100037":null,"EXPVL.200005":null,"TDEVL.100006":null,"NSRVL.100010":null,"VCEVL.200010":null,"VNAVL.210612":null,"VCAVL.250099":null,"VCSVL.200012":null,"FMVAL.900005":null,"VCAVL.200007":null,"VCIVL.220909":null,"EXEVL.100036":null,"VCPVL.200005":null,"VCEVL.220310":null,"FCEVL.100034":null,"FCEVL.100060":null,"FMVAL.300002":null,"FCEVL.100068":null,"VCEVL.230112":null,"VCPVL.200010":null,"VCEVL.230212":null,"EXXVL.100205":null,"EXPVL.200008":null,"NSRVL.100006":null,"VCPVL.220709":null,"NSRVL.200010":null,"VCSVL.250099":null,"NSRVL.300004":null,"SMSVL.100043":null,"EXPVL.200036":null,"VCSVL.240099":null,"EXPVL.200023":null,"VCIVL.221009":null,"VCEVL.220712":null,"EXPVL.200001":null,"NSRVL.300005":null,"EXSVL.200027":null,"FCEVL.100063":null,"TDEVL.100001":null}"""
datasci.model.defaults.v_3_0_0.fraud_us.model_type="h2o"
datasci.model.defaults.v_3_0_0.fraud_us.model_format="mojo"
datasci.model.defaults.v_3_0_0.fraud_us.quantile_mapping="""
quantile,score
0.999,0.97847443819046
0.998,0.971745312213897
0.997,0.965814709663391
0.996,0.960439682006836
0.995,0.954270780086517
0.994,0.950079560279846
0.993,0.944045722484588
0.992,0.938908874988556
0.991,0.935216963291168
0.99,0.930653393268585
0.989,0.925942420959472
0.988,0.921341717243194
0.987,0.916829228401184
0.986,0.912428140640258
0.985,0.906385719776153
0.984,0.902432382106781
0.983,0.898655951023101
0.982,0.894191563129425
0.981,0.890186727046966
0.98,0.885906815528869
0.979,0.882175266742706
0.978,0.878580391407012
0.977,0.874832034111023
0.976,0.872199475765228
0.975,0.868728041648864
0.974,0.863736033439636
0.973,0.859375894069671
0.972,0.855317294597625
0.971,0.851404309272766
0.97,0.84744507074356
0.969,0.843867063522338
0.968,0.839986205101013
0.967,0.836043059825897
0.966,0.832256197929382
0.965,0.828505456447601
0.964,0.824593663215637
0.963,0.820570290088653
0.962,0.81699138879776
0.961,0.812300860881805
0.96,0.808302342891693
0.959,0.8030646443367
0.958,0.79938119649887
0.957,0.795254707336425
0.956,0.791071236133575
0.955,0.787864089012146
0.954,0.78406947851181
0.953,0.779949724674224
0.952,0.775849997997283
0.951,0.772625923156738
0.95,0.769415438175201
0.949,0.766324341297149
0.948,0.763644933700561
0.947,0.76018750667572
0.946,0.757071256637573
0.945,0.753467917442321
0.944,0.750514328479766
0.943,0.747146070003509
0.942,0.744145929813385
0.941,0.741367161273956
0.94,0.7375990152359
0.939,0.734589993953704
0.938,0.731934785842895
0.937,0.729055821895599
0.936,0.72640174627304
0.935,0.723303198814392
0.934,0.719708442687988
0.933,0.716484308242797
0.932,0.712928116321563
0.931,0.709486484527587
0.93,0.706896841526031
0.929,0.70314222574234
0.928,0.699323415756225
0.927,0.695648908615112
0.926,0.692938387393951
0.925,0.690487444400787
0.924,0.687258064746856
0.923,0.683373034000396
0.922,0.68015432357788
0.921,0.677939653396606
0.92,0.674309074878692
0.919,0.670363187789917
0.918,0.666810512542724
0.917,0.663655400276184
0.916,0.660841047763824
0.915,0.657465100288391
0.914,0.654687941074371
0.913,0.650570273399353
0.912,0.647232353687286
0.911,0.644077360630035
0.91,0.640835583209991
0.909,0.638992726802825
0.908,0.635465562343597
0.907,0.632687091827392
0.906,0.630262732505798
0.905,0.626914978027343
0.904,0.623533904552459
0.903,0.62075388431549
0.902,0.618037641048431
0.901,0.614990055561065
0.9,0.611868560314178
0.899,0.608356177806854
0.898,0.604584515094757
0.897,0.601321518421173
0.896,0.597765326499939
0.895,0.594600737094879
0.894,0.591772317886352
0.893,0.589059591293335
0.892,0.586576402187347
0.891,0.583312034606933
0.89,0.581107795238494
0.889,0.578407227993011
0.888,0.575287282466888
0.887,0.57273519039154
0.886,0.570090234279632
0.885,0.567652523517608
0.884,0.564601242542266
0.883,0.562313199043273
0.882,0.559324026107788
0.881,0.556210815906524
0.88,0.553777813911438
0.879,0.551380455493927
0.878,0.548835575580596
0.877,0.545691430568695
0.876,0.543019592761993
0.875,0.540562570095062
0.874,0.537265658378601
0.873,0.534075379371643
0.872,0.531772792339325
0.871,0.528424024581909
0.87,0.526186943054199
0.869,0.523183286190033
0.868,0.520591795444488
0.867,0.517983078956604
0.866,0.514571666717529
0.865,0.511753439903259
0.864,0.509323835372924
0.863,0.506715834140777
0.862,0.50354117155075
0.861,0.501348733901977
0.86,0.498787730932235
0.859,0.496263980865478
0.858,0.493333429098129
0.857,0.490947514772415
0.856,0.48868703842163
0.855,0.485859096050262
0.854,0.482936769723892
0.853,0.480639785528183
0.852,0.477965384721756
0.851,0.475662440061569
0.85,0.472751885652542
0.849,0.469600766897201
0.848,0.46730899810791
0.847,0.464871287345886
0.846,0.462468713521957
0.845,0.459381431341171
0.844,0.457485109567642
0.843,0.454893350601196
0.842,0.452476292848587
0.841,0.450244605541229
0.84,0.447923451662063
0.839,0.445921063423156
0.838,0.443795382976532
0.837,0.441713601350784
0.836,0.439405918121337
0.835,0.436697959899902
0.834,0.433711349964141
0.833,0.431552976369857
0.832,0.428958147764205
0.831,0.426952332258224
0.83,0.425267308950424
0.829,0.423179984092712
0.828,0.42107143998146
0.827,0.418365895748138
0.826,0.416273534297943
0.825,0.414708286523818
0.824,0.412986814975738
0.823,0.41113057732582
0.822,0.40898197889328
0.821,0.406960666179657
0.82,0.404924392700195
0.819,0.402692824602127
0.818,0.400338768959045
0.817,0.397878438234329
0.816,0.395889610052108
0.815,0.393797665834426
0.814,0.3925341963768
0.813,0.390474587678909
0.812,0.388715177774429
0.811,0.386867254972457
0.81,0.384919941425323
0.809,0.383134186267852
0.808,0.381174087524414
0.807,0.379042714834213
0.806,0.377112418413162
0.805,0.375437051057815
0.804,0.373514175415039
0.803,0.371179640293121
0.802,0.369095504283905
0.801,0.367676794528961
0.8,0.366015464067459
0.799,0.364161521196365
0.798,0.362584501504898
0.797,0.361038565635681
0.796,0.358815163373947
0.795,0.357157200574874
0.794,0.355842053890228
0.793,0.353868722915649
0.792,0.352073222398757
0.791,0.350079089403152
0.79,0.348107904195785
0.789,0.346192061901092
0.788,0.344671934843063
0.787,0.343090027570724
0.786,0.341278642416
0.785,0.339652389287948
0.784,0.338432639837265
0.783,0.336814790964126
0.782,0.335542917251586
0.781,0.333979904651641
0.78,0.332597464323043
0.779,0.331269055604934
0.778,0.329567521810531
0.777,0.328173577785491
0.776,0.326488763093948
0.775,0.325158506631851
0.774,0.323793679475784
0.773,0.322898775339126
0.772,0.320875704288482
0.771,0.31897023320198
0.77,0.317773401737213
0.769,0.316441357135772
0.768,0.314858168363571
0.767,0.313407838344574
0.766,0.312248617410659
0.765,0.310779750347137
0.764,0.309608042240142
0.763,0.308220624923706
0.762,0.306564271450042
0.761,0.304979920387268
0.76,0.303548216819763
0.759,0.30226731300354
0.758,0.300798445940017
0.757,0.299306184053421
0.756,0.298058986663818
0.755,0.296859562397003
0.754,0.295485913753509
0.753,0.294040381908416
0.752,0.292682379484176
0.751,0.291246384382247
0.75,0.290153503417968
0.749,0.289091199636459
0.748,0.287590980529785
0.747,0.286517947912216
0.746,0.284918129444122
0.745,0.283515572547912
0.744,0.282181441783905
0.743,0.281165808439254
0.742,0.280020624399185
0.741,0.279192566871643
0.74,0.277875751256942
0.739,0.276473879814147
0.738,0.275535643100738
0.737,0.27446636557579
0.736,0.273227483034133
0.735,0.271908104419708
0.734,0.27094566822052
0.733,0.269573837518692
0.732,0.268289327621459
0.731,0.266798675060272
0.73,0.265823394060134
0.729,0.264413774013519
0.728,0.263504087924957
0.727,0.262809097766876
0.726,0.261851698160171
0.725,0.2608562707901
0.724,0.259750396013259
0.723,0.258673936128616
0.722,0.25758546590805
0.721,0.256366252899169
0.72,0.255137503147125
0.719,0.25399625301361
0.718,0.252953320741653
0.717,0.251751333475112
0.716,0.250629782676696
0.715,0.250215113162994
0.714,0.249992400407791
0.713,0.248895481228828
0.712,0.247797921299934
0.711,0.246849372982978
0.71,0.24596220254898
0.709,0.244891971349716
0.708,0.243931978940963
0.707,0.242683231830596
0.706,0.24163968861103
0.705,0.240935534238815
0.704,0.239998340606689
0.703,0.238993257284164
0.702,0.238051891326904
0.701,0.236994743347167
0.7,0.235809057950973
0.699,0.234879851341247
0.698,0.234017387032508
0.697,0.232999593019485
0.696,0.231968566775321
0.695,0.230909809470176
0.694,0.229899480938911
0.693,0.229417309165
0.692,0.228399410843849
0.691,0.227497845888137
0.69,0.22662878036499
0.689,0.225570753216743
0.688,0.224713906645774
0.687,0.224117919802665
0.686,0.223471000790596
0.685,0.222598254680633
0.684,0.221827328205108
0.683,0.220844760537147
0.682,0.219860225915908
0.681,0.218978807330131
0.68,0.218136131763458
0.679,0.217232659459114
0.678,0.216258496046066
0.677,0.21562048792839
0.676,0.214736208319664
0.675,0.213820070028305
0.674,0.212861239910125
0.673,0.211853086948394
0.672,0.211247399449348
0.671,0.210318639874458
0.67,0.209433630108833
0.669,0.208800315856933
0.668,0.208245769143104
0.667,0.207594826817512
0.666,0.206950828433036
0.665,0.206319913268089
0.664,0.205468609929084
0.663,0.204563826322555
0.662,0.203733369708061
0.661,0.203057318925857
0.66,0.202222526073455
0.659,0.201415583491325
0.658,0.200765818357467
0.657,0.199846193194389
0.656,0.199237406253814
0.655,0.198314189910888
0.654,0.197667181491851
0.653,0.196961909532547
0.652,0.196032389998435
0.651,0.195167437195777
0.65,0.19447274506092
0.649,0.193934351205825
0.648,0.192995443940162
0.647,0.192398220300674
0.646,0.191641598939895
0.645,0.190948456525802
0.644,0.190199166536331
0.643,0.189466446638107
0.642,0.188651874661445
0.641,0.187830805778503
0.64,0.187128856778144
0.639,0.186431497335433
0.638,0.18602792918682
0.637,0.185423135757446
0.636,0.184830918908119
0.635,0.184118330478668
0.634,0.183402314782142
0.633,0.1825962215662
0.632,0.182013273239135
0.631,0.18126505613327
0.63,0.180513277649879
0.629,0.179885312914848
0.628,0.179298102855682
0.627,0.178583711385726
0.626,0.177915588021278
0.625,0.177225351333618
0.624,0.176693305373191
0.623,0.176166653633117
0.622,0.175598606467247
0.621,0.174664735794067
0.62,0.174198418855667
0.619,0.173617169260978
0.618,0.173108845949172
0.617,0.172453463077545
0.616,0.171998649835586
0.615,0.171348452568054
0.614,0.170700132846832
0.613,0.170025736093521
0.612,0.169341340661048
0.611,0.168866008520126
0.61,0.168376609683036
0.609,0.167776569724082
0.608,0.16732782125473
0.607,0.166736856102943
0.606,0.166144475340843
0.605,0.165658459067344
0.604,0.165064483880996
0.603,0.164421558380126
0.602,0.163949519395828
0.601,0.163332089781761
0.6,0.162593871355056
0.599,0.162115931510925
0.598,0.16172082722187
0.597,0.161287680268287
0.596,0.160573080182075
0.595,0.160091757774353
0.594,0.159539505839347
0.593,0.159147083759307
0.592,0.158680081367492
0.591,0.157982915639877
0.59,0.157387807965278
0.589,0.156740069389343
0.588,0.156307488679885
0.587,0.155790999531745
0.586,0.15540088713169
0.585,0.154924690723419
0.584,0.154440656304359
0.583,0.154049918055534
0.582,0.153702393174171
0.581,0.153166323900222
0.58,0.152726784348487
0.579,0.152281820774078
0.578,0.151832655072212
0.577,0.151412203907966
0.576,0.150852918624877
0.575,0.150282740592956
0.574,0.149933233857154
0.573,0.149543449282646
0.572,0.149046555161476
0.571,0.148504018783569
0.57,0.148118153214454
0.569,0.147609964013099
0.568,0.147161915898323
0.567,0.146696358919143
0.566,0.146288648247718
0.565,0.145873442292213
0.564,0.145534425973892
0.563,0.145084992051124
0.562,0.144679248332977
0.561,0.144247069954872
0.56,0.143742680549621
0.559,0.143196135759353
0.558,0.142768234014511
0.557,0.142327710986137
0.556,0.141758531332016
0.555,0.141392722725868
0.554,0.140956252813339
0.553,0.140507146716117
0.552,0.14013560116291
0.551,0.139641106128692
0.55,0.139219984412193
0.549,0.138757392764091
0.548,0.138359785079956
0.547,0.137953832745552
0.546,0.137616023421287
0.545,0.137180998921394
0.544,0.136810287833213
0.543,0.136341720819473
0.542,0.136029869318008
0.541,0.13561175763607
0.54,0.135253846645355
0.539,0.134948343038558
0.538,0.134453564882278
0.537,0.133980259299278
0.536,0.133465439081192
0.535,0.133125156164169
0.534,0.132764518260955
0.533,0.132426887750625
0.532,0.131955936551094
0.531,0.131432026624679
0.53,0.131083548069
0.529,0.130711957812309
0.528,0.130366489291191
0.527,0.129940956830978
0.526,0.12954144179821
0.525,0.129142373800277
0.524,0.128794491291046
0.523,0.128363206982612
0.522,0.12797737121582
0.521,0.127519160509109
0.52,0.127051666378974
0.519,0.126677349209785
0.518,0.126197874546051
0.517,0.125870749354362
0.516,0.125380799174308
0.515,0.125061973929405
0.514,0.124732188880443
0.513,0.124269165098667
0.512,0.123825952410697
0.511,0.123361937701702
0.51,0.122929200530052
0.509,0.122540675103664
0.508,0.122242711484432
0.507,0.121898204088211
0.506,0.121564514935016
0.505,0.121218346059322
0.504,0.120834872126579
0.503,0.12053969502449
0.502,0.120301403105258
0.501,0.119828790426254
0.5,0.119414053857326
0.499,0.118997730314731
0.498,0.118665494024753
0.497,0.118286170065402
0.496,0.118022412061691
0.495,0.11767604202032
0.494,0.117267854511737
0.493,0.116905026137828
0.492,0.116635963320732
0.491,0.116259187459945
0.49,0.115953363478183
0.489,0.115698985755443
0.488,0.115325257182121
0.487,0.114987879991531
0.486,0.114699564874172
0.485,0.114392265677452
0.484,0.114089906215667
0.483,0.11378813534975
0.482,0.113545805215835
0.481,0.113257527351379
0.48,0.11304373294115
0.479,0.112775221467018
0.478,0.112464740872383
0.477,0.112147450447082
0.476,0.11185547709465
0.475,0.111586585640907
0.474,0.111141942441463
0.473,0.110818542540073
0.472,0.110469795763492
0.471,0.110182464122772
0.47,0.109859816730022
0.469,0.109518855810165
0.468,0.109132155776023
0.467,0.108874596655368
0.466,0.108642168343067
0.465,0.108402594923973
0.464,0.108035154640674
0.463,0.107790738344192
0.462,0.107560016214847
0.461,0.107276812195777
0.46,0.106926806271076
0.459,0.106667168438434
0.458,0.106403373181819
0.457,0.106004625558853
0.456,0.105649918317794
0.455,0.105424627661705
0.454,0.105129182338714
0.453,0.104756489396095
0.452,0.104457736015319
0.451,0.104196742177009
0.45,0.103866405785083
0.449,0.103637129068374
0.448,0.103287696838378
0.447,0.102959387004375
0.446,0.102763526141643
0.445,0.102440677583217
0.444,0.102134965360164
0.443,0.101871192455291
0.442,0.101644188165664
0.441,0.10130151361227
0.44,0.101035147905349
0.439,0.100766465067863
0.438,0.100557878613471
0.437,0.100352689623832
0.436,0.100074172019958
0.435,0.099809929728508
0.434,0.0994865819811821
0.433,0.0991902574896812
0.432,0.0989248007535934
0.431,0.0986595228314399
0.43,0.0983557477593422
0.429,0.0981161594390869
0.428,0.0979015678167343
0.427,0.0976119861006736
0.426,0.0973213613033294
0.425,0.097043938934803
0.424,0.096681535243988
0.423,0.096410684287548
0.422,0.0960988625884056
0.421,0.0958487018942833
0.42,0.0955444425344467
0.419,0.0953085273504257
0.418,0.0949417799711227
0.417,0.0946614220738411
0.416,0.0944513157010078
0.415,0.0941278412938118
0.414,0.0938981622457504
0.413,0.0935951769351959
0.412,0.0933089703321456
0.411,0.0930458754301071
0.41,0.0928493440151214
0.409,0.0925920605659484
0.408,0.0922827422618866
0.407,0.0920511111617088
0.406,0.0917731150984764
0.405,0.0915743485093116
0.404,0.0913489162921905
0.403,0.0911374390125274
0.402,0.0908915698528289
0.401,0.0906181633472442
0.4,0.0903869867324829
0.399,0.090134672820568
0.398,0.0898837521672248
0.397,0.0895671471953392
0.396,0.089367464184761
0.395,0.0891147404909133
0.394,0.0888006091117858
0.393,0.0885786637663841
0.392,0.0883685275912284
0.391,0.088064357638359
0.39,0.0877981185913086
0.389,0.0875773429870605
0.388,0.0873176455497741
0.387,0.0870533287525177
0.386,0.0868300572037696
0.385,0.0866064876317977
0.384,0.0863756164908409
0.383,0.0861750543117523
0.382,0.0859680995345115
0.381,0.0857058763504028
0.38,0.0854382216930389
0.379,0.0852694362401962
0.378,0.0850794464349746
0.377,0.0848165825009346
0.376,0.0844797492027282
0.375,0.0842426791787147
0.374,0.0840325206518173
0.373,0.0838419944047927
0.372,0.0836376696825027
0.371,0.0834376960992813
0.37,0.0832104012370109
0.369,0.082976296544075
0.368,0.0827497318387031
0.367,0.0825470834970474
0.366,0.0822997912764549
0.365,0.0820921286940574
0.364,0.0818523168563842
0.363,0.0816889330744743
0.362,0.0814376398921012
0.361,0.0812094509601593
0.36,0.0809713900089263
0.359,0.0807743519544601
0.358,0.0805432274937629
0.357,0.0803514569997787
0.356,0.0802172422409057
0.355,0.07997627556324
0.354,0.0797643586993217
0.353,0.0795691981911659
0.352,0.0793340355157852
0.351,0.0791080519556999
0.35,0.0789310559630394
0.349,0.0787220224738121
0.348,0.0784883722662925
0.347,0.0781805589795112
0.346,0.0779621079564094
0.345,0.0777482986450195
0.344,0.0775289759039878
0.343,0.0773408710956573
0.342,0.077117770910263
0.341,0.0768672451376915
0.34,0.0766624361276626
0.339,0.0764530748128891
0.338,0.0762771666049957
0.337,0.0760770887136459
0.336,0.0759086087346077
0.335,0.0756817013025283
0.334,0.0754313394427299
0.333,0.0752031132578849
0.332,0.0750592574477195
0.331,0.0747716575860977
0.33,0.0745342448353767
0.329,0.0743617340922355
0.328,0.0741189196705818
0.327,0.0739214047789573
0.326,0.0737044438719749
0.325,0.0734604075551033
0.324,0.0732890143990516
0.323,0.073095828294754
0.322,0.0728882551193237
0.321,0.0727107897400856
0.32,0.0725366994738578
0.319,0.0723161771893501
0.318,0.0720594525337219
0.317,0.0718859210610389
0.316,0.0717408433556556
0.315,0.0714980736374855
0.314,0.0712741389870643
0.313,0.0711294934153556
0.312,0.0709033831954002
0.311,0.0707208439707756
0.31,0.0705323740839958
0.309,0.0704065337777137
0.308,0.0701622068881988
0.307,0.0699501112103462
0.306,0.069788858294487
0.305,0.0695939809083938
0.304,0.0694384053349495
0.303,0.0691984444856643
0.302,0.0689552128314971
0.301,0.0687625557184219
0.3,0.06857281178236
0.299,0.0683726370334625
0.298,0.0681715831160545
0.297,0.0679775401949882
0.296,0.0677837803959846
0.295,0.0675428137183189
0.294,0.067375898361206
0.293,0.0671998411417007
0.292,0.0669546201825141
0.291,0.0667970031499862
0.29,0.0665652751922607
0.289,0.0663679763674736
0.288,0.0662039294838905
0.287,0.0660261139273643
0.286,0.0658619627356529
0.285,0.0656508803367614
0.284,0.0654277354478836
0.283,0.0652607902884483
0.282,0.0650535225868225
0.281,0.0648364499211311
0.28,0.0646314471960067
0.279,0.0644209682941436
0.278,0.0642310082912445
0.277,0.0639917775988578
0.276,0.0637845769524574
0.275,0.0635782480239868
0.274,0.0633603706955909
0.273,0.0632059574127197
0.272,0.0629890114068985
0.271,0.0628356486558914
0.27,0.0626711249351501
0.269,0.0624543763697147
0.268,0.0622798465192317
0.267,0.062133889645338
0.266,0.0619744807481765
0.265,0.0618040189146995
0.264,0.0615635365247726
0.263,0.0613899752497673
0.262,0.0611608996987342
0.261,0.0609481707215309
0.26,0.0607666261494159
0.259,0.060537926852703
0.258,0.0603854171931743
0.257,0.0602021217346191
0.256,0.0599901229143142
0.255,0.0598088130354881
0.254,0.0596752613782882
0.253,0.0595226436853408
0.252,0.0593667477369308
0.251,0.059216558933258
0.25,0.059047307819128
0.249,0.0588616468012332
0.248,0.0586870312690734
0.247,0.0584986805915832
0.246,0.0582945570349693
0.245,0.0580814741551876
0.244,0.0578885152935981
0.243,0.0577224940061569
0.242,0.0575704202055931
0.241,0.0574549213051795
0.24,0.0572738088667392
0.239,0.0570935979485511
0.238,0.0569323971867561
0.237,0.056771945208311
0.236,0.0566079206764698
0.235,0.0564739368855953
0.234,0.0562829673290252
0.233,0.0561212822794914
0.232,0.0559188239276409
0.231,0.0557141043245792
0.23,0.0554982870817184
0.229,0.0553208403289318
0.228,0.0551419220864772
0.227,0.0550264343619346
0.226,0.0548440217971801
0.225,0.0547031052410602
0.224,0.0545195825397968
0.223,0.0543304085731506
0.222,0.0541279464960098
0.221,0.0539634935557842
0.22,0.0538139753043651
0.219,0.0536875128746032
0.218,0.0535369776189327
0.217,0.0533483512699604
0.216,0.0532088838517665
0.215,0.0530115067958831
0.214,0.0528232790529727
0.213,0.0525721572339534
0.212,0.0524265468120574
0.211,0.0522026233375072
0.21,0.0520610362291336
0.209,0.0519045889377594
0.208,0.0517366603016853
0.207,0.0515705831348896
0.206,0.0513563193380832
0.205,0.0512333735823631
0.204,0.0511197298765182
0.203,0.0509326122701168
0.202,0.0507546439766883
0.201,0.0505578368902206
0.2,0.0503590740263462
0.199,0.0501621626317501
0.198,0.0500040017068386
0.197,0.0498391613364219
0.196,0.0496830455958843
0.195,0.049478281289339
0.194,0.0493209213018417
0.193,0.0491300635039806
0.192,0.0489830300211906
0.191,0.0488119646906852
0.19,0.0486339293420314
0.189,0.0484764464199543
0.188,0.0483524687588214
0.187,0.0482053980231285
0.186,0.0480359196662902
0.185,0.0478729084134101
0.184,0.047722227871418
0.183,0.0475204475224018
0.182,0.0474034212529659
0.181,0.0472524613142013
0.18,0.0471136048436164
0.179,0.0469165481626987
0.178,0.0467964485287666
0.177,0.0465980060398578
0.176,0.0464626140892505
0.175,0.046281773597002
0.174,0.0460874959826469
0.173,0.0458982214331626
0.172,0.045741282403469
0.171,0.0455622673034668
0.17,0.0453558750450611
0.169,0.0451575331389904
0.168,0.04501723498106
0.167,0.0448715947568416
0.166,0.0447948127985
0.165,0.0446000434458255
0.164,0.0444023087620735
0.163,0.0442365147173404
0.162,0.0440692529082298
0.161,0.0439240150153636
0.16,0.0437420643866062
0.159,0.0434899404644966
0.158,0.0433130636811256
0.157,0.043135829269886
0.156,0.0429556593298912
0.155,0.0428182780742645
0.154,0.042660791426897
0.153,0.0424828790128231
0.152,0.042290449142456
0.151,0.0421254448592662
0.15,0.0419845283031463
0.149,0.0418214797973632
0.148,0.0416888259351253
0.147,0.0415466763079166
0.146,0.0413598045706748
0.145,0.0411843210458755
0.144,0.0409775674343109
0.143,0.0408329404890537
0.142,0.0406677685678005
0.141,0.0404850505292415
0.14,0.0402735993266105
0.139,0.0400859452784061
0.138,0.0399495474994182
0.137,0.0398202650249004
0.136,0.0396158844232559
0.135,0.0394652001559734
0.134,0.0393234491348266
0.133,0.0391684211790561
0.132,0.0390079021453857
0.131,0.0387933999300003
0.13,0.0386237204074859
0.129,0.0384510084986686
0.128,0.0382864922285079
0.127,0.0381297767162323
0.126,0.0379446037113666
0.125,0.0377774536609649
0.124,0.0376096107065677
0.123,0.0374101810157299
0.122,0.0372377783060073
0.121,0.0370554104447364
0.12,0.0368800684809684
0.119,0.0367402844130992
0.118,0.036571804434061
0.117,0.0364319719374179
0.116,0.0362235009670257
0.115,0.0360506772994995
0.114,0.035931073129177
0.113,0.0357921309769153
0.112,0.035561341792345
0.111,0.0354091748595237
0.11,0.0352604053914547
0.109,0.0351253822445869
0.108,0.0349094942212104
0.107,0.034713290631771
0.106,0.0345600768923759
0.105,0.0344005674123764
0.104,0.0341973304748535
0.103,0.034006018191576
0.102,0.0338102467358112
0.101,0.0336834192276001
0.1,0.0334968343377113
0.099,0.0333187878131866
0.098,0.0331601127982139
0.097,0.0329369232058525
0.096,0.0327165946364402
0.095,0.0325088761746883
0.094,0.0323675088584423
0.093,0.0321594513952732
0.092,0.0320083796977996
0.091,0.0318422615528106
0.09,0.0316874980926513
0.089,0.0315368175506591
0.088,0.0313857719302177
0.087,0.0312468111515045
0.086,0.0311068873852491
0.085,0.0309462919831275
0.084,0.0307838208973407
0.083,0.0306214019656181
0.082,0.0304609350860118
0.081,0.0302903242409229
0.08,0.0301307141780853
0.079,0.0299656596034765
0.078,0.0297823660075664
0.077,0.0296245068311691
0.076,0.029445294290781
0.075,0.0292774830013513
0.074,0.0290841497480869
0.073,0.0289103258401155
0.072,0.028719337657094
0.071,0.0285128429532051
0.07,0.0283059887588024
0.069,0.0281143561005592
0.068,0.0279597174376249
0.067,0.0277798026800155
0.066,0.0276060868054628
0.065,0.0274153165519237
0.064,0.0273119751363992
0.063,0.0271125435829162
0.062,0.0268806964159011
0.061,0.0266905836760997
0.06,0.0265053622424602
0.059,0.0263178925961256
0.058,0.0261192601174116
0.057,0.0259504187852144
0.056,0.0257801432162523
0.055,0.02560356259346
0.054,0.0253967568278312
0.053,0.0252354517579078
0.052,0.0250491071492433
0.051,0.024835892021656
0.05,0.0246330257505178
0.049,0.0244286004453897
0.048,0.0242333654314279
0.047,0.0240776035934686
0.046,0.0238554626703262
0.045,0.023711510002613
0.044,0.0235477648675441
0.043,0.0233605429530143
0.042,0.0231760647147893
0.041,0.022863283753395
0.04,0.022694768384099
0.039,0.0224520917981863
0.038,0.0222039520740509
0.037,0.0219771284610033
0.036,0.0218534991145133
0.035,0.021628325805068
0.034,0.0214509144425392
0.033,0.0211755875498056
0.032,0.021051513031125
0.031,0.0208633355796337
0.03,0.0206362716853618
0.029,0.0203661937266588
0.028,0.0201535653322935
0.027,0.0199709795415401
0.026,0.0196477603167295
0.025,0.019316304475069
0.024,0.0189332962036132
0.023,0.0187167711555957
0.022,0.0184576082974672
0.021,0.0182719882577657
0.02,0.0180836785584688
0.019,0.0178364794701337
0.018,0.0175586380064487
0.017,0.0172259137034416
0.016,0.0169721469283103
0.015,0.0166507456451654
0.014,0.0162105113267898
0.013,0.0159360133111476
0.012,0.0155999613925814
0.011,0.0151478061452507
0.01,0.0148683059960603
0.009,0.0146070262417197
0.008,0.0141427172347903
0.007,0.0135072255507111
0.006,0.0131422383710742
0.005,0.0125411767512559
0.004,0.012156156823039
0.003,0.01137366425246
0.002,0.0103842560201883
0.001,0
"""

#Fraud - International(V3.0)
datasci.model.defaults.v_3_0_0.fraud_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/Fraud_SigmaAll_XGB_DEV_EXA_EXE_EXP_EXS_EXX_FCE_FMV_NSG_NSI_NSR_SMS_SSE_TDE_VCA_VCE_VCI_VCP_VCS_VNA_VNE_VNP_20220311"
datasci.model.defaults.v_3_0_0.fraud_int.name="Fraud Generic Model (International)"
datasci.model.defaults.v_3_0_0.fraud_int.version="3.0"
datasci.model.defaults.v_3_0_0.fraud_int.identifier="Fraud_SigmaAll_XGB_DEV_EXA_EXE_EXP_EXS_EXX_FCE_FMV_NSG_NSI_NSR_SMS_SSE_TDE_VCA_VCE_VCI_VCP_VCS_VNA_VNE_VNP_20220311"
datasci.model.defaults.v_3_0_0.fraud_int.params="""{"NSGVL.100030":null,"EXPVL.300017":null,"DEVAL.100058":null,"VCPVL.220812":null,"VCSVL.220210":null,"FCEVL.100053":null,"VCIVL.230812":null,"NSRVL.100007":null,"VCEVL.210310":null,"NSGVL.100017":null,"DEVAL.100022":null,"NSIVL.100033":null,"VCPVL.240099":null,"SSEVL.100014":null,"EXEVL.100025":null,"NSRVL.300008":null,"NSRVL.100005":null,"TDEVL.100011":null,"TDEVL.200008":null,"VCEVL.210212":null,"NSIVL.100001":null,"EXSVL.200035":null,"VCIVL.220906":null,"NSRVL.200028":null,"FCEVL.100047":null,"VCPVL.220407":null,"VCIVL.230409":null,"EXPVL.200042":null,"EXPVL.200029":null,"EXPVL.300014":null,"NSRVL.100008":null,"FCEVL.100004":null,"DEVAL.100027":null,"DEVAL.100029":null,"FMVAL.600031":null,"EXPVL.200045":null,"FCEVL.100046":null,"NSGVL.100021":null,"EXEVL.100000":null,"VCPVL.220607":null,"VCPVL.210610":null,"VCPVL.250099":null,"VCAVL.220602":null,"TDEVL.100025":null,"TDEVL.100039":null,"NSRVL.100002":null,"FCEVL.100036":null,"VCEVL.200003":null,"VCIVL.250099":null,"EXPVL.200024":null,"FCEVL.100007":null,"NSGVL.100012":null,"EXSVL.200036":null,"VNEVL.210307":null,"NSRVL.100009":null,"SSEVL.100001":null,"EXEVL.100006":null,"VCAVL.220409":null,"EXEVL.100014":null,"SSEVL.100033":null,"VCPVL.220810":null,"VCEVL.220710":null,"VCPVL.220809":null,"VCEVL.220610":null,"VCIVL.220810":null,"VNEVL.210612":null,"EXEVL.100017":null,"DEVAL.100046":null,"NSIVL.100004":null,"FCEVL.100017":null,"TDEVL.100002":null,"EXEVL.100029":null,"EXPVL.200007":null,"NSRVL.100004":null,"EXSVL.200022":null,"VCEVL.200007":null,"SSEVL.100030":null,"VNPVL.200008":null,"FMVAL.900019":null,"EXAVL.100005":null,"FCEVL.100049":null,"VCEVL.200012":null,"VCPVL.220409":null,"EXSVL.300020":null,"DEVAL.100026":null,"TDEVL.500001":null,"EXEVL.100031":null,"TDEVL.100007":null,"EXEVL.100027":null,"NSGVL.100024":null,"VCIVL.220907":null,"VCAVL.220410":null,"SSEVL.100002":null,"EXPVL.100007":null,"DEVAL.100048":null,"EXSVL.200037":null,"VCAVL.200005":null,"FCEVL.100008":null,"NSRVL.200018":null,"NSRVL.200032":null,"DEVAL.100051":null,"NSRVL.200014":null,"VCEVL.220910":null,"VNPVL.200009":null,"VCEVL.220112":null,"EXEVL.100004":null,"FCEVL.100019":null,"EXPVL.200016":null,"FCEVL.100002":null,"NSRVL.100003":null,"VNAVL.200007":null,"VCAVL.200009":null,"VCEVL.240099":null,"EXEVL.100011":null,"FCEVL.100048":null,"EXPVL.100002":null,"EXAVL.100002":null,"SSEVL.100010":null,"EXPVL.300010":null,"VCPVL.200006":null,"TDEVL.200007":null,"NSRVL.200011":null,"VCPVL.220712":null,"TDEVL.100008":null,"EXEVL.100012":null,"DEVAL.100049":null,"VCPVL.220710":null,"FMVAL.300045":null,"VCIVL.200012":null,"VCIVL.220707":null,"EXPVL.200044":null,"TDEVL.100035":null,"EXPVL.200037":null,"VCAVL.200010":null,"VCEVL.220909":null,"VCPVL.220609":null,"VNPVL.210310":null,"VCAVL.200012":null,"EXPVL.300007":null,"VCEVL.220812":null,"EXPVL.100005":null,"EXSVL.200042":null,"EXPVL.300013":null,"NSIVL.100005":null,"VCPVL.220907":null,"VCPVL.200012":null,"SSEVL.100006":null,"VCEVL.220612":null,"NSGVL.100022":null,"FCEVL.100006":null,"EXEVL.100002":null,"SMSVL.100046":null,"EXPVL.200025":null,"TDEVL.100038":null,"SSEVL.100034":null,"NSIVL.100061":null,"FCEVL.100059":null,"EXPVL.300001":null,"DEVAL.100055":null,"EXPVL.300009":null,"EXSVL.200043":null,"DEVAL.100047":null,"DEVAL.100056":null,"EXPVL.200015":null,"VCPVL.200007":null,"VCIVL.230109":null,"EXPVL.200009":null,"NSIVL.100026":null,"VCEVL.220907":null,"VCEVL.200009":null,"VCSVL.220309":null,"EXPVL.200010":null,"NSRVL.300001":null,"TDEVL.100013":null,"NSGVL.100018":null,"VCIVL.230612":null,"VCPVL.220109":null,"NSRVL.200022":null,"EXEVL.100028":null,"SSEVL.100031":null,"TDEVL.100004":null,"VCPVL.210609":null,"DEVAL.100024":null,"VCEVL.250099":null,"VCEVL.200006":null,"EXSVL.300001":null,"FCEVL.100056":null,"EXEVL.100037":null,"EXPVL.200005":null,"TDEVL.100006":null,"NSRVL.100010":null,"VCEVL.200010":null,"VNAVL.210612":null,"VCAVL.250099":null,"VCSVL.200012":null,"FMVAL.900005":null,"VCAVL.200007":null,"VCIVL.220909":null,"EXEVL.100036":null,"VCPVL.200005":null,"VCEVL.220310":null,"FCEVL.100034":null,"FCEVL.100060":null,"FMVAL.300002":null,"FCEVL.100068":null,"VCEVL.230112":null,"VCPVL.200010":null,"VCEVL.230212":null,"EXXVL.100205":null,"EXPVL.200008":null,"NSRVL.100006":null,"VCPVL.220709":null,"NSRVL.200010":null,"VCSVL.250099":null,"NSRVL.300004":null,"SMSVL.100043":null,"EXPVL.200036":null,"VCSVL.240099":null,"EXPVL.200023":null,"VCIVL.221009":null,"VCEVL.220712":null,"EXPVL.200001":null,"NSRVL.300005":null,"EXSVL.200027":null,"FCEVL.100063":null,"TDEVL.100001":null}"""
datasci.model.defaults.v_3_0_0.fraud_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.fraud_int.model_format="mojo"
datasci.model.defaults.v_3_0_0.fraud_int.quantile_mapping="""
quantile,score
0.999,0.97847443819046
0.998,0.971745312213897
0.997,0.965814709663391
0.996,0.960439682006836
0.995,0.954270780086517
0.994,0.950079560279846
0.993,0.944045722484588
0.992,0.938908874988556
0.991,0.935216963291168
0.99,0.930653393268585
0.989,0.925942420959472
0.988,0.921341717243194
0.987,0.916829228401184
0.986,0.912428140640258
0.985,0.906385719776153
0.984,0.902432382106781
0.983,0.898655951023101
0.982,0.894191563129425
0.981,0.890186727046966
0.98,0.885906815528869
0.979,0.882175266742706
0.978,0.878580391407012
0.977,0.874832034111023
0.976,0.872199475765228
0.975,0.868728041648864
0.974,0.863736033439636
0.973,0.859375894069671
0.972,0.855317294597625
0.971,0.851404309272766
0.97,0.84744507074356
0.969,0.843867063522338
0.968,0.839986205101013
0.967,0.836043059825897
0.966,0.832256197929382
0.965,0.828505456447601
0.964,0.824593663215637
0.963,0.820570290088653
0.962,0.81699138879776
0.961,0.812300860881805
0.96,0.808302342891693
0.959,0.8030646443367
0.958,0.79938119649887
0.957,0.795254707336425
0.956,0.791071236133575
0.955,0.787864089012146
0.954,0.78406947851181
0.953,0.779949724674224
0.952,0.775849997997283
0.951,0.772625923156738
0.95,0.769415438175201
0.949,0.766324341297149
0.948,0.763644933700561
0.947,0.76018750667572
0.946,0.757071256637573
0.945,0.753467917442321
0.944,0.750514328479766
0.943,0.747146070003509
0.942,0.744145929813385
0.941,0.741367161273956
0.94,0.7375990152359
0.939,0.734589993953704
0.938,0.731934785842895
0.937,0.729055821895599
0.936,0.72640174627304
0.935,0.723303198814392
0.934,0.719708442687988
0.933,0.716484308242797
0.932,0.712928116321563
0.931,0.709486484527587
0.93,0.706896841526031
0.929,0.70314222574234
0.928,0.699323415756225
0.927,0.695648908615112
0.926,0.692938387393951
0.925,0.690487444400787
0.924,0.687258064746856
0.923,0.683373034000396
0.922,0.68015432357788
0.921,0.677939653396606
0.92,0.674309074878692
0.919,0.670363187789917
0.918,0.666810512542724
0.917,0.663655400276184
0.916,0.660841047763824
0.915,0.657465100288391
0.914,0.654687941074371
0.913,0.650570273399353
0.912,0.647232353687286
0.911,0.644077360630035
0.91,0.640835583209991
0.909,0.638992726802825
0.908,0.635465562343597
0.907,0.632687091827392
0.906,0.630262732505798
0.905,0.626914978027343
0.904,0.623533904552459
0.903,0.62075388431549
0.902,0.618037641048431
0.901,0.614990055561065
0.9,0.611868560314178
0.899,0.608356177806854
0.898,0.604584515094757
0.897,0.601321518421173
0.896,0.597765326499939
0.895,0.594600737094879
0.894,0.591772317886352
0.893,0.589059591293335
0.892,0.586576402187347
0.891,0.583312034606933
0.89,0.581107795238494
0.889,0.578407227993011
0.888,0.575287282466888
0.887,0.57273519039154
0.886,0.570090234279632
0.885,0.567652523517608
0.884,0.564601242542266
0.883,0.562313199043273
0.882,0.559324026107788
0.881,0.556210815906524
0.88,0.553777813911438
0.879,0.551380455493927
0.878,0.548835575580596
0.877,0.545691430568695
0.876,0.543019592761993
0.875,0.540562570095062
0.874,0.537265658378601
0.873,0.534075379371643
0.872,0.531772792339325
0.871,0.528424024581909
0.87,0.526186943054199
0.869,0.523183286190033
0.868,0.520591795444488
0.867,0.517983078956604
0.866,0.514571666717529
0.865,0.511753439903259
0.864,0.509323835372924
0.863,0.506715834140777
0.862,0.50354117155075
0.861,0.501348733901977
0.86,0.498787730932235
0.859,0.496263980865478
0.858,0.493333429098129
0.857,0.490947514772415
0.856,0.48868703842163
0.855,0.485859096050262
0.854,0.482936769723892
0.853,0.480639785528183
0.852,0.477965384721756
0.851,0.475662440061569
0.85,0.472751885652542
0.849,0.469600766897201
0.848,0.46730899810791
0.847,0.464871287345886
0.846,0.462468713521957
0.845,0.459381431341171
0.844,0.457485109567642
0.843,0.454893350601196
0.842,0.452476292848587
0.841,0.450244605541229
0.84,0.447923451662063
0.839,0.445921063423156
0.838,0.443795382976532
0.837,0.441713601350784
0.836,0.439405918121337
0.835,0.436697959899902
0.834,0.433711349964141
0.833,0.431552976369857
0.832,0.428958147764205
0.831,0.426952332258224
0.83,0.425267308950424
0.829,0.423179984092712
0.828,0.42107143998146
0.827,0.418365895748138
0.826,0.416273534297943
0.825,0.414708286523818
0.824,0.412986814975738
0.823,0.41113057732582
0.822,0.40898197889328
0.821,0.406960666179657
0.82,0.404924392700195
0.819,0.402692824602127
0.818,0.400338768959045
0.817,0.397878438234329
0.816,0.395889610052108
0.815,0.393797665834426
0.814,0.3925341963768
0.813,0.390474587678909
0.812,0.388715177774429
0.811,0.386867254972457
0.81,0.384919941425323
0.809,0.383134186267852
0.808,0.381174087524414
0.807,0.379042714834213
0.806,0.377112418413162
0.805,0.375437051057815
0.804,0.373514175415039
0.803,0.371179640293121
0.802,0.369095504283905
0.801,0.367676794528961
0.8,0.366015464067459
0.799,0.364161521196365
0.798,0.362584501504898
0.797,0.361038565635681
0.796,0.358815163373947
0.795,0.357157200574874
0.794,0.355842053890228
0.793,0.353868722915649
0.792,0.352073222398757
0.791,0.350079089403152
0.79,0.348107904195785
0.789,0.346192061901092
0.788,0.344671934843063
0.787,0.343090027570724
0.786,0.341278642416
0.785,0.339652389287948
0.784,0.338432639837265
0.783,0.336814790964126
0.782,0.335542917251586
0.781,0.333979904651641
0.78,0.332597464323043
0.779,0.331269055604934
0.778,0.329567521810531
0.777,0.328173577785491
0.776,0.326488763093948
0.775,0.325158506631851
0.774,0.323793679475784
0.773,0.322898775339126
0.772,0.320875704288482
0.771,0.31897023320198
0.77,0.317773401737213
0.769,0.316441357135772
0.768,0.314858168363571
0.767,0.313407838344574
0.766,0.312248617410659
0.765,0.310779750347137
0.764,0.309608042240142
0.763,0.308220624923706
0.762,0.306564271450042
0.761,0.304979920387268
0.76,0.303548216819763
0.759,0.30226731300354
0.758,0.300798445940017
0.757,0.299306184053421
0.756,0.298058986663818
0.755,0.296859562397003
0.754,0.295485913753509
0.753,0.294040381908416
0.752,0.292682379484176
0.751,0.291246384382247
0.75,0.290153503417968
0.749,0.289091199636459
0.748,0.287590980529785
0.747,0.286517947912216
0.746,0.284918129444122
0.745,0.283515572547912
0.744,0.282181441783905
0.743,0.281165808439254
0.742,0.280020624399185
0.741,0.279192566871643
0.74,0.277875751256942
0.739,0.276473879814147
0.738,0.275535643100738
0.737,0.27446636557579
0.736,0.273227483034133
0.735,0.271908104419708
0.734,0.27094566822052
0.733,0.269573837518692
0.732,0.268289327621459
0.731,0.266798675060272
0.73,0.265823394060134
0.729,0.264413774013519
0.728,0.263504087924957
0.727,0.262809097766876
0.726,0.261851698160171
0.725,0.2608562707901
0.724,0.259750396013259
0.723,0.258673936128616
0.722,0.25758546590805
0.721,0.256366252899169
0.72,0.255137503147125
0.719,0.25399625301361
0.718,0.252953320741653
0.717,0.251751333475112
0.716,0.250629782676696
0.715,0.250215113162994
0.714,0.249992400407791
0.713,0.248895481228828
0.712,0.247797921299934
0.711,0.246849372982978
0.71,0.24596220254898
0.709,0.244891971349716
0.708,0.243931978940963
0.707,0.242683231830596
0.706,0.24163968861103
0.705,0.240935534238815
0.704,0.239998340606689
0.703,0.238993257284164
0.702,0.238051891326904
0.701,0.236994743347167
0.7,0.235809057950973
0.699,0.234879851341247
0.698,0.234017387032508
0.697,0.232999593019485
0.696,0.231968566775321
0.695,0.230909809470176
0.694,0.229899480938911
0.693,0.229417309165
0.692,0.228399410843849
0.691,0.227497845888137
0.69,0.22662878036499
0.689,0.225570753216743
0.688,0.224713906645774
0.687,0.224117919802665
0.686,0.223471000790596
0.685,0.222598254680633
0.684,0.221827328205108
0.683,0.220844760537147
0.682,0.219860225915908
0.681,0.218978807330131
0.68,0.218136131763458
0.679,0.217232659459114
0.678,0.216258496046066
0.677,0.21562048792839
0.676,0.214736208319664
0.675,0.213820070028305
0.674,0.212861239910125
0.673,0.211853086948394
0.672,0.211247399449348
0.671,0.210318639874458
0.67,0.209433630108833
0.669,0.208800315856933
0.668,0.208245769143104
0.667,0.207594826817512
0.666,0.206950828433036
0.665,0.206319913268089
0.664,0.205468609929084
0.663,0.204563826322555
0.662,0.203733369708061
0.661,0.203057318925857
0.66,0.202222526073455
0.659,0.201415583491325
0.658,0.200765818357467
0.657,0.199846193194389
0.656,0.199237406253814
0.655,0.198314189910888
0.654,0.197667181491851
0.653,0.196961909532547
0.652,0.196032389998435
0.651,0.195167437195777
0.65,0.19447274506092
0.649,0.193934351205825
0.648,0.192995443940162
0.647,0.192398220300674
0.646,0.191641598939895
0.645,0.190948456525802
0.644,0.190199166536331
0.643,0.189466446638107
0.642,0.188651874661445
0.641,0.187830805778503
0.64,0.187128856778144
0.639,0.186431497335433
0.638,0.18602792918682
0.637,0.185423135757446
0.636,0.184830918908119
0.635,0.184118330478668
0.634,0.183402314782142
0.633,0.1825962215662
0.632,0.182013273239135
0.631,0.18126505613327
0.63,0.180513277649879
0.629,0.179885312914848
0.628,0.179298102855682
0.627,0.178583711385726
0.626,0.177915588021278
0.625,0.177225351333618
0.624,0.176693305373191
0.623,0.176166653633117
0.622,0.175598606467247
0.621,0.174664735794067
0.62,0.174198418855667
0.619,0.173617169260978
0.618,0.173108845949172
0.617,0.172453463077545
0.616,0.171998649835586
0.615,0.171348452568054
0.614,0.170700132846832
0.613,0.170025736093521
0.612,0.169341340661048
0.611,0.168866008520126
0.61,0.168376609683036
0.609,0.167776569724082
0.608,0.16732782125473
0.607,0.166736856102943
0.606,0.166144475340843
0.605,0.165658459067344
0.604,0.165064483880996
0.603,0.164421558380126
0.602,0.163949519395828
0.601,0.163332089781761
0.6,0.162593871355056
0.599,0.162115931510925
0.598,0.16172082722187
0.597,0.161287680268287
0.596,0.160573080182075
0.595,0.160091757774353
0.594,0.159539505839347
0.593,0.159147083759307
0.592,0.158680081367492
0.591,0.157982915639877
0.59,0.157387807965278
0.589,0.156740069389343
0.588,0.156307488679885
0.587,0.155790999531745
0.586,0.15540088713169
0.585,0.154924690723419
0.584,0.154440656304359
0.583,0.154049918055534
0.582,0.153702393174171
0.581,0.153166323900222
0.58,0.152726784348487
0.579,0.152281820774078
0.578,0.151832655072212
0.577,0.151412203907966
0.576,0.150852918624877
0.575,0.150282740592956
0.574,0.149933233857154
0.573,0.149543449282646
0.572,0.149046555161476
0.571,0.148504018783569
0.57,0.148118153214454
0.569,0.147609964013099
0.568,0.147161915898323
0.567,0.146696358919143
0.566,0.146288648247718
0.565,0.145873442292213
0.564,0.145534425973892
0.563,0.145084992051124
0.562,0.144679248332977
0.561,0.144247069954872
0.56,0.143742680549621
0.559,0.143196135759353
0.558,0.142768234014511
0.557,0.142327710986137
0.556,0.141758531332016
0.555,0.141392722725868
0.554,0.140956252813339
0.553,0.140507146716117
0.552,0.14013560116291
0.551,0.139641106128692
0.55,0.139219984412193
0.549,0.138757392764091
0.548,0.138359785079956
0.547,0.137953832745552
0.546,0.137616023421287
0.545,0.137180998921394
0.544,0.136810287833213
0.543,0.136341720819473
0.542,0.136029869318008
0.541,0.13561175763607
0.54,0.135253846645355
0.539,0.134948343038558
0.538,0.134453564882278
0.537,0.133980259299278
0.536,0.133465439081192
0.535,0.133125156164169
0.534,0.132764518260955
0.533,0.132426887750625
0.532,0.131955936551094
0.531,0.131432026624679
0.53,0.131083548069
0.529,0.130711957812309
0.528,0.130366489291191
0.527,0.129940956830978
0.526,0.12954144179821
0.525,0.129142373800277
0.524,0.128794491291046
0.523,0.128363206982612
0.522,0.12797737121582
0.521,0.127519160509109
0.52,0.127051666378974
0.519,0.126677349209785
0.518,0.126197874546051
0.517,0.125870749354362
0.516,0.125380799174308
0.515,0.125061973929405
0.514,0.124732188880443
0.513,0.124269165098667
0.512,0.123825952410697
0.511,0.123361937701702
0.51,0.122929200530052
0.509,0.122540675103664
0.508,0.122242711484432
0.507,0.121898204088211
0.506,0.121564514935016
0.505,0.121218346059322
0.504,0.120834872126579
0.503,0.12053969502449
0.502,0.120301403105258
0.501,0.119828790426254
0.5,0.119414053857326
0.499,0.118997730314731
0.498,0.118665494024753
0.497,0.118286170065402
0.496,0.118022412061691
0.495,0.11767604202032
0.494,0.117267854511737
0.493,0.116905026137828
0.492,0.116635963320732
0.491,0.116259187459945
0.49,0.115953363478183
0.489,0.115698985755443
0.488,0.115325257182121
0.487,0.114987879991531
0.486,0.114699564874172
0.485,0.114392265677452
0.484,0.114089906215667
0.483,0.11378813534975
0.482,0.113545805215835
0.481,0.113257527351379
0.48,0.11304373294115
0.479,0.112775221467018
0.478,0.112464740872383
0.477,0.112147450447082
0.476,0.11185547709465
0.475,0.111586585640907
0.474,0.111141942441463
0.473,0.110818542540073
0.472,0.110469795763492
0.471,0.110182464122772
0.47,0.109859816730022
0.469,0.109518855810165
0.468,0.109132155776023
0.467,0.108874596655368
0.466,0.108642168343067
0.465,0.108402594923973
0.464,0.108035154640674
0.463,0.107790738344192
0.462,0.107560016214847
0.461,0.107276812195777
0.46,0.106926806271076
0.459,0.106667168438434
0.458,0.106403373181819
0.457,0.106004625558853
0.456,0.105649918317794
0.455,0.105424627661705
0.454,0.105129182338714
0.453,0.104756489396095
0.452,0.104457736015319
0.451,0.104196742177009
0.45,0.103866405785083
0.449,0.103637129068374
0.448,0.103287696838378
0.447,0.102959387004375
0.446,0.102763526141643
0.445,0.102440677583217
0.444,0.102134965360164
0.443,0.101871192455291
0.442,0.101644188165664
0.441,0.10130151361227
0.44,0.101035147905349
0.439,0.100766465067863
0.438,0.100557878613471
0.437,0.100352689623832
0.436,0.100074172019958
0.435,0.099809929728508
0.434,0.0994865819811821
0.433,0.0991902574896812
0.432,0.0989248007535934
0.431,0.0986595228314399
0.43,0.0983557477593422
0.429,0.0981161594390869
0.428,0.0979015678167343
0.427,0.0976119861006736
0.426,0.0973213613033294
0.425,0.097043938934803
0.424,0.096681535243988
0.423,0.096410684287548
0.422,0.0960988625884056
0.421,0.0958487018942833
0.42,0.0955444425344467
0.419,0.0953085273504257
0.418,0.0949417799711227
0.417,0.0946614220738411
0.416,0.0944513157010078
0.415,0.0941278412938118
0.414,0.0938981622457504
0.413,0.0935951769351959
0.412,0.0933089703321456
0.411,0.0930458754301071
0.41,0.0928493440151214
0.409,0.0925920605659484
0.408,0.0922827422618866
0.407,0.0920511111617088
0.406,0.0917731150984764
0.405,0.0915743485093116
0.404,0.0913489162921905
0.403,0.0911374390125274
0.402,0.0908915698528289
0.401,0.0906181633472442
0.4,0.0903869867324829
0.399,0.090134672820568
0.398,0.0898837521672248
0.397,0.0895671471953392
0.396,0.089367464184761
0.395,0.0891147404909133
0.394,0.0888006091117858
0.393,0.0885786637663841
0.392,0.0883685275912284
0.391,0.088064357638359
0.39,0.0877981185913086
0.389,0.0875773429870605
0.388,0.0873176455497741
0.387,0.0870533287525177
0.386,0.0868300572037696
0.385,0.0866064876317977
0.384,0.0863756164908409
0.383,0.0861750543117523
0.382,0.0859680995345115
0.381,0.0857058763504028
0.38,0.0854382216930389
0.379,0.0852694362401962
0.378,0.0850794464349746
0.377,0.0848165825009346
0.376,0.0844797492027282
0.375,0.0842426791787147
0.374,0.0840325206518173
0.373,0.0838419944047927
0.372,0.0836376696825027
0.371,0.0834376960992813
0.37,0.0832104012370109
0.369,0.082976296544075
0.368,0.0827497318387031
0.367,0.0825470834970474
0.366,0.0822997912764549
0.365,0.0820921286940574
0.364,0.0818523168563842
0.363,0.0816889330744743
0.362,0.0814376398921012
0.361,0.0812094509601593
0.36,0.0809713900089263
0.359,0.0807743519544601
0.358,0.0805432274937629
0.357,0.0803514569997787
0.356,0.0802172422409057
0.355,0.07997627556324
0.354,0.0797643586993217
0.353,0.0795691981911659
0.352,0.0793340355157852
0.351,0.0791080519556999
0.35,0.0789310559630394
0.349,0.0787220224738121
0.348,0.0784883722662925
0.347,0.0781805589795112
0.346,0.0779621079564094
0.345,0.0777482986450195
0.344,0.0775289759039878
0.343,0.0773408710956573
0.342,0.077117770910263
0.341,0.0768672451376915
0.34,0.0766624361276626
0.339,0.0764530748128891
0.338,0.0762771666049957
0.337,0.0760770887136459
0.336,0.0759086087346077
0.335,0.0756817013025283
0.334,0.0754313394427299
0.333,0.0752031132578849
0.332,0.0750592574477195
0.331,0.0747716575860977
0.33,0.0745342448353767
0.329,0.0743617340922355
0.328,0.0741189196705818
0.327,0.0739214047789573
0.326,0.0737044438719749
0.325,0.0734604075551033
0.324,0.0732890143990516
0.323,0.073095828294754
0.322,0.0728882551193237
0.321,0.0727107897400856
0.32,0.0725366994738578
0.319,0.0723161771893501
0.318,0.0720594525337219
0.317,0.0718859210610389
0.316,0.0717408433556556
0.315,0.0714980736374855
0.314,0.0712741389870643
0.313,0.0711294934153556
0.312,0.0709033831954002
0.311,0.0707208439707756
0.31,0.0705323740839958
0.309,0.0704065337777137
0.308,0.0701622068881988
0.307,0.0699501112103462
0.306,0.069788858294487
0.305,0.0695939809083938
0.304,0.0694384053349495
0.303,0.0691984444856643
0.302,0.0689552128314971
0.301,0.0687625557184219
0.3,0.06857281178236
0.299,0.0683726370334625
0.298,0.0681715831160545
0.297,0.0679775401949882
0.296,0.0677837803959846
0.295,0.0675428137183189
0.294,0.067375898361206
0.293,0.0671998411417007
0.292,0.0669546201825141
0.291,0.0667970031499862
0.29,0.0665652751922607
0.289,0.0663679763674736
0.288,0.0662039294838905
0.287,0.0660261139273643
0.286,0.0658619627356529
0.285,0.0656508803367614
0.284,0.0654277354478836
0.283,0.0652607902884483
0.282,0.0650535225868225
0.281,0.0648364499211311
0.28,0.0646314471960067
0.279,0.0644209682941436
0.278,0.0642310082912445
0.277,0.0639917775988578
0.276,0.0637845769524574
0.275,0.0635782480239868
0.274,0.0633603706955909
0.273,0.0632059574127197
0.272,0.0629890114068985
0.271,0.0628356486558914
0.27,0.0626711249351501
0.269,0.0624543763697147
0.268,0.0622798465192317
0.267,0.062133889645338
0.266,0.0619744807481765
0.265,0.0618040189146995
0.264,0.0615635365247726
0.263,0.0613899752497673
0.262,0.0611608996987342
0.261,0.0609481707215309
0.26,0.0607666261494159
0.259,0.060537926852703
0.258,0.0603854171931743
0.257,0.0602021217346191
0.256,0.0599901229143142
0.255,0.0598088130354881
0.254,0.0596752613782882
0.253,0.0595226436853408
0.252,0.0593667477369308
0.251,0.059216558933258
0.25,0.059047307819128
0.249,0.0588616468012332
0.248,0.0586870312690734
0.247,0.0584986805915832
0.246,0.0582945570349693
0.245,0.0580814741551876
0.244,0.0578885152935981
0.243,0.0577224940061569
0.242,0.0575704202055931
0.241,0.0574549213051795
0.24,0.0572738088667392
0.239,0.0570935979485511
0.238,0.0569323971867561
0.237,0.056771945208311
0.236,0.0566079206764698
0.235,0.0564739368855953
0.234,0.0562829673290252
0.233,0.0561212822794914
0.232,0.0559188239276409
0.231,0.0557141043245792
0.23,0.0554982870817184
0.229,0.0553208403289318
0.228,0.0551419220864772
0.227,0.0550264343619346
0.226,0.0548440217971801
0.225,0.0547031052410602
0.224,0.0545195825397968
0.223,0.0543304085731506
0.222,0.0541279464960098
0.221,0.0539634935557842
0.22,0.0538139753043651
0.219,0.0536875128746032
0.218,0.0535369776189327
0.217,0.0533483512699604
0.216,0.0532088838517665
0.215,0.0530115067958831
0.214,0.0528232790529727
0.213,0.0525721572339534
0.212,0.0524265468120574
0.211,0.0522026233375072
0.21,0.0520610362291336
0.209,0.0519045889377594
0.208,0.0517366603016853
0.207,0.0515705831348896
0.206,0.0513563193380832
0.205,0.0512333735823631
0.204,0.0511197298765182
0.203,0.0509326122701168
0.202,0.0507546439766883
0.201,0.0505578368902206
0.2,0.0503590740263462
0.199,0.0501621626317501
0.198,0.0500040017068386
0.197,0.0498391613364219
0.196,0.0496830455958843
0.195,0.049478281289339
0.194,0.0493209213018417
0.193,0.0491300635039806
0.192,0.0489830300211906
0.191,0.0488119646906852
0.19,0.0486339293420314
0.189,0.0484764464199543
0.188,0.0483524687588214
0.187,0.0482053980231285
0.186,0.0480359196662902
0.185,0.0478729084134101
0.184,0.047722227871418
0.183,0.0475204475224018
0.182,0.0474034212529659
0.181,0.0472524613142013
0.18,0.0471136048436164
0.179,0.0469165481626987
0.178,0.0467964485287666
0.177,0.0465980060398578
0.176,0.0464626140892505
0.175,0.046281773597002
0.174,0.0460874959826469
0.173,0.0458982214331626
0.172,0.045741282403469
0.171,0.0455622673034668
0.17,0.0453558750450611
0.169,0.0451575331389904
0.168,0.04501723498106
0.167,0.0448715947568416
0.166,0.0447948127985
0.165,0.0446000434458255
0.164,0.0444023087620735
0.163,0.0442365147173404
0.162,0.0440692529082298
0.161,0.0439240150153636
0.16,0.0437420643866062
0.159,0.0434899404644966
0.158,0.0433130636811256
0.157,0.043135829269886
0.156,0.0429556593298912
0.155,0.0428182780742645
0.154,0.042660791426897
0.153,0.0424828790128231
0.152,0.042290449142456
0.151,0.0421254448592662
0.15,0.0419845283031463
0.149,0.0418214797973632
0.148,0.0416888259351253
0.147,0.0415466763079166
0.146,0.0413598045706748
0.145,0.0411843210458755
0.144,0.0409775674343109
0.143,0.0408329404890537
0.142,0.0406677685678005
0.141,0.0404850505292415
0.14,0.0402735993266105
0.139,0.0400859452784061
0.138,0.0399495474994182
0.137,0.0398202650249004
0.136,0.0396158844232559
0.135,0.0394652001559734
0.134,0.0393234491348266
0.133,0.0391684211790561
0.132,0.0390079021453857
0.131,0.0387933999300003
0.13,0.0386237204074859
0.129,0.0384510084986686
0.128,0.0382864922285079
0.127,0.0381297767162323
0.126,0.0379446037113666
0.125,0.0377774536609649
0.124,0.0376096107065677
0.123,0.0374101810157299
0.122,0.0372377783060073
0.121,0.0370554104447364
0.12,0.0368800684809684
0.119,0.0367402844130992
0.118,0.036571804434061
0.117,0.0364319719374179
0.116,0.0362235009670257
0.115,0.0360506772994995
0.114,0.035931073129177
0.113,0.0357921309769153
0.112,0.035561341792345
0.111,0.0354091748595237
0.11,0.0352604053914547
0.109,0.0351253822445869
0.108,0.0349094942212104
0.107,0.034713290631771
0.106,0.0345600768923759
0.105,0.0344005674123764
0.104,0.0341973304748535
0.103,0.034006018191576
0.102,0.0338102467358112
0.101,0.0336834192276001
0.1,0.0334968343377113
0.099,0.0333187878131866
0.098,0.0331601127982139
0.097,0.0329369232058525
0.096,0.0327165946364402
0.095,0.0325088761746883
0.094,0.0323675088584423
0.093,0.0321594513952732
0.092,0.0320083796977996
0.091,0.0318422615528106
0.09,0.0316874980926513
0.089,0.0315368175506591
0.088,0.0313857719302177
0.087,0.0312468111515045
0.086,0.0311068873852491
0.085,0.0309462919831275
0.084,0.0307838208973407
0.083,0.0306214019656181
0.082,0.0304609350860118
0.081,0.0302903242409229
0.08,0.0301307141780853
0.079,0.0299656596034765
0.078,0.0297823660075664
0.077,0.0296245068311691
0.076,0.029445294290781
0.075,0.0292774830013513
0.074,0.0290841497480869
0.073,0.0289103258401155
0.072,0.028719337657094
0.071,0.0285128429532051
0.07,0.0283059887588024
0.069,0.0281143561005592
0.068,0.0279597174376249
0.067,0.0277798026800155
0.066,0.0276060868054628
0.065,0.0274153165519237
0.064,0.0273119751363992
0.063,0.0271125435829162
0.062,0.0268806964159011
0.061,0.0266905836760997
0.06,0.0265053622424602
0.059,0.0263178925961256
0.058,0.0261192601174116
0.057,0.0259504187852144
0.056,0.0257801432162523
0.055,0.02560356259346
0.054,0.0253967568278312
0.053,0.0252354517579078
0.052,0.0250491071492433
0.051,0.024835892021656
0.05,0.0246330257505178
0.049,0.0244286004453897
0.048,0.0242333654314279
0.047,0.0240776035934686
0.046,0.0238554626703262
0.045,0.023711510002613
0.044,0.0235477648675441
0.043,0.0233605429530143
0.042,0.0231760647147893
0.041,0.022863283753395
0.04,0.022694768384099
0.039,0.0224520917981863
0.038,0.0222039520740509
0.037,0.0219771284610033
0.036,0.0218534991145133
0.035,0.021628325805068
0.034,0.0214509144425392
0.033,0.0211755875498056
0.032,0.021051513031125
0.031,0.0208633355796337
0.03,0.0206362716853618
0.029,0.0203661937266588
0.028,0.0201535653322935
0.027,0.0199709795415401
0.026,0.0196477603167295
0.025,0.019316304475069
0.024,0.0189332962036132
0.023,0.0187167711555957
0.022,0.0184576082974672
0.021,0.0182719882577657
0.02,0.0180836785584688
0.019,0.0178364794701337
0.018,0.0175586380064487
0.017,0.0172259137034416
0.016,0.0169721469283103
0.015,0.0166507456451654
0.014,0.0162105113267898
0.013,0.0159360133111476
0.012,0.0155999613925814
0.011,0.0151478061452507
0.01,0.0148683059960603
0.009,0.0146070262417197
0.008,0.0141427172347903
0.007,0.0135072255507111
0.006,0.0131422383710742
0.005,0.0125411767512559
0.004,0.012156156823039
0.003,0.01137366425246
0.002,0.0103842560201883
0.001,0
"""

#Correlation Model - Name vs Address - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNameAddress_GBM_EXA_EXX_NSR_20220718"
datasci.model.defaults.v_3_0_0.cs_name_address.name="Correlation Model - Name vs Address (US)"
datasci.model.defaults.v_3_0_0.cs_name_address.version="10.1"
datasci.model.defaults.v_3_0_0.cs_name_address.identifier="CorrNameAddress_GBM_EXA_EXX_NSR_20220718"
datasci.model.defaults.v_3_0_0.cs_name_address.params="""{"EXAVL.100029":null,"NSRVL.100012":null,"EXXVL.100301":null}"""
datasci.model.defaults.v_3_0_0.cs_name_address.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_address.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_address.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_address.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_address.model_format="mojo"

#Correlation Model - Name vs Email - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNameEmail_XGB_EXE_FCE_FMV_NSR_SSE_TDE_20220309"
datasci.model.defaults.v_3_0_0.cs_name_email.name="Correlation Model - Name vs Email (US)"
datasci.model.defaults.v_3_0_0.cs_name_email.version="7.0"
datasci.model.defaults.v_3_0_0.cs_name_email.identifier="CorrNameEmail_XGB_EXE_FCE_FMV_NSR_SSE_TDE_20220309"
datasci.model.defaults.v_3_0_0.cs_name_email.params="""{"TDEVL.400004":null,"FCEVL.100087":null,"TDEVL.100051":null,"EXEVL.100048":null,"SSEVL.100048":null,"NSRVL.300001":null,"TDEVL.400005":null,"NSRVL.100014":null,"EXEVL.100038":null,"FMVAL.300002":null,"FMVAL.300003":null,"EXEVL.100017":null,"TDEVL.200007":null}"""
datasci.model.defaults.v_3_0_0.cs_name_email.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_email.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_email.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_email.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_email.model_format="mojo"

#Correlation Model - Name vs Phone - US (V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNamePhone_XGB_EXP_EXX_NSR_20220309"
datasci.model.defaults.v_3_0_0.cs_name_phone.name="Correlation Model - Name vs Phone (US)"
datasci.model.defaults.v_3_0_0.cs_name_phone.version="7.0"
datasci.model.defaults.v_3_0_0.cs_name_phone.identifier="CorrNamePhone_XGB_EXP_EXX_NSR_20220309"
datasci.model.defaults.v_3_0_0.cs_name_phone.params="""{"EXPVL.100029":null,"NSRVL.100013":null,"EXXVL.100302":null,"NSRVL.100000":null,"NSRVL.200005":null}"""
datasci.model.defaults.v_3_0_0.cs_name_phone.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_phone.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_phone.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_phone.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_phone.model_format="mojo"

#Correlation Model - Name vs Address - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_address_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNameAddress_GBM_EXA_EXX_NSR_20220718"
datasci.model.defaults.v_3_0_0.cs_name_address_int.name="Correlation Model - Name vs Address (International)"
datasci.model.defaults.v_3_0_0.cs_name_address_int.version="10.1"
datasci.model.defaults.v_3_0_0.cs_name_address_int.identifier="CorrNameAddress_GBM_EXA_EXX_NSR_20220718"
datasci.model.defaults.v_3_0_0.cs_name_address_int.params="""{"EXAVL.100029":null,"NSRVL.100012":null,"EXXVL.100301":null}"""
datasci.model.defaults.v_3_0_0.cs_name_address_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_address_int.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_address_int.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_address_int.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_address_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_address_int.model_format="mojo"

#Correlation Model - Name vs Email - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_email_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNameEmail_XGB_EXE_FCE_FMV_NSR_SSE_TDE_20220309"
datasci.model.defaults.v_3_0_0.cs_name_email_int.name="Correlation Model - Name vs Email (International)"
datasci.model.defaults.v_3_0_0.cs_name_email_int.version="7.0"
datasci.model.defaults.v_3_0_0.cs_name_email_int.identifier="CorrNameEmail_XGB_EXE_FCE_FMV_NSR_SSE_TDE_20220309"
datasci.model.defaults.v_3_0_0.cs_name_email_int.params="""{"TDEVL.400004":null,"FCEVL.100087":null,"TDEVL.100051":null,"EXEVL.100048":null,"SSEVL.100048":null,"NSRVL.300001":null,"TDEVL.400005":null,"NSRVL.100014":null,"EXEVL.100038":null,"FMVAL.300002":null,"FMVAL.300003":null,"EXEVL.100017":null,"TDEVL.200007":null}"""
datasci.model.defaults.v_3_0_0.cs_name_email_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_email_int.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_email_int.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_email_int.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_email_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_email_int.model_format="mojo"

#Correlation Model - Name vs Phone - International (=V3.0)
datasci.model.defaults.v_3_0_0.cs_name_phone_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/CorrNamePhone_XGB_EXP_EXX_NSR_20220309"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.name="Correlation Model - Name vs Phone (International)"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.version="7.0"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.identifier="CorrNamePhone_XGB_EXP_EXX_NSR_20220309"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.params="""{"EXPVL.100029":null,"NSRVL.100013":null,"EXXVL.100302":null,"NSRVL.100000":null,"NSRVL.200005":null}"""
datasci.model.defaults.v_3_0_0.cs_name_phone_int.positive.threshold=0.65
datasci.model.defaults.v_3_0_0.cs_name_phone_int.negative.threshold=0.20
datasci.model.defaults.v_3_0_0.cs_name_phone_int.username="<EMAIL>"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.password="""ENC(7znx9A8y7mKuhGz8kzSfd3x85Ji9KHVqxXuORoQO9sXlgShqgP8hkdBKaad7X661HC06/2X7jVqzT+ZmuhJxzQ==)"""
datasci.model.defaults.v_3_0_0.cs_name_phone_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.cs_name_phone_int.model_format="mojo"

#Address Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_address_us.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskAddress_XGB_DEV_EXA_FMV_NSR_SMS_VCA_VCI_VNA_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.name="Address Risk Model (US) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.version="7.0"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.identifier="RiskAddress_XGB_DEV_EXA_FMV_NSR_SMS_VCA_VCI_VNA_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.params="""{"VCAVL.240099":null,"EXAVL.100008":null,"SMSVL.100023":null,"NSRVL.400008":null,"VCAVL.220902":null,"FMVAL.300082":null,"SMSVL.100026":null,"VNAVL.200007":null,"VCIVL.220907":null,"NSRVL.400001":null,"FMVAL.900017":null,"NSRVL.400006":null,"FMVAL.300083":null,"SMSVL.100027":null,"EXAVL.100002":null,"FMVAL.300022":null,"VCIVL.220906":null,"SMSVL.100021":null,"NSRVL.400003":null,"NSRVL.400007":null,"EXAVL.100006":null,"EXAVL.100007":null,"EXAVL.100000":null,"EXAVL.100005":null,"SMSVL.100025":null,"VCAVL.250099":null,"DEVAL.100029":null,"SMSVL.100011":null,"VCAVL.200002":null,"VCIVL.220105":null,"VCAVL.200005":null,"SMSVL.100004":null,"FMVAL.900019":null,"SMSVL.100020":null,"NSRVL.400005":null,"DEVAL.100031":null,"FMVAL.900016":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_address_us.low_risk.threshold=0.7510337233543396
datasci.model.defaults.v_3_0_0.pii_risk_address_us.high_risk.threshold=0.844167172908783
datasci.model.defaults.v_3_0_0.pii_risk_address_us.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_address_us.quantile_mapping="""
quantile,score
0.999,0.9841867089271544
0.998,0.9671353697776794
0.997,0.9565958380699158
0.996,0.9480999112129213
0.995,0.9393505454063416
0.994,0.9326708912849426
0.993,0.9261453747749328
0.992,0.9204515814781188
0.991,0.9161317348480223
0.99,0.9101036190986632
0.989,0.9053646326065063
0.988,0.9002053141593933
0.987,0.8949927091598511
0.986,0.8912638425827026
0.985,0.8846305012702942
0.984,0.8809611201286316
0.983,0.8772628903388977
0.982,0.873691201210022
0.981,0.8701662421226501
0.98,0.8671857118606567
0.979,0.864138662815094
0.978,0.8585741519927979
0.977,0.8580392599105835
0.975,0.8580297827720642
0.973,0.8543660044670105
0.972,0.8509525656700134
0.971,0.8472552299499512
0.97,0.844167172908783
0.969,0.8441534042358398
0.968,0.8417407274246216
0.967,0.8392245173454285
0.966,0.8368319869041443
0.965,0.833855390548706
0.964,0.8317813277244568
0.963,0.8296879529953003
0.962,0.8273279070854187
0.961,0.8253874182701111
0.96,0.8233518004417419
0.959,0.821662187576294
0.958,0.8197885751724243
0.957,0.8184704184532166
0.956,0.8168404698371887
0.955,0.815255343914032
0.954,0.813923716545105
0.953,0.8126425743103027
0.952,0.8114075064659119
0.951,0.8102093935012817
0.95,0.8088898062705994
0.949,0.8075861930847168
0.948,0.8060550093650818
0.947,0.8049739599227905
0.946,0.8036089539527893
0.945,0.802669882774353
0.944,0.8014019727706909
0.943,0.8002526164054871
0.942,0.7988056540489197
0.941,0.7973110675811768
0.94,0.7962175607681274
0.939,0.7950422167778015
0.938,0.7939891219139099
0.937,0.793268620967865
0.936,0.7929186224937439
0.935,0.7923200726509094
0.934,0.7911103367805481
0.933,0.7899804711341858
0.932,0.7889124155044556
0.931,0.7875193357467651
0.93,0.7862673997879028
0.929,0.7855590581893921
0.928,0.784477174282074
0.927,0.7828498482704163
0.926,0.7822381854057312
0.925,0.7811525464057922
0.924,0.7800905704498291
0.923,0.7790059447288513
0.922,0.7778918743133545
0.921,0.7766749262809753
0.92,0.7756417989730835
0.919,0.7742270231246948
0.918,0.7734388709068298
0.917,0.7726202011108398
0.916,0.7718141078948975
0.915,0.7707961201667786
0.914,0.7696170210838318
0.913,0.7686772346496582
0.912,0.7674049735069275
0.911,0.766402006149292
0.91,0.7651528120040894
0.909,0.7635332345962524
0.908,0.7622884511947632
0.907,0.7611088156700134
0.906,0.7600050568580627
0.905,0.7583280801773071
0.904,0.7568359971046448
0.903,0.7555179595947266
0.902,0.7541468739509583
0.901,0.7525314688682556
0.9,0.7510337233543396
0.899,0.7497481107711792
0.898,0.7483746409416199
0.897,0.7472361326217651
0.896,0.7460102438926697
0.895,0.7446846961975098
0.894,0.7434784770011902
0.893,0.7423617839813232
0.892,0.7409403324127197
0.891,0.7396239638328552
0.89,0.7385638356208801
0.889,0.7372404336929321
0.888,0.7359375953674316
0.887,0.7348250150680542
0.886,0.7336435914039612
0.885,0.7321699857711792
0.884,0.7306428551673889
0.883,0.7297672033309937
0.882,0.7292856574058533
0.881,0.7281319499015808
0.88,0.726807177066803
0.879,0.7253257036209106
0.878,0.7240905165672302
0.877,0.7228142619132996
0.876,0.7216954231262207
0.875,0.7204180359840393
0.874,0.7194365859031677
0.873,0.7184605002403259
0.872,0.7172621488571167
0.871,0.7162494659423828
0.87,0.714948832988739
0.869,0.713965654373169
0.868,0.7122678756713867
0.867,0.7110304832458496
0.866,0.7099546790122986
0.865,0.7082321047782898
0.864,0.7072537541389465
0.863,0.7058095932006836
0.862,0.7047653198242188
0.861,0.7040078639984131
0.86,0.7033730149269104
0.859,0.7020277380943298
0.858,0.700954020023346
0.857,0.6999290585517883
0.856,0.698675274848938
0.855,0.6974000334739685
0.854,0.6963090300559998
0.853,0.6948112845420837
0.852,0.6941421627998352
0.851,0.6929053068161011
0.85,0.6915296316146851
0.849,0.6902632713317871
0.848,0.689300537109375
0.847,0.6876311898231506
0.846,0.686196506023407
0.845,0.6854322552680969
0.844,0.6840457916259766
0.843,0.6829443573951721
0.842,0.6816017031669617
0.841,0.6804499626159668
0.84,0.6791226863861084
0.839,0.6775773763656616
0.838,0.6761483550071716
0.837,0.6750684380531311
0.836,0.6736717224121094
0.835,0.6724806427955627
0.834,0.6713582277297974
0.833,0.6701507568359375
0.832,0.6688261032104492
0.831,0.6678858995437622
0.83,0.6668569445610046
0.829,0.6657378077507019
0.828,0.6644701957702637
0.827,0.6633434295654297
0.826,0.6619994044303894
0.825,0.6607838869094849
0.824,0.6594918966293335
0.823,0.6587854623794556
0.822,0.6574918031692505
0.821,0.6563200950622559
0.82,0.6553702354431152
0.819,0.6538453698158264
0.818,0.6525734663009644
0.817,0.6510637998580933
0.816,0.6499423384666443
0.815,0.6486318707466125
0.814,0.6475381255149841
0.813,0.64621901512146
0.812,0.645358681678772
0.811,0.6443989872932434
0.81,0.643058717250824
0.809,0.6415638327598572
0.808,0.640244722366333
0.807,0.6392203569412231
0.806,0.6383376717567444
0.805,0.6369950175285339
0.804,0.6359436511993408
0.803,0.6343027353286743
0.802,0.6332067847251892
0.801,0.6319666504859924
0.8,0.6308748722076416
0.799,0.630181610584259
0.798,0.6287649273872375
0.797,0.6278054118156433
0.796,0.6262039542198181
0.795,0.6250312328338623
0.794,0.6239308714866638
0.793,0.6231303215026855
0.792,0.6219234466552734
0.791,0.620730459690094
0.79,0.6194250583648682
0.789,0.6183837652206421
0.788,0.6173070669174194
0.787,0.616182804107666
0.786,0.6151554584503174
0.785,0.6143284440040588
0.784,0.6131735444068909
0.783,0.6123083233833313
0.782,0.6110999584197998
0.781,0.6101534366607666
0.78,0.6093289852142334
0.779,0.6081274151802063
0.778,0.6070018410682678
0.777,0.6060560941696167
0.776,0.6050764918327332
0.775,0.6040632128715515
0.774,0.6027572154998779
0.773,0.6020939946174622
0.772,0.601051926612854
0.771,0.6000850796699524
0.77,0.5992435216903687
0.769,0.5981319546699524
0.768,0.5970686078071594
0.767,0.5962061285972595
0.766,0.5955637097358704
0.765,0.5946596264839172
0.764,0.5940322279930115
0.763,0.5934280157089233
0.762,0.5928024649620056
0.761,0.5918314456939697
0.76,0.5906813740730286
0.759,0.5899809002876282
0.758,0.5892319083213806
0.757,0.5883240699768066
0.756,0.5878224968910217
0.755,0.5871281623840332
0.754,0.5868812799453735
0.753,0.5852323174476624
0.751,0.5847119092941284
0.75,0.5838718414306641
0.749,0.58307284116745
0.748,0.5826490521430969
0.747,0.5817070007324219
0.746,0.5812944769859314
0.745,0.579999566078186
0.744,0.5796923041343689
0.743,0.5772121548652649
0.741,0.5766417384147644
0.74,0.5757039189338684
0.739,0.5746272802352905
0.738,0.5738400816917419
0.737,0.5725991725921631
0.736,0.5718294382095337
0.735,0.5710147619247437
0.734,0.5694212317466736
0.733,0.5690028071403503
0.732,0.5682955980300903
0.731,0.5674760341644287
0.73,0.5668916702270508
0.729,0.5656755566596985
0.728,0.5644910335540771
0.727,0.5635544061660767
0.726,0.5627771615982056
0.725,0.5622852444648743
0.724,0.5622666478157043
0.723,0.5603644251823425
0.721,0.5586442947387695
0.72,0.557656466960907
0.719,0.5569208264350891
0.718,0.556048572063446
0.717,0.5558198094367981
0.716,0.5548899173736572
0.715,0.5545734167098999
0.714,0.554020881652832
0.713,0.5539242029190063
0.712,0.5523980259895325
0.711,0.5521040558815002
0.708,0.5495017766952515
0.707,0.5488787889480591
0.706,0.5482320189476013
0.705,0.5478039383888245
0.704,0.5467863082885742
0.703,0.5458541512489319
0.702,0.5453410148620605
0.701,0.5443916320800781
0.699,0.5428951382637024
0.698,0.5423058867454529
0.697,0.5413841605186462
0.696,0.5410618782043457
0.695,0.539990246295929
0.694,0.5395151972770691
0.693,0.5387111902236938
0.692,0.5385069847106934
0.691,0.5373117923736572
0.69,0.5367673635482788
0.689,0.5361343622207642
0.688,0.5354726910591125
0.687,0.5347975492477417
0.686,0.5342736840248108
0.685,0.5336644649505615
0.684,0.5332489609718323
0.683,0.5321633815765381
0.682,0.5318541526794434
0.679,0.5288530588150024
0.678,0.5281866788864136
0.677,0.5276082158088684
0.676,0.5270749926567078
0.675,0.5265218615531921
0.674,0.5257906913757324
0.673,0.5254903435707092
0.672,0.5251528024673462
0.67,0.5231626033782959
0.669,0.5226805210113525
0.668,0.5219401121139526
0.667,0.5214816927909851
0.666,0.5205666422843933
0.665,0.5200191736221313
0.664,0.5197328925132751
0.662,0.5178512334823608
0.661,0.5173823237419128
0.66,0.5169334411621094
0.659,0.5166772603988647
0.658,0.5159091949462891
0.657,0.5153113603591919
0.656,0.5148261189460754
0.655,0.5142725706100464
0.654,0.5139093399047852
0.653,0.5128160119056702
0.652,0.51249760389328
0.651,0.5120019316673279
0.65,0.5114155411720276
0.649,0.5109339952468872
0.648,0.5105747580528259
0.647,0.5101292729377747
0.646,0.5097319483757019
0.645,0.5095212459564209
0.644,0.5090538859367371
0.643,0.5084915161132812
0.642,0.5079628229141235
0.641,0.5074138641357422
0.64,0.5068644285202026
0.639,0.5063470005989075
0.638,0.5059133172035217
0.637,0.5055029988288879
0.636,0.5050285458564758
0.635,0.5046903491020203
0.634,0.504486083984375
0.633,0.5041370391845703
0.632,0.5035680532455444
0.631,0.5031883716583252
0.63,0.5025523900985718
0.629,0.5021125078201294
0.628,0.5016189217567444
0.627,0.501374363899231
0.626,0.5011905431747437
0.625,0.5008888840675354
0.624,0.5004554986953735
0.623,0.5001555681228638
0.622,0.4997461438179016
0.621,0.4993517398834228
0.62,0.4987897574901581
0.619,0.4983581304550171
0.618,0.497856080532074
0.617,0.4974470138549804
0.616,0.4973752796649933
0.614,0.4961567223072052
0.613,0.4950810670852661
0.612,0.4947459697723388
0.611,0.4941387474536896
0.61,0.493349552154541
0.609,0.4924066960811615
0.608,0.4920897185802459
0.607,0.4916504621505737
0.606,0.4913220107555389
0.605,0.4913220107555389
0.604,0.4900254309177398
0.603,0.489195168018341
0.602,0.4887000024318695
0.601,0.4884435832500458
0.6,0.4879011809825897
0.599,0.4877022802829742
0.597,0.4864908754825592
0.596,0.4860582947731018
0.595,0.4855389595031738
0.594,0.4852236807346344
0.593,0.4847365319728851
0.592,0.4846073091030121
0.591,0.4842548966407776
0.59,0.4837513267993927
0.589,0.4837223291397095
0.588,0.4830213189125061
0.587,0.4826250374317169
0.586,0.4819003343582153
0.585,0.4813949763774872
0.584,0.4807997941970825
0.583,0.4803619086742401
0.582,0.480179876089096
0.581,0.479922741651535
0.58,0.4797545373439789
0.579,0.4795281291007995
0.578,0.4791017174720764
0.577,0.4786993563175201
0.576,0.4783927798271179
0.575,0.4779804348945617
0.571,0.477324366569519
0.567,0.4771287143230438
0.566,0.4767566621303558
0.565,0.476616233587265
0.563,0.4752699732780456
0.562,0.4750823974609375
0.561,0.4749937355518341
0.56,0.4747730195522308
0.559,0.474547803401947
0.558,0.4742194414138794
0.557,0.4735279381275177
0.556,0.473286360502243
0.555,0.4727378189563751
0.554,0.4724656641483307
0.553,0.4719079732894897
0.552,0.4713526666164398
0.551,0.4712904393672943
0.549,0.4710314273834228
0.548,0.4707735180854797
0.547,0.4702586829662323
0.546,0.470150738954544
0.545,0.4699408710002899
0.544,0.4695769548416137
0.543,0.4691424071788788
0.542,0.4685269594192505
0.541,0.4681124985218048
0.54,0.4676235020160675
0.539,0.467164009809494
0.538,0.4665537178516388
0.537,0.4662676453590393
0.536,0.4659675061702728
0.535,0.4651024341583252
0.534,0.4648610353469848
0.533,0.464786559343338
0.532,0.4644763469696045
0.531,0.4637297093868255
0.53,0.4632822275161743
0.529,0.4626772999763489
0.528,0.4626027941703796
0.527,0.4618973433971405
0.526,0.4612491428852081
0.525,0.4612491428852081
0.524,0.4612491428852081
0.523,0.4609846770763397
0.522,0.4609217345714569
0.521,0.4600669145584106
0.52,0.4598170518875122
0.519,0.4593871235847473
0.518,0.4592302739620209
0.517,0.4583720862865448
0.516,0.4578678011894226
0.515,0.4574329257011413
0.514,0.4570189416408539
0.513,0.4565095603466034
0.512,0.4561980366706848
0.511,0.4561980366706848
0.51,0.4559713006019592
0.509,0.4555000960826874
0.508,0.4552175998687744
0.482,0.4552138149738312
0.455,0.4390663206577301
0.454,0.4385444521903991
0.453,0.4382203221321106
0.452,0.4378570020198822
0.451,0.4375122189521789
0.45,0.4372525513172149
0.449,0.4368767142295837
0.448,0.4364601075649261
0.447,0.4362148940563202
0.446,0.4360783398151397
0.445,0.4355460405349731
0.444,0.435145229101181
0.443,0.4345017075538635
0.442,0.4343093037605285
0.441,0.4341831505298614
0.398,0.4198526740074157
0.397,0.4192555844783783
0.396,0.4187971651554107
0.395,0.4183405339717865
0.394,0.4179874360561371
0.393,0.4174931943416595
0.392,0.4173842966556549
0.39,0.4165166914463043
0.389,0.4158720374107361
0.388,0.4153600931167602
0.387,0.4151634275913238
0.386,0.4151634275913238
0.385,0.4150676131248474
0.384,0.4143587350845337
0.383,0.4140152335166931
0.382,0.4138211905956268
0.381,0.413281112909317
0.38,0.4129895865917206
0.379,0.4128384292125702
0.378,0.4120583534240722
0.377,0.411532998085022
0.376,0.4113496840000152
0.374,0.4088398814201355
0.371,0.4084307849407196
0.37,0.4077741503715515
0.369,0.4073934853076935
0.367,0.4061225056648254
0.366,0.405902087688446
0.365,0.4053327739238739
0.364,0.4048140347003937
0.363,0.4043448567390442
0.362,0.4038760960102081
0.361,0.4037951231002807
0.36,0.403202623128891
0.359,0.402587741613388
0.358,0.4023493528366089
0.357,0.4016463458538055
0.356,0.4011945724487304
0.355,0.4008672833442688
0.354,0.3994217216968536
0.352,0.3991900384426117
0.351,0.3989839255809784
0.349,0.3972309529781341
0.348,0.3971626162528991
0.347,0.3968240618705749
0.346,0.3957080841064453
0.345,0.3954663276672363
0.344,0.3940206170082092
0.343,0.393769770860672
0.342,0.3934316635131836
0.34,0.3910529613494873
0.338,0.3909223079681396
0.337,0.3902637064456939
0.336,0.3895451128482818
0.335,0.3886074423789978
0.334,0.3879804015159607
0.333,0.3876371085643768
0.332,0.3868022263050079
0.331,0.386339783668518
0.33,0.3859055638313293
0.329,0.3858966827392578
0.328,0.3839933574199676
0.327,0.3833908140659332
0.326,0.3828345835208893
0.325,0.3825173676013946
0.323,0.3825170695781708
0.321,0.3800546526908874
0.32,0.3794945776462555
0.319,0.3790258765220642
0.318,0.3790258765220642
0.317,0.3778446316719055
0.316,0.3770728409290313
0.315,0.3760698437690735
0.314,0.375379741191864
0.313,0.3742486238479614
0.312,0.3734982311725616
0.311,0.3726317882537842
0.31,0.3719231188297272
0.309,0.3710330128669739
0.308,0.3706375956535339
0.307,0.3700031638145447
0.306,0.3688889741897583
0.305,0.3680811524391174
0.304,0.3670027256011963
0.303,0.3663473427295685
0.302,0.3656936883926391
0.301,0.3643123805522918
0.3,0.3636693358421325
0.299,0.3630207180976867
0.298,0.3619307577610016
0.297,0.3604979217052459
0.296,0.3592165112495422
0.295,0.3584937751293182
0.294,0.3575043678283691
0.293,0.356609046459198
0.292,0.3557910621166229
0.291,0.3550219535827636
0.29,0.3536334931850433
0.289,0.3524174094200134
0.288,0.351435512304306
0.287,0.350456178188324
0.286,0.349705308675766
0.285,0.3487189710140228
0.284,0.3476707339286804
0.283,0.3467379510402679
0.282,0.3464096188545227
0.281,0.3453386425971985
0.28,0.3445061445236206
0.279,0.3434670269489288
0.278,0.3423522412776947
0.277,0.3414378762245178
0.276,0.3402759432792663
0.275,0.3391590714454651
0.274,0.3380855321884155
0.273,0.3373854756355285
0.272,0.3363674879074096
0.271,0.3353987038135528
0.27,0.3342801928520202
0.269,0.3331377506256103
0.268,0.3324795365333557
0.267,0.3314972519874573
0.266,0.3304246366024017
0.265,0.3293551802635193
0.264,0.3285572826862335
0.263,0.327460378408432
0.262,0.3264891505241394
0.261,0.3255650699138641
0.26,0.3249039649963379
0.259,0.3239560127258301
0.258,0.3233616948127746
0.257,0.3224019706249237
0.256,0.3215309083461761
0.255,0.3208015263080597
0.254,0.3192443251609802
0.253,0.3182434737682342
0.252,0.3173278868198395
0.251,0.3167174458503723
0.25,0.3155567348003387
0.249,0.3147612512111664
0.248,0.3137987852096557
0.247,0.312663197517395
0.246,0.3115901052951813
0.245,0.3107454776763916
0.244,0.3093235492706299
0.243,0.3085197508335113
0.242,0.3075051307678222
0.241,0.3068921267986297
0.24,0.3058412075042724
0.239,0.3045948147773742
0.238,0.3037171959877014
0.237,0.3026918172836303
0.236,0.3020647764205932
0.235,0.3011580109596252
0.234,0.2999331057071686
0.233,0.2988470494747162
0.232,0.2978286743164062
0.231,0.2966750264167785
0.23,0.2957430481910705
0.229,0.2947779893875122
0.228,0.2937696278095245
0.227,0.2923130989074707
0.226,0.2909323275089264
0.225,0.2903180420398712
0.224,0.2892031371593475
0.223,0.2881531417369842
0.222,0.28776615858078
0.221,0.2868492007255554
0.22,0.2858010828495025
0.219,0.2843529880046844
0.218,0.2833906412124634
0.217,0.2819778025150299
0.216,0.2809098958969116
0.215,0.2794640362262726
0.214,0.2785099446773529
0.213,0.2770164012908935
0.212,0.2758975625038147
0.211,0.2750003635883331
0.21,0.2739079296588897
0.209,0.2729027569293976
0.208,0.2719548046588897
0.207,0.2714033722877502
0.206,0.2701607644557953
0.205,0.2692139744758606
0.204,0.2686191201210022
0.203,0.2676388025283813
0.202,0.2667694389820099
0.201,0.2659389078617096
0.2,0.2656782567501068
0.199,0.2643085122108459
0.198,0.2635880708694458
0.197,0.2624340653419494
0.196,0.2615030407905578
0.195,0.2601885795593261
0.194,0.2591773569583893
0.193,0.2591773569583893
0.192,0.2584108412265777
0.191,0.2572270035743713
0.19,0.2562382519245147
0.189,0.2554630637168884
0.188,0.2553631365299225
0.187,0.2545172572135925
0.186,0.2537376582622528
0.185,0.2530028223991394
0.184,0.251499593257904
0.183,0.2506126463413238
0.182,0.2495378106832504
0.181,0.2485974878072738
0.18,0.2475094497203827
0.179,0.2470146566629409
0.178,0.2455194145441055
0.177,0.2444016635417938
0.176,0.2436525672674179
0.175,0.2436525672674179
0.174,0.2420459538698196
0.173,0.2407802939414978
0.172,0.2400886714458465
0.171,0.239445686340332
0.17,0.2383622974157333
0.169,0.2368773519992828
0.168,0.2361116111278534
0.167,0.2348381429910659
0.166,0.2338552922010421
0.165,0.2327072322368621
0.164,0.2316182255744934
0.163,0.2305165082216262
0.162,0.2294174283742904
0.161,0.2287155836820602
0.16,0.2276404052972793
0.159,0.2269460558891296
0.157,0.2242731302976608
0.156,0.2241233736276626
0.155,0.2229226976633072
0.154,0.2223510593175888
0.153,0.2213257700204849
0.152,0.2210318744182586
0.151,0.2197437733411789
0.15,0.2186034917831421
0.149,0.2179346531629562
0.148,0.2169902771711349
0.147,0.2161994129419326
0.146,0.2154754698276519
0.145,0.2143571078777313
0.144,0.2137061655521392
0.143,0.2129240483045578
0.142,0.2122690975666046
0.141,0.2109076678752899
0.14,0.2097185403108596
0.139,0.2088255435228347
0.138,0.2078585922718048
0.137,0.2070178389549255
0.136,0.2055594027042389
0.135,0.2047089189291
0.134,0.2036516815423965
0.133,0.2027109712362289
0.132,0.2015277594327926
0.131,0.2004994601011276
0.13,0.1996637731790542
0.129,0.1990932822227478
0.128,0.1976166665554046
0.127,0.197035476565361
0.126,0.1960436254739761
0.125,0.195494458079338
0.123,0.1911619007587433
0.121,0.190692514181137
0.12,0.1901394724845886
0.118,0.1878801584243774
0.117,0.1872794777154922
0.116,0.1858127862215042
0.115,0.1847635060548782
0.114,0.1842886507511139
0.113,0.1828397512435913
0.112,0.1826957911252975
0.109,0.1781761348247528
0.108,0.1771362125873565
0.107,0.176437109708786
0.106,0.1752899140119552
0.105,0.1741855144500732
0.104,0.1740134358406067
0.101,0.1703957021236419
0.1,0.1690742075443267
0.099,0.1688728332519531
0.097,0.1688727736473083
0.095,0.1656427383422851
0.094,0.1626845896244049
0.092,0.1613146662712097
0.091,0.1602694392204284
0.09,0.1593477874994278
0.089,0.1586101800203323
0.088,0.1573435366153717
0.087,0.1562521159648895
0.086,0.1552959978580474
0.085,0.154971107840538
0.083,0.153142362833023
0.081,0.1531272977590561
0.08,0.149332582950592
0.079,0.1482055038213729
0.078,0.1473854333162307
0.073,0.138542965054512
0.068,0.1383069455623626
0.067,0.1366936117410659
0.066,0.1359047144651413
0.065,0.1353534162044525
0.057,0.1264935582876205
0.056,0.1261838674545288
0.055,0.1241492480039596
0.054,0.1231422945857048
0.053,0.1230346411466598
0.05,0.1230278834700584
0.046,0.1144565939903259
0.045,0.1129436790943145
0.044,0.111915722489357
0.043,0.1116744801402092
0.042,0.1110162287950515
0.041,0.1097118780016899
0.04,0.1089324802160263
0.039,0.1064961478114128
0.038,0.105569452047348
0.037,0.10395497828722
0.036,0.1036033555865287
0.034,0.1003430932760238
0.033,0.100087970495224
0.032,0.1000854074954986
0.03,0.0958704426884651
0.029,0.0947248190641403
0.028,0.0940927192568779
0.027,0.0915341302752494
0.026,0.0899812951683998
0.025,0.0887722000479698
0.022,0.0845418646931648
0.021,0.0837126225233078
0.02,0.0815801173448562
0.019,0.079048216342926
0.018,0.0787089169025421
0.017,0.0761246457695961
0.016,0.0721418559551239
0.015,0.0705944523215293
0.014,0.0683925524353981
0.013,0.0653340592980384
0.012,0.0622953698039054
0.011,0.0594903938472271
0.009,0.0511208958923816
0.008,0.0483747236430645
0.007,0.045796450227499
0.006,0.0433907993137836
0.005,0.0430777855217456
0.004,0.032389298081398
0.003,0.028358731418848
0.002,0.0237598251551389
0.001,0.0
"""

#Email Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_email_us.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskEmail_XGB_DEV_EXE_FCE_FMV_NSR_SSE_TDE_VCE_20220311"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.name="Email Risk Model (US) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.version="8.0"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.identifier="RiskEmail_XGB_DEV_EXE_FCE_FMV_NSR_SSE_TDE_VCE_20220311"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.params="""{"EXEVL.100006":null,"TDEVL.200008":null,"TDEVL.500005":null,"SSEVL.100005":null,"FCEVL.100015":null,"FCEVL.100069":null,"FCEVL.100036":null,"TDEVL.100007":null,"FCEVL.100000":null,"FCEVL.100004":null,"FCEVL.100058":null,"TDEVL.100006":null,"DEVAL.100061":null,"EXEVL.100036":null,"SSEVL.100031":null,"NSRVL.300005":null,"SSEVL.100006":null,"SSEVL.100019":null,"FMVAL.300003":null,"TDEVL.200007":null,"FCEVL.100008":null,"EXEVL.100025":null,"DEVAL.100034":null,"NSRVL.300008":null,"EXEVL.100013":null,"FMVAL.600027":null,"SSEVL.100007":null,"SSEVL.100029":null,"FCEVL.100017":null,"FCEVL.100007":null,"TDEVL.400005":null,"EXEVL.100028":null,"SSEVL.100033":null,"TDEVL.100004":null,"TDEVL.100016":null,"EXEVL.100015":null,"DEVAL.100004":null,"FCEVL.100019":null,"TDEVL.400004":null,"VCEVL.200006":null,"DEVAL.100045":null,"EXEVL.100011":null,"FCEVL.100006":null,"EXEVL.100023":null,"FMVAL.600013":null,"FMVAL.600025":null,"FCEVL.100068":null,"DEVAL.100053":null,"TDEVL.100005":null,"DEVAL.100052":null,"NSRVL.100009":null,"SSEVL.100004":null,"NSRVL.300001":null,"FMVAL.600012":null,"FCEVL.100067":null,"EXEVL.100037":null,"SSEVL.100001":null,"FCEVL.100002":null,"EXEVL.100012":null,"VCEVL.200009":null,"FCEVL.100018":null,"EXEVL.100022":null,"SSEVL.100002":null,"EXEVL.100033":null,"FCEVL.100001":null,"EXEVL.100004":null,"FCEVL.100005":null,"EXEVL.100024":null,"VCEVL.211106":null,"EXEVL.100002":null,"VCEVL.240099":null,"TDEVL.100002":null,"NSRVL.300004":null,"FMVAL.600001":null,"TDEVL.100008":null,"TDEVL.400001":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_email_us.low_risk.threshold=0.9745648503303528
datasci.model.defaults.v_3_0_0.pii_risk_email_us.high_risk.threshold=0.9892882108688354
datasci.model.defaults.v_3_0_0.pii_risk_email_us.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_email_us.quantile_mapping="""
quantile,score
0.999,0.9946267008781432
0.998,0.9941422343254088
0.997,0.9939078092575072
0.996,0.9936543703079224
0.995,0.9933078289031982
0.994,0.9928594827651978
0.993,0.9926164746284484
0.992,0.9923700094223022
0.991,0.9921448826789856
0.99,0.9919346570968628
0.989,0.9917451739311218
0.988,0.991550087928772
0.987,0.9914186000823976
0.986,0.9912680387496948
0.985,0.9911590218544006
0.984,0.9909895658493042
0.983,0.9908437728881836
0.982,0.9907466173171996
0.981,0.990631341934204
0.98,0.990512192249298
0.979,0.990391969680786
0.978,0.9902495741844176
0.977,0.9901071786880492
0.976,0.9900012016296388
0.975,0.9898830056190492
0.974,0.9897537231445312
0.973,0.9896174669265748
0.972,0.9895116686820984
0.971,0.9894078969955444
0.97,0.9892882108688354
0.969,0.9891441464424132
0.968,0.9890426993370056
0.967,0.988934338092804
0.966,0.9888303875923156
0.965,0.9886571764945984
0.964,0.9885286688804626
0.963,0.9883508682250975
0.962,0.9882105588912964
0.961,0.9880141615867616
0.96,0.9878408312797546
0.959,0.9876922965049744
0.958,0.987542450428009
0.957,0.9873961806297302
0.956,0.9871972799301147
0.955,0.9870063066482544
0.954,0.986825168132782
0.953,0.9866699576377868
0.952,0.9865277409553528
0.951,0.9863852858543396
0.95,0.9862213134765624
0.949,0.9860799908638
0.948,0.9859551787376404
0.947,0.9857926964759828
0.946,0.9856138825416564
0.945,0.9854715466499328
0.944,0.9852983951568604
0.943,0.9851674437522888
0.942,0.984972596168518
0.941,0.9848072528839112
0.94,0.9845906496047974
0.939,0.9844592809677124
0.938,0.9842764139175416
0.937,0.9840736389160156
0.936,0.9838191866874696
0.935,0.983623206615448
0.934,0.9833973050117492
0.933,0.9832599759101868
0.932,0.9831017851829528
0.931,0.9829323291778564
0.93,0.9827894568443298
0.929,0.9825804829597472
0.928,0.9823681712150574
0.927,0.9822030067443848
0.926,0.9819703102111816
0.925,0.9817264676094056
0.924,0.9815250635147096
0.923,0.9813343286514282
0.922,0.9811018109321594
0.921,0.9808236360549928
0.92,0.9805893898010254
0.919,0.980322539806366
0.918,0.9800801873207092
0.917,0.979855179786682
0.916,0.9796263575553894
0.915,0.97941255569458
0.914,0.9791169762611388
0.913,0.9788418412208556
0.912,0.9786044359207152
0.911,0.9783541560173036
0.91,0.9780129790306092
0.909,0.9777878522872924
0.908,0.9774213433265686
0.907,0.9770734310150146
0.906,0.9767309427261353
0.905,0.9763656854629515
0.904,0.976024091243744
0.903,0.9757180213928224
0.902,0.9752996563911438
0.901,0.9749324321746826
0.9,0.9745648503303528
0.899,0.97408527135849
0.898,0.9737713932991028
0.897,0.9733358025550842
0.896,0.972984254360199
0.895,0.9726294279098512
0.894,0.9721883535385132
0.893,0.971786618232727
0.892,0.9713882803916932
0.891,0.9709389805793762
0.89,0.970458686351776
0.889,0.9700177907943726
0.888,0.969353199005127
0.887,0.9688066244125366
0.886,0.9682040214538574
0.885,0.9676923751831056
0.884,0.9671686887741088
0.883,0.9666807651519777
0.882,0.9662367105484008
0.881,0.9657093286514282
0.88,0.9651607871055604
0.879,0.9646402597427368
0.878,0.964103639125824
0.877,0.9635515809059144
0.876,0.963074028491974
0.875,0.962611436843872
0.874,0.9620861411094666
0.873,0.961527705192566
0.872,0.9610896706581116
0.871,0.9606165289878844
0.87,0.9601516723632812
0.869,0.959604799747467
0.868,0.9591064453125
0.867,0.9586076736450196
0.866,0.9581841230392456
0.865,0.9576879143714904
0.864,0.9571743607521056
0.863,0.9567953944206238
0.862,0.9564078450202942
0.861,0.9560911655426024
0.86,0.9557766318321228
0.859,0.9554212093353271
0.858,0.95507150888443
0.857,0.9547101855278016
0.856,0.9544256329536438
0.855,0.9541158080101012
0.854,0.953783631324768
0.853,0.9534220099449158
0.852,0.9530594348907472
0.851,0.9527631402015686
0.85,0.95237398147583
0.849,0.9520647525787354
0.848,0.951783299446106
0.847,0.9514157772064208
0.846,0.9511101841926576
0.845,0.950839638710022
0.844,0.950522243976593
0.843,0.9502573013305664
0.842,0.9499216675758362
0.841,0.949825406074524
0.84,0.9494733214378356
0.839,0.9491612315177916
0.838,0.9488497376441956
0.837,0.9485188722610474
0.836,0.9482558369636536
0.835,0.947942316532135
0.834,0.9476398825645448
0.833,0.9473448395729064
0.832,0.9470798373222352
0.831,0.9467175602912904
0.83,0.946483314037323
0.829,0.94620019197464
0.828,0.9459283351898192
0.827,0.9455904364585876
0.826,0.9453554749488832
0.825,0.9450703859329224
0.824,0.944724142551422
0.823,0.9444551467895508
0.822,0.944223165512085
0.821,0.9439858198165894
0.82,0.9436535239219666
0.819,0.9433906674385072
0.818,0.9430685639381408
0.817,0.9428114295005798
0.816,0.9425106644630432
0.815,0.9420989155769348
0.814,0.9417322278022766
0.813,0.9413938522338868
0.812,0.9411975145339966
0.811,0.9408788084983826
0.81,0.9406023621559144
0.809,0.9402875304222108
0.808,0.9399738907814026
0.807,0.9396948218345642
0.806,0.9394031167030334
0.805,0.9391191005706788
0.804,0.9389050006866456
0.803,0.9386656284332277
0.802,0.9383087754249572
0.801,0.938056707382202
0.8,0.9377169609069824
0.799,0.9374003410339355
0.798,0.9371749758720398
0.797,0.9368811249732972
0.796,0.936603844165802
0.795,0.936284065246582
0.794,0.9359871745109558
0.793,0.935653805732727
0.792,0.9353604316711426
0.791,0.9350371360778807
0.79,0.9347106218338012
0.789,0.9343411326408386
0.788,0.9340525269508362
0.787,0.9337401986122132
0.786,0.9334561228752136
0.785,0.9331777095794678
0.784,0.93303781747818
0.783,0.9327256679534912
0.782,0.9323861002922058
0.781,0.9321202635765076
0.78,0.9318429827690125
0.779,0.9315268397331238
0.778,0.9312520623207092
0.777,0.9309282898902892
0.776,0.9307153224945068
0.775,0.9303430318832396
0.774,0.9299681782722472
0.773,0.9297068119049072
0.772,0.92929208278656
0.771,0.9289279580116272
0.77,0.9285811185836792
0.769,0.9282383918762208
0.768,0.927872359752655
0.767,0.9276033043861388
0.766,0.927316427230835
0.765,0.9269307255744934
0.764,0.9265618324279784
0.763,0.9262419939041138
0.762,0.925917625427246
0.761,0.9255992770195008
0.76,0.925283908843994
0.759,0.9248773455619812
0.758,0.92466938495636
0.757,0.9242452383041382
0.756,0.9239715933799744
0.755,0.9235526919364928
0.754,0.9232332110404968
0.753,0.9229488968849182
0.752,0.922612190246582
0.751,0.9222684502601624
0.75,0.921894371509552
0.749,0.9214819669723512
0.748,0.9210475087165833
0.747,0.9206750988960266
0.746,0.9203029870986938
0.745,0.919889271259308
0.744,0.9195270538330078
0.743,0.919152557849884
0.742,0.9186684489250184
0.741,0.9182680249214172
0.74,0.9178873300552368
0.739,0.9174380898475648
0.738,0.9170482158660888
0.737,0.9166671633720398
0.736,0.9162437915802002
0.735,0.9158461093902588
0.734,0.9154471755027772
0.733,0.9150746464729308
0.732,0.9146281480789183
0.731,0.91416198015213
0.73,0.9136999249458312
0.729,0.9133175611495972
0.728,0.9128249883651732
0.727,0.9123337864875792
0.726,0.91189444065094
0.725,0.9115320444107056
0.724,0.9110684394836426
0.723,0.910651445388794
0.722,0.9102177023887634
0.721,0.9098336696624756
0.72,0.9092993140220642
0.719,0.9090060591697692
0.718,0.908608615398407
0.717,0.9081773161888124
0.716,0.9077006578445436
0.715,0.9071677327156068
0.714,0.9067059755325316
0.713,0.9062731862068176
0.712,0.905907154083252
0.711,0.905440866947174
0.71,0.9049742221832277
0.709,0.9044575691223145
0.708,0.9039674997329712
0.707,0.9035214185714722
0.706,0.9030529856681824
0.705,0.9026548266410828
0.704,0.9021986722946168
0.703,0.9018136858940125
0.702,0.9013809561729432
0.701,0.90091073513031
0.7,0.9005568623542786
0.699,0.9000608921051025
0.698,0.8996601104736328
0.697,0.8992281556129456
0.696,0.8987516164779663
0.695,0.898387610912323
0.694,0.8979465961456299
0.693,0.8974243998527527
0.692,0.8969859480857849
0.691,0.8964440822601318
0.69,0.8958069086074829
0.689,0.895357608795166
0.688,0.8948891162872314
0.687,0.8944570422172546
0.686,0.894012451171875
0.685,0.8934907913208008
0.684,0.8930146098136902
0.683,0.8926055431365967
0.682,0.8921086192131042
0.681,0.8917055130004883
0.68,0.8911989331245422
0.679,0.8906767964363098
0.678,0.8901200294494629
0.677,0.8895864486694336
0.676,0.8891509771347046
0.675,0.8887481093406677
0.674,0.888248860836029
0.673,0.887782871723175
0.672,0.8873283863067627
0.671,0.8868291974067688
0.67,0.8863220810890198
0.669,0.8858475685119629
0.668,0.8855047821998596
0.667,0.8849945664405823
0.666,0.8843482136726379
0.665,0.8839123249053955
0.664,0.8833770751953125
0.663,0.8828929662704468
0.662,0.8823733925819397
0.661,0.8819059133529663
0.66,0.8813650012016296
0.659,0.8807847499847412
0.658,0.8802872896194458
0.657,0.8797791600227356
0.656,0.8792745471000671
0.655,0.8788350224494934
0.654,0.878338634967804
0.653,0.8777943849563599
0.652,0.8773334622383118
0.651,0.8768674731254578
0.65,0.8764247298240662
0.649,0.8759018182754517
0.648,0.8752902150154114
0.647,0.8746593594551086
0.646,0.8741769194602966
0.645,0.8737103939056396
0.644,0.8731696605682373
0.643,0.8726657032966614
0.642,0.8722428679466248
0.641,0.871780514717102
0.64,0.8712937235832214
0.639,0.8707387447357178
0.638,0.8702797293663025
0.637,0.8697181344032288
0.636,0.869310200214386
0.635,0.8687338829040527
0.634,0.8682772517204285
0.633,0.8677894473075867
0.632,0.8671905398368835
0.631,0.8666743040084839
0.63,0.8662036657333374
0.629,0.8656080961227417
0.628,0.8651540279388428
0.627,0.8646922707557678
0.626,0.8642338514328003
0.625,0.8637340664863586
0.624,0.8633155822753906
0.623,0.8628906607627869
0.622,0.8624566793441772
0.621,0.8618230223655701
0.62,0.8612935543060303
0.619,0.8607051372528076
0.618,0.8601539134979248
0.617,0.8596628308296204
0.616,0.8591119647026062
0.615,0.858683705329895
0.614,0.8581253886222839
0.613,0.8576202988624573
0.612,0.8570523262023926
0.611,0.8564679622650146
0.61,0.8559613823890686
0.609,0.8553425073623657
0.608,0.8548423051834106
0.607,0.8543075919151306
0.606,0.853750467300415
0.605,0.8532240390777588
0.604,0.8526188135147095
0.603,0.8520087599754333
0.602,0.8514529466629028
0.601,0.8509587049484253
0.6,0.8503131866455078
0.599,0.8496691584587097
0.598,0.849190890789032
0.597,0.8485842347145081
0.596,0.8480783700942993
0.595,0.8474311232566833
0.594,0.8469438552856445
0.593,0.8464454412460327
0.592,0.8458694815635681
0.591,0.845291018486023
0.59,0.8446719646453857
0.589,0.8440850973129272
0.588,0.8435266017913818
0.587,0.842866837978363
0.586,0.8423991203308105
0.585,0.8418708443641663
0.584,0.8412582278251648
0.583,0.8408012986183167
0.582,0.8402970433235168
0.581,0.8397542834281921
0.58,0.8393101692199707
0.579,0.8387672901153564
0.578,0.8383492231369019
0.577,0.8377779126167297
0.576,0.8372728824615479
0.575,0.8367634415626526
0.574,0.8362101912498474
0.573,0.8355543613433838
0.572,0.8350762128829956
0.571,0.8344319462776184
0.57,0.8339004516601562
0.569,0.8332622051239014
0.568,0.8327251076698303
0.567,0.8322630524635315
0.566,0.8316890001296997
0.565,0.8310218453407288
0.564,0.8303456902503967
0.563,0.8297364115715027
0.562,0.8291165232658386
0.561,0.8286513090133667
0.56,0.8282415866851807
0.559,0.8276568055152893
0.558,0.8270482420921326
0.557,0.8264313340187073
0.556,0.8258660435676575
0.555,0.8252843022346497
0.554,0.8247846961021423
0.553,0.8241292238235474
0.552,0.8235077261924744
0.551,0.8229525685310364
0.55,0.8225123882293701
0.549,0.8218908309936523
0.548,0.8214361071586609
0.547,0.8208801746368408
0.546,0.820311427116394
0.545,0.8198035359382629
0.544,0.8192833065986633
0.543,0.8187493681907654
0.542,0.8183221220970154
0.541,0.8178889155387878
0.54,0.8173338770866394
0.539,0.8166977763175964
0.538,0.816128134727478
0.537,0.8155568242073059
0.536,0.8150656223297119
0.535,0.8143837451934814
0.534,0.813839852809906
0.533,0.8133246302604675
0.532,0.8127697706222534
0.531,0.8122989535331726
0.53,0.8117460608482361
0.529,0.8111168742179871
0.528,0.8105555772781372
0.527,0.8099226951599121
0.526,0.8093987703323364
0.525,0.8089420199394226
0.524,0.8084242939949036
0.523,0.807876706123352
0.522,0.8072918653488159
0.521,0.8068172335624695
0.52,0.8062142133712769
0.519,0.8056159019470215
0.518,0.8050817251205444
0.517,0.8045529723167419
0.516,0.8040134310722351
0.515,0.8033463954925537
0.514,0.8026549816131592
0.513,0.8020790815353394
0.512,0.8015340566635132
0.511,0.8010070323944092
0.51,0.800451397895813
0.509,0.7998197078704834
0.508,0.7993049621582031
0.507,0.7987244129180908
0.506,0.7981520295143127
0.505,0.797607421875
0.504,0.7970390319824219
0.503,0.796480119228363
0.502,0.7958235144615173
0.501,0.7952033877372742
0.5,0.7945639491081238
0.499,0.7939942479133606
0.498,0.7933862805366516
0.497,0.7928696274757385
0.496,0.7923110127449036
0.495,0.7917618751525879
0.494,0.791169285774231
0.493,0.7904981374740601
0.492,0.7898994088172913
0.491,0.7893251180648804
0.49,0.7887389659881592
0.489,0.788230299949646
0.488,0.7876998782157898
0.487,0.7871714234352112
0.486,0.7866674065589905
0.485,0.7861251831054688
0.484,0.7854887843132019
0.483,0.7848451733589172
0.482,0.7842085957527161
0.481,0.7837066650390625
0.48,0.783098042011261
0.479,0.7825894951820374
0.478,0.7820398211479187
0.477,0.7815654277801514
0.476,0.7810392379760742
0.475,0.780410885810852
0.474,0.7798945307731628
0.473,0.7793743014335632
0.472,0.7788049578666687
0.471,0.7782212495803833
0.47,0.7777142524719238
0.469,0.7770759463310242
0.468,0.7764747142791748
0.467,0.7758222818374634
0.466,0.7753083109855652
0.465,0.7747974395751953
0.464,0.7743257880210876
0.463,0.7738011479377747
0.462,0.7731691598892212
0.461,0.7724568843841553
0.46,0.7718797922134399
0.459,0.7713025808334351
0.458,0.7707915902137756
0.457,0.7702967524528503
0.456,0.7696964740753174
0.455,0.7691355347633362
0.454,0.7685180306434631
0.453,0.7680140137672424
0.452,0.7675192952156067
0.451,0.7667776942253113
0.45,0.7662506103515625
0.449,0.7656927108764648
0.448,0.765065610408783
0.447,0.7645079493522644
0.446,0.7639384269714355
0.445,0.7633752822875977
0.444,0.7628399133682251
0.443,0.7621510624885559
0.442,0.7615677714347839
0.441,0.7609871625900269
0.44,0.7603616714477539
0.439,0.7597817778587341
0.438,0.7591698169708252
0.437,0.7584701776504517
0.436,0.7578814029693604
0.435,0.757231593132019
0.434,0.7567304372787476
0.433,0.7560344934463501
0.432,0.7553874850273132
0.431,0.7546551823616028
0.43,0.7540551424026489
0.429,0.753559947013855
0.428,0.7528820037841797
0.427,0.7523853182792664
0.426,0.7517542839050293
0.425,0.7511783242225647
0.424,0.7505919933319092
0.423,0.7500880360603333
0.422,0.7495187520980835
0.421,0.7489535212516785
0.42,0.7482714056968689
0.419,0.7476269602775574
0.418,0.7470900416374207
0.417,0.7464340925216675
0.416,0.7458263039588928
0.415,0.7451829314231873
0.414,0.7445639371871948
0.413,0.7439584136009216
0.412,0.7431941032409668
0.411,0.7426120042800903
0.41,0.7418320178985596
0.409,0.7410500645637512
0.408,0.7404740452766418
0.407,0.7398877739906311
0.406,0.7393107414245605
0.405,0.7386374473571777
0.404,0.7379170060157776
0.403,0.7373735904693604
0.402,0.7368086576461792
0.401,0.7361220717430115
0.4,0.7355149388313293
0.399,0.7348690629005432
0.398,0.7343354821205139
0.397,0.733814001083374
0.396,0.7332010865211487
0.395,0.7326757907867432
0.394,0.7319567799568176
0.393,0.7314093112945557
0.392,0.7307674288749695
0.391,0.7301920652389526
0.39,0.7294465899467468
0.389,0.7287818193435669
0.388,0.7281779646873474
0.387,0.727538526058197
0.386,0.7269198298454285
0.385,0.726284384727478
0.384,0.7256928086280823
0.383,0.7250953316688538
0.382,0.7243241667747498
0.381,0.7236175537109375
0.38,0.722975492477417
0.379,0.7224327921867371
0.378,0.7216684818267822
0.377,0.7209630012512207
0.376,0.720302164554596
0.375,0.719516396522522
0.374,0.7188693881034851
0.373,0.7181175351142883
0.372,0.7175112366676331
0.371,0.7168634533882141
0.37,0.7162505984306335
0.369,0.7155883312225342
0.368,0.714897632598877
0.367,0.7141599059104919
0.366,0.7136086821556091
0.365,0.7130201458930969
0.364,0.7124870419502258
0.363,0.7118380069732666
0.362,0.7111403942108154
0.361,0.7103064060211182
0.36,0.7094986438751221
0.359,0.7087323069572449
0.358,0.7079485058784485
0.357,0.7071727514266968
0.356,0.7064831852912903
0.355,0.7057254910469055
0.354,0.704997181892395
0.353,0.7041091322898865
0.352,0.7033753395080566
0.351,0.7027677297592163
0.35,0.7020539045333862
0.349,0.701108992099762
0.348,0.7002388834953308
0.347,0.6992578506469727
0.346,0.6979197263717651
0.345,0.6962162256240845
0.344,0.6946433186531067
0.343,0.6927715539932251
0.342,0.6910662055015564
0.341,0.6893261671066284
0.34,0.6876708269119263
0.339,0.685678243637085
0.338,0.6837948560714722
0.337,0.6814194321632385
0.336,0.679387629032135
0.335,0.6774864792823792
0.334,0.6751524209976196
0.333,0.6732219457626343
0.332,0.6712827682495117
0.331,0.6691543459892273
0.33,0.666486918926239
0.329,0.6644368171691895
0.328,0.6624358892440796
0.327,0.660423755645752
0.326,0.6582353115081787
0.325,0.6555524468421936
0.324,0.6532065868377686
0.323,0.651208758354187
0.322,0.6486964821815491
0.321,0.6461983919143677
0.32,0.6440473794937134
0.319,0.6419345736503601
0.318,0.6397438645362854
0.317,0.6375420093536377
0.316,0.6351723670959473
0.315,0.6325446367263794
0.314,0.6292967200279236
0.313,0.6264986991882324
0.312,0.6237962245941162
0.311,0.6207607388496399
0.31,0.6180967688560486
0.309,0.6160616278648376
0.308,0.614071249961853
0.307,0.6123080253601074
0.306,0.609726071357727
0.305,0.6086925268173218
0.304,0.6079997420310974
0.303,0.6068431735038757
0.302,0.605480968952179
0.301,0.604152262210846
0.3,0.6028140783309937
0.299,0.6014288663864136
0.298,0.5996046662330627
0.297,0.5978929400444031
0.296,0.5966488718986511
0.295,0.5960004329681396
0.294,0.5953904986381531
0.293,0.5945503115653992
0.292,0.5938211679458618
0.291,0.5932542681694031
0.29,0.5926530361175537
0.289,0.5918277502059937
0.288,0.5911698937416077
0.287,0.5903524160385132
0.286,0.5896455645561218
0.285,0.588965892791748
0.284,0.5883357524871826
0.283,0.5876027345657349
0.282,0.5868353843688965
0.281,0.5862071514129639
0.28,0.5855448246002197
0.279,0.584762692451477
0.278,0.5841581225395203
0.277,0.583524227142334
0.276,0.582791268825531
0.275,0.5821045637130737
0.274,0.5815129280090332
0.273,0.5807849168777466
0.272,0.5799700617790222
0.271,0.5791947841644287
0.27,0.5784889459609985
0.269,0.5777319073677063
0.268,0.5769975781440735
0.267,0.5763323903083801
0.266,0.5755073428153992
0.265,0.5749120712280273
0.264,0.5741445422172546
0.263,0.5733550190925598
0.262,0.5725072026252747
0.261,0.5717421770095825
0.26,0.5709163546562195
0.259,0.5702916383743286
0.258,0.5695826411247253
0.257,0.5687806606292725
0.256,0.5680264830589294
0.255,0.5671532154083252
0.254,0.5664019584655762
0.253,0.5656746625900269
0.252,0.564949095249176
0.251,0.5642467141151428
0.25,0.563601016998291
0.249,0.5629205107688904
0.248,0.5620524287223816
0.247,0.561241626739502
0.246,0.5604842901229858
0.245,0.5598537921905518
0.244,0.5591334104537964
0.243,0.5582689046859741
0.242,0.5574504733085632
0.241,0.5567753314971924
0.24,0.5559777617454529
0.239,0.5552257299423218
0.238,0.5544567704200745
0.237,0.5536172986030579
0.236,0.5528450012207031
0.235,0.5522770285606384
0.234,0.5513708591461182
0.233,0.5504541397094727
0.232,0.5496957898139954
0.231,0.5489317178726196
0.23,0.5480349659919739
0.229,0.5473924875259399
0.228,0.5466943979263306
0.227,0.5458922982215881
0.226,0.545206606388092
0.225,0.5443527102470398
0.224,0.5435469150543213
0.223,0.5427653789520264
0.222,0.5419679880142212
0.221,0.541235625743866
0.22,0.540538489818573
0.219,0.5398420095443726
0.218,0.5391877889633179
0.217,0.5384549498558044
0.216,0.5378541946411133
0.215,0.5370384454727173
0.214,0.5362628698348999
0.213,0.5355432629585266
0.212,0.5346992611885071
0.211,0.5323728919029236
0.21,0.5315743088722229
0.209,0.5283093452453613
0.208,0.5274532437324524
0.207,0.5267000198364258
0.206,0.5259957909584045
0.205,0.5250146985054016
0.204,0.5242056846618652
0.203,0.5231711864471436
0.202,0.5221099853515625
0.201,0.5211912989616394
0.2,0.5203512907028198
0.199,0.5192920565605164
0.198,0.5182322263717651
0.197,0.5174584984779358
0.196,0.5163623094558716
0.195,0.5154227018356323
0.194,0.514419674873352
0.193,0.5135026574134827
0.192,0.5126211047172546
0.191,0.5115959644317627
0.19,0.5107628107070923
0.189,0.5099937319755554
0.188,0.5089104771614075
0.187,0.5080356001853943
0.186,0.5070648193359375
0.185,0.5060917735099792
0.184,0.5050144791603088
0.183,0.5042200684547424
0.182,0.503329873085022
0.181,0.5023356080055237
0.18,0.5014610886573792
0.179,0.5005422234535217
0.178,0.4995346963405609
0.177,0.4986289441585541
0.176,0.4976312518119812
0.175,0.4964808523654938
0.174,0.4955320358276367
0.173,0.4944963157176971
0.172,0.4935081303119659
0.171,0.4925319850444793
0.17,0.4913948774337768
0.169,0.4904382526874542
0.168,0.4894824922084808
0.167,0.4885153174400329
0.166,0.4874790608882904
0.165,0.4866428971290588
0.164,0.4855308532714844
0.163,0.4846703112125397
0.162,0.4835704565048218
0.161,0.4825258553028106
0.16,0.4815665483474731
0.159,0.48039710521698
0.158,0.479152649641037
0.157,0.4778082966804504
0.156,0.4766306579113006
0.155,0.4755558967590332
0.154,0.4744581580162048
0.153,0.4734082221984863
0.152,0.4724337756633758
0.151,0.4713548421859741
0.15,0.4702935814857483
0.149,0.469249278306961
0.148,0.4681984186172485
0.147,0.467193603515625
0.146,0.466187059879303
0.145,0.4649397730827331
0.144,0.4638700783252716
0.143,0.4626805782318115
0.142,0.4615622758865356
0.141,0.4605612754821777
0.14,0.4597445726394653
0.139,0.4587045013904571
0.138,0.4579822421073913
0.137,0.4569554030895233
0.136,0.4559722542762756
0.135,0.4550348818302154
0.134,0.4539240896701813
0.133,0.4526872634887695
0.132,0.4516052901744842
0.131,0.4505267441272735
0.13,0.4496097266674042
0.129,0.4485931694507599
0.128,0.4473990201950073
0.127,0.4463711380958557
0.126,0.4450307488441467
0.125,0.4438648819923401
0.124,0.4426600337028503
0.123,0.4416567683219909
0.122,0.4402706027030945
0.121,0.4390320479869842
0.12,0.4378115832805633
0.119,0.4368284046649933
0.118,0.4357131719589233
0.117,0.4347384572029114
0.116,0.4336102306842804
0.115,0.4325035214424133
0.114,0.431209921836853
0.113,0.4299277663230896
0.112,0.428741455078125
0.111,0.4273850321769714
0.11,0.4260600507259369
0.109,0.424763560295105
0.108,0.4235714972019195
0.107,0.4224527180194855
0.106,0.4214721322059631
0.105,0.4203813970088959
0.104,0.4191440045833587
0.103,0.4177476167678833
0.102,0.4165583848953247
0.101,0.4152339994907379
0.1,0.4140801429748535
0.099,0.4128437340259552
0.098,0.411681205034256
0.097,0.4105020761489868
0.096,0.4091891348361969
0.095,0.407721996307373
0.094,0.4064987897872925
0.093,0.4051377177238464
0.092,0.4037542343139648
0.091,0.4023529589176178
0.09,0.4009946584701538
0.089,0.3998652398586273
0.088,0.3983897566795349
0.087,0.3974610269069671
0.086,0.3959658443927765
0.085,0.3942922949790954
0.084,0.3927665650844574
0.083,0.3912537097930908
0.082,0.3894433379173279
0.081,0.3880766034126282
0.08,0.3863488435745239
0.079,0.3848066627979278
0.078,0.3834125101566314
0.077,0.3820904493331909
0.076,0.3803150951862335
0.075,0.3786589205265045
0.074,0.3768672347068786
0.073,0.3753072619438171
0.072,0.3734138011932373
0.071,0.3717053234577179
0.07,0.369688332080841
0.069,0.3676797747611999
0.068,0.3656879365444183
0.067,0.3634332120418548
0.066,0.3611243367195129
0.065,0.3588551580905914
0.064,0.3566454946994781
0.063,0.354030430316925
0.062,0.3513917028903961
0.061,0.3487825989723205
0.06,0.3461207747459411
0.059,0.3431337773799896
0.058,0.3402921259403229
0.057,0.3377613425254822
0.056,0.3346793353557586
0.055,0.3316464424133301
0.054,0.3294158279895782
0.053,0.3264980316162109
0.052,0.3235078155994415
0.051,0.3207087814807892
0.05,0.3177165687084198
0.049,0.3149529993534088
0.048,0.3126184046268463
0.047,0.3095733523368835
0.046,0.3069785535335541
0.045,0.3043548762798309
0.044,0.3015701472759247
0.043,0.2991997301578522
0.042,0.2969039976596832
0.041,0.2944153249263763
0.04,0.2921086251735687
0.039,0.2892205119132995
0.038,0.2869962751865387
0.037,0.284531831741333
0.036,0.2818567156791687
0.035,0.2792093753814697
0.034,0.2762026190757751
0.033,0.2732788026332855
0.032,0.2699902057647705
0.031,0.2673105895519256
0.03,0.2638788819313049
0.029,0.2607062757015228
0.028,0.2578806281089782
0.027,0.2552720904350281
0.026,0.2521741688251495
0.025,0.2493307143449783
0.024,0.245944932103157
0.023,0.2427808940410614
0.022,0.2402146011590957
0.021,0.2373397648334503
0.02,0.2330639213323593
0.019,0.2295775115489959
0.018,0.2262651324272155
0.017,0.2224967926740646
0.016,0.2184387296438217
0.015,0.2146351933479309
0.014,0.2108270525932312
0.013,0.2077525556087494
0.012,0.2043730914592743
0.011,0.2001692503690719
0.01,0.196865975856781
0.009,0.1936569809913635
0.008,0.190575435757637
0.007,0.1873094290494918
0.006,0.1830955743789672
0.005,0.1784524470567703
0.004,0.1720014959573745
0.003,0.1635862439870834
0.002,0.152490884065628
0.001,0.0
"""

#Phone Risk US model
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskPhone_XGB_DEV_EXP_FMV_NSR_VCP_VNP_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.name="Phone Risk Model (US) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.version="6.0"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.identifier="RiskPhone_XGB_DEV_EXP_FMV_NSR_VCP_VNP_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.params="""{"EXPVL.200001":null,"VCPVL.230310":null,"EXPVL.200006":null,"EXPVL.300006":null,"NSRVL.200020":null,"EXPVL.200011":null,"NSRVL.200010":null,"EXPVL.200003":null,"EXPVL.100006":null,"FMVAL.300043":null,"EXPVL.100004":null,"FMVAL.100143":null,"NSRVL.200028":null,"FMVAL.300051":null,"EXPVL.300010":null,"NSRVL.200017":null,"EXPVL.200002":null,"EXPVL.100008":null,"EXPVL.200019":null,"NSRVL.200004":null,"FMVAL.300046":null,"NSRVL.200029":null,"EXPVL.200049":null,"EXPVL.300001":null,"FMVAL.300042":null,"EXPVL.300007":null,"FMVAL.300041":null,"NSRVL.200030":null,"NSRVL.200006":null,"FMVAL.300045":null,"EXPVL.100005":null,"NSRVL.200014":null,"NSRVL.100005":null,"NSRVL.200031":null,"EXPVL.300004":null,"NSRVL.200032":null,"FMVAL.300044":null,"VCPVL.210412":null,"NSRVL.100004":null,"EXPVL.200017":null,"EXPVL.300002":null,"EXPVL.300005":null,"EXPVL.300011":null,"DEVAL.100027":null,"VNPVL.210612":null,"FMVAL.300049":null,"EXPVL.200018":null,"EXPVL.200004":null,"EXPVL.200009":null,"NSRVL.200012":null,"EXPVL.100001":null,"NSRVL.200018":null,"FMVAL.300047":null,"NSRVL.200003":null,"VCPVL.240099":null,"VCPVL.220306":null,"EXPVL.100003":null,"EXPVL.300009":null,"NSRVL.200001":null,"NSRVL.200023":null,"EXPVL.200016":null,"NSRVL.200008":null,"EXPVL.100002":null,"NSRVL.200011":null,"NSRVL.200021":null,"EXPVL.200005":null,"EXPVL.100000":null,"EXPVL.100007":null,"EXPVL.300003":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.low_risk.threshold=0.5747311115264893
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.high_risk.threshold=0.8375539779663086
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_phone_us.quantile_mapping="""
quantile,score
0.999,0.970825731754303
0.998,0.9631718993186952
0.997,0.9550985097885132
0.996,0.9517051577568054
0.995,0.9468578100204468
0.994,0.9437103271484376
0.993,0.9389105439186096
0.992,0.9329725503921508
0.991,0.927563488483429
0.99,0.9229004979133606
0.989,0.9171832203865052
0.988,0.9110299348831176
0.987,0.9082868695259094
0.986,0.9039602875709534
0.985,0.899019181728363
0.984,0.8961778283119202
0.983,0.8907104730606079
0.982,0.8845624327659607
0.981,0.8812872767448425
0.98,0.8756623268127441
0.979,0.8720995783805847
0.978,0.8687747716903687
0.977,0.8671383261680603
0.976,0.867072582244873
0.975,0.8624016046524048
0.974,0.8556196689605713
0.973,0.851760983467102
0.972,0.8470873832702637
0.971,0.8422773480415344
0.97,0.8375539779663086
0.969,0.8339580297470093
0.968,0.8310776948928833
0.967,0.8284393548965454
0.966,0.8239399790763855
0.965,0.8192464709281921
0.964,0.8159703612327576
0.963,0.8119668364524841
0.962,0.8086245656013489
0.961,0.8039931654930115
0.96,0.8021897673606873
0.959,0.797102689743042
0.958,0.7946996688842773
0.957,0.7904686331748962
0.956,0.7868194580078125
0.955,0.7841578722000122
0.954,0.7634493112564087
0.953,0.7605733275413513
0.952,0.7567816376686096
0.951,0.7506152391433716
0.95,0.7479152083396912
0.949,0.7436582446098328
0.948,0.7411127686500549
0.947,0.7373179793357849
0.946,0.7328469157218933
0.945,0.728097140789032
0.944,0.7234308123588562
0.942,0.7223928570747375
0.941,0.718612790107727
0.94,0.7137414216995239
0.939,0.710041880607605
0.938,0.7068527340888977
0.937,0.702413022518158
0.936,0.6988905668258667
0.935,0.6936160922050476
0.934,0.6887931227684021
0.933,0.6855254769325256
0.932,0.6808486580848694
0.931,0.6777334213256836
0.93,0.6739626526832581
0.929,0.6708827018737793
0.928,0.6655757427215576
0.927,0.6614140868186951
0.926,0.6579087972640991
0.925,0.6524040102958679
0.924,0.6488588452339172
0.923,0.6456771492958069
0.922,0.6417209506034851
0.921,0.6393606066703796
0.92,0.6350030899047852
0.919,0.6282413005828857
0.918,0.6237565875053406
0.917,0.6212980151176453
0.916,0.6176189184188843
0.915,0.6145570874214172
0.914,0.6114445328712463
0.913,0.6101537346839905
0.912,0.6065135598182678
0.911,0.6032756567001343
0.91,0.6000781655311584
0.908,0.5950173735618591
0.907,0.5939241051673889
0.906,0.5910182595252991
0.905,0.5888734459877014
0.904,0.5875285267829895
0.903,0.5839168429374695
0.902,0.5814783573150635
0.901,0.57756108045578
0.9,0.5747311115264893
0.899,0.5724249482154846
0.898,0.5693888664245605
0.897,0.5668460130691528
0.896,0.5638296604156494
0.895,0.5603318810462952
0.894,0.5587818622589111
0.893,0.5569258332252502
0.892,0.5537561774253845
0.891,0.5517631769180298
0.89,0.5485529899597168
0.889,0.5462655425071716
0.888,0.5428155660629272
0.887,0.5394821166992188
0.886,0.5370410680770874
0.885,0.5348741412162781
0.884,0.5319526195526123
0.883,0.5283908247947693
0.882,0.5253578424453735
0.881,0.5224708318710327
0.88,0.5203103423118591
0.879,0.5174174308776855
0.878,0.5150684714317322
0.877,0.5117892622947693
0.876,0.5079039931297302
0.875,0.5062859058380127
0.874,0.5048810243606567
0.873,0.4994456470012665
0.871,0.4976584315299988
0.87,0.4861909747123718
0.869,0.4838486611843109
0.868,0.4809840321540832
0.867,0.4796015918254852
0.866,0.4773958921432495
0.865,0.4746321141719818
0.864,0.4710865616798401
0.863,0.4682435691356659
0.862,0.4652826189994812
0.861,0.4629519581794739
0.86,0.4603344798088074
0.859,0.4573516249656677
0.858,0.4550157785415649
0.857,0.4522751569747925
0.856,0.4498549103736877
0.855,0.4470792114734649
0.854,0.4449315071105957
0.853,0.4423964321613312
0.852,0.4395911991596222
0.851,0.4373363554477691
0.85,0.4351069629192352
0.849,0.4333620369434356
0.848,0.4306747019290924
0.847,0.428651213645935
0.846,0.4259475767612457
0.845,0.4218921065330505
0.844,0.4181468486785888
0.843,0.4146488904953003
0.842,0.4121282398700714
0.841,0.4100085794925689
0.84,0.4079967737197876
0.839,0.4058393239974975
0.838,0.4038521945476532
0.837,0.4015945494174957
0.836,0.3993217647075653
0.835,0.3974894285202026
0.834,0.3946163952350616
0.833,0.3924044966697693
0.832,0.3904275596141815
0.831,0.3882544934749603
0.83,0.3858674466609955
0.829,0.383805364370346
0.828,0.3810371458530426
0.827,0.3789017796516418
0.826,0.3771813511848449
0.825,0.3752437233924866
0.824,0.3734316825866699
0.823,0.3716250658035278
0.822,0.3699961602687835
0.821,0.3681127429008484
0.82,0.3657575249671936
0.819,0.3624095320701599
0.818,0.3617427051067352
0.817,0.3600198328495025
0.816,0.3570198118686676
0.815,0.3549655973911285
0.814,0.3533419668674469
0.813,0.3519810438156128
0.812,0.3506412208080292
0.811,0.3492927551269531
0.81,0.3457867503166199
0.809,0.3363096415996551
0.803,0.3361497223377228
0.802,0.3345771133899688
0.801,0.3333409130573272
0.8,0.3316740691661834
0.799,0.3299730718135834
0.798,0.3286183476448059
0.797,0.3272208273410797
0.796,0.3255196511745453
0.795,0.3240418434143066
0.794,0.3227821886539459
0.793,0.3213260471820831
0.792,0.3201055526733398
0.791,0.3185512125492096
0.79,0.3168685734272003
0.789,0.3154890239238739
0.788,0.3142918050289154
0.787,0.3130797445774078
0.786,0.310342013835907
0.785,0.3093386590480804
0.784,0.3076974153518677
0.783,0.3066152036190033
0.782,0.3055615723133087
0.781,0.3042689263820648
0.78,0.3031567931175232
0.779,0.3016634583473205
0.778,0.3004802167415619
0.777,0.2989881336688995
0.776,0.2977007627487182
0.775,0.2921885550022125
0.774,0.2912311553955078
0.773,0.2901064753532409
0.772,0.2888646125793457
0.771,0.2878984212875366
0.77,0.2866479754447937
0.769,0.2851682901382446
0.768,0.2842240035533905
0.767,0.2828220129013061
0.766,0.2818139791488647
0.765,0.2809046506881714
0.764,0.2800991237163543
0.763,0.2789046466350555
0.762,0.2779084742069244
0.761,0.2770960628986358
0.76,0.2762328386306762
0.759,0.2753442823886871
0.758,0.2740919888019562
0.757,0.2730975449085235
0.756,0.2721543610095978
0.755,0.2713491320610046
0.754,0.2703588604927063
0.753,0.2696599960327148
0.752,0.2682593464851379
0.751,0.2668960690498352
0.75,0.2661836445331573
0.749,0.2653349637985229
0.748,0.2643495798110962
0.747,0.2629970014095306
0.746,0.2628562152385711
0.745,0.2608661651611328
0.744,0.2602491974830627
0.743,0.2592412829399109
0.742,0.2584436535835266
0.741,0.2577992677688598
0.74,0.2569244503974914
0.739,0.2561898529529571
0.738,0.255642682313919
0.737,0.2539952099323272
0.736,0.2508684992790222
0.734,0.2507112622261047
0.733,0.2500604391098022
0.732,0.2494364529848098
0.731,0.2486065030097961
0.73,0.2478345036506652
0.729,0.2473478317260742
0.728,0.2466412633657455
0.727,0.2459548264741897
0.726,0.2453386336565017
0.725,0.2447682172060012
0.724,0.2438475042581558
0.723,0.2431557476520538
0.722,0.2423790842294693
0.72,0.2406065315008163
0.719,0.2398697286844253
0.718,0.2393131703138351
0.717,0.2388483136892318
0.716,0.2381860613822937
0.715,0.2376403659582138
0.714,0.2363633811473846
0.713,0.236158937215805
0.712,0.2355803400278091
0.711,0.2328819781541824
0.709,0.2323922514915466
0.708,0.2314846962690353
0.707,0.2309528887271881
0.706,0.230229303240776
0.705,0.2292422801256179
0.704,0.2288240939378738
0.703,0.2284555435180664
0.702,0.2280754297971725
0.701,0.2274694591760635
0.7,0.2267457842826843
0.699,0.2262134402990341
0.698,0.226114347577095
0.692,0.2188029438257217
0.691,0.2181444317102432
0.69,0.2175237387418747
0.689,0.2169527858495712
0.688,0.2164025604724884
0.687,0.2159328162670135
0.686,0.2154728919267654
0.685,0.2150214165449142
0.684,0.2143630683422088
0.683,0.2139210999011993
0.682,0.2133599668741226
0.681,0.2125989943742752
0.68,0.2120396196842193
0.679,0.2108756005764007
0.677,0.210768774151802
0.676,0.2101657390594482
0.675,0.2097032070159912
0.674,0.2092376798391342
0.673,0.2086163908243179
0.672,0.2081945538520813
0.671,0.207645907998085
0.67,0.2071158587932586
0.669,0.2065936923027038
0.668,0.2060246765613556
0.667,0.2056099474430084
0.666,0.2048680633306503
0.665,0.2044591456651687
0.653,0.198560282588005
0.652,0.1981695890426635
0.651,0.1976798772811889
0.65,0.1971331685781479
0.649,0.1966776251792907
0.648,0.1962466239929199
0.647,0.1957456618547439
0.646,0.1952964812517166
0.645,0.1948271095752716
0.644,0.1941882967948913
0.643,0.1936615705490112
0.642,0.1931171417236328
0.641,0.1926730871200561
0.64,0.1921217292547226
0.639,0.1920814961194992
0.638,0.1910586059093475
0.637,0.1905693113803863
0.636,0.1900855004787445
0.635,0.1895217895507812
0.634,0.1890967488288879
0.633,0.1884511858224868
0.632,0.1878673434257507
0.631,0.1874684393405914
0.63,0.1870726346969604
0.629,0.1865030378103256
0.628,0.1860082298517227
0.627,0.1855466663837433
0.626,0.1850035041570663
0.625,0.1845601797103881
0.624,0.1840610355138778
0.623,0.1836650669574737
0.622,0.1832607090473175
0.621,0.1829291731119156
0.62,0.1823187619447708
0.619,0.1818951517343521
0.618,0.1814315170049667
0.617,0.1809442192316055
0.616,0.1805279105901718
0.615,0.1801917105913162
0.614,0.1795482635498047
0.613,0.179094061255455
0.612,0.178666815161705
0.611,0.1783070862293243
0.61,0.1779881864786148
0.609,0.1775630712509155
0.608,0.177093356847763
0.607,0.1766913831233978
0.606,0.1762638390064239
0.605,0.1753977984189987
0.604,0.1750575900077819
0.603,0.1746724098920822
0.602,0.1741966009140014
0.601,0.1737828701734542
0.6,0.1733483225107193
0.599,0.1727784723043441
0.598,0.1723896265029907
0.597,0.1719258725643158
0.596,0.1715544164180755
0.595,0.171204537153244
0.594,0.1709086894989013
0.593,0.1703639477491378
0.592,0.1699103266000747
0.591,0.1694345772266388
0.59,0.1690123677253723
0.589,0.1685535460710525
0.588,0.1681417375802993
0.587,0.167817160487175
0.586,0.1674512922763824
0.585,0.1671521961688995
0.584,0.1667753010988235
0.583,0.1663239896297454
0.582,0.1658344268798828
0.581,0.1654500216245651
0.58,0.1646585464477539
0.579,0.164301648736
0.578,0.1639219373464584
0.577,0.1635101586580276
0.576,0.1633664071559906
0.575,0.1628543436527252
0.574,0.1624601930379867
0.573,0.1620325595140457
0.572,0.1616848111152649
0.571,0.161305159330368
0.57,0.1608670800924301
0.569,0.1604274064302444
0.568,0.1600048691034317
0.567,0.1596696376800537
0.566,0.1593462377786636
0.565,0.1588948369026184
0.564,0.1581884771585464
0.563,0.1577323079109192
0.562,0.1574127078056335
0.561,0.1569908261299133
0.56,0.156648188829422
0.559,0.1566099971532821
0.558,0.1554457694292068
0.557,0.1549881845712661
0.556,0.1546353101730346
0.555,0.1542137116193771
0.554,0.153860718011856
0.553,0.1534438282251358
0.552,0.1530651748180389
0.551,0.1527004837989807
0.55,0.1522846221923828
0.549,0.1519924849271774
0.548,0.1516069620847702
0.547,0.1512261182069778
0.546,0.1508783400058746
0.545,0.1505180299282074
0.544,0.1501473486423492
0.543,0.1497065275907516
0.542,0.1491124480962753
0.541,0.1487847566604614
0.54,0.1485014855861663
0.539,0.1480956673622131
0.538,0.1477028280496597
0.537,0.1473942846059799
0.536,0.1470784991979599
0.535,0.1467004418373108
0.534,0.1464487463235855
0.533,0.1460924446582794
0.532,0.1457628309726715
0.531,0.1455050408840179
0.53,0.1450620293617248
0.529,0.144703209400177
0.528,0.1444132626056671
0.527,0.1440827399492263
0.526,0.1433659642934799
0.525,0.1430725753307342
0.524,0.1428405940532684
0.523,0.1424823552370071
0.522,0.1421414464712143
0.521,0.1418108344078064
0.52,0.1414520591497421
0.519,0.1411307454109192
0.518,0.1408401131629943
0.517,0.1405031532049179
0.516,0.139419674873352
0.514,0.1391289681196212
0.513,0.1389387100934982
0.512,0.1386611610651016
0.511,0.138293445110321
0.51,0.1380333751440048
0.504,0.1339681446552276
0.503,0.1337748318910598
0.502,0.1334932446479797
0.501,0.1331326067447662
0.5,0.1328766196966171
0.499,0.1325832605361938
0.498,0.13229601085186
0.497,0.1320089548826217
0.496,0.1318026036024093
0.495,0.131532996892929
0.494,0.1312280893325805
0.493,0.1309554129838943
0.492,0.1306282877922058
0.491,0.1303662359714508
0.49,0.1301430016756057
0.489,0.1299067884683609
0.488,0.1296391189098358
0.487,0.12936931848526
0.486,0.1290286183357238
0.485,0.1287252753973007
0.484,0.1284556090831756
0.483,0.1281073987483978
0.482,0.1278727352619171
0.481,0.1275654286146164
0.48,0.1273012012243271
0.479,0.1268131285905838
0.478,0.1265911906957626
0.477,0.1263048648834228
0.476,0.1260875016450882
0.475,0.1258207410573959
0.474,0.1255167573690414
0.473,0.1252549886703491
0.472,0.1250379383563995
0.471,0.1247782185673713
0.47,0.1245530471205711
0.469,0.1243115663528442
0.468,0.1241962760686874
0.467,0.1239660307765007
0.466,0.1236980929970741
0.465,0.1234407946467399
0.464,0.1233584880828857
0.461,0.1214458122849464
0.46,0.1211976334452629
0.459,0.1209306642413139
0.458,0.1206899881362915
0.457,0.1204524859786033
0.456,0.1202440783381462
0.455,0.1200718134641647
0.454,0.1198866888880729
0.453,0.119655355811119
0.452,0.1195106580853462
0.451,0.1192802488803863
0.45,0.1191014274954795
0.449,0.1183225288987159
0.448,0.1180386319756507
0.447,0.1178577840328216
0.446,0.1176560372114181
0.445,0.1174604743719101
0.444,0.1172961369156837
0.443,0.1169328466057777
0.442,0.1167101711034774
0.441,0.1163937076926231
0.44,0.1161167472600936
0.439,0.1158958226442337
0.438,0.115748718380928
0.437,0.1155778616666793
0.436,0.1153762713074684
0.435,0.1151630356907844
0.434,0.1149635016918182
0.433,0.1146586164832115
0.432,0.1145349666476249
0.431,0.1142960786819458
0.43,0.1140575483441352
0.429,0.1138683408498764
0.428,0.113835796713829
0.427,0.1135772466659545
0.426,0.1133892089128494
0.425,0.1131550595164299
0.424,0.1129448562860488
0.423,0.1127615571022033
0.422,0.1125263124704361
0.421,0.1122706905007362
0.42,0.1120993793010711
0.419,0.1117615848779678
0.418,0.1115877851843833
0.417,0.1113907098770141
0.416,0.1112028360366821
0.415,0.1110058724880218
0.414,0.1108426228165626
0.413,0.110625572502613
0.412,0.110211543738842
0.411,0.1100719049572944
0.41,0.109877772629261
0.409,0.109704703092575
0.408,0.1095070391893386
0.407,0.1093116253614425
0.406,0.1091237813234329
0.405,0.1089649572968483
0.404,0.1087485700845718
0.403,0.1085207983851432
0.402,0.1083195209503173
0.401,0.1081610545516014
0.4,0.1079621613025665
0.399,0.1077658459544181
0.398,0.1075704768300056
0.397,0.1073739305138588
0.396,0.1071576103568077
0.395,0.1069909781217575
0.393,0.1068237125873565
0.392,0.1066187620162963
0.391,0.1064068004488945
0.39,0.1061967015266418
0.389,0.1059552654623985
0.388,0.1057387515902519
0.387,0.1055401265621185
0.386,0.1053069531917572
0.385,0.105294331908226
0.384,0.1045807674527168
0.383,0.1044594869017601
0.382,0.1043651923537254
0.381,0.1039145961403846
0.38,0.1037126928567886
0.379,0.1035023182630539
0.378,0.1032760217785835
0.377,0.1030696034431457
0.376,0.1028470546007156
0.375,0.1027998775243759
0.374,0.1023081392049789
0.373,0.1022038385272026
0.372,0.101760409772396
0.371,0.1015455871820449
0.37,0.1012028306722641
0.369,0.1010103970766067
0.368,0.1008126661181449
0.367,0.1006991863250732
0.366,0.1005443185567855
0.365,0.1002200469374656
0.364,0.1001237705349922
0.351,0.0947649627923965
0.35,0.0945617854595184
0.349,0.0943638831377029
0.348,0.0941666588187217
0.347,0.0939878076314926
0.346,0.09376972168684
0.345,0.0935835540294647
0.344,0.0934426262974739
0.343,0.0932907313108444
0.342,0.0931354463100433
0.341,0.0929551124572753
0.34,0.0927745923399925
0.339,0.0925898030400276
0.338,0.0924018993973732
0.337,0.09222112596035
0.336,0.0920984745025634
0.335,0.0919159203767776
0.334,0.0917044803500175
0.333,0.0915353968739509
0.332,0.0913684070110321
0.331,0.0912384986877441
0.33,0.0911001414060592
0.329,0.09095199406147
0.328,0.0908081158995628
0.327,0.0903268083930015
0.326,0.0901903212070465
0.325,0.0899999290704727
0.324,0.0893844068050384
0.323,0.0893491879105568
0.322,0.0892526879906654
0.321,0.0890904888510704
0.32,0.0888948217034339
0.319,0.0887083411216735
0.318,0.0885425359010696
0.317,0.0883887261152267
0.316,0.0882223919034004
0.315,0.0880675539374351
0.314,0.0879124477505683
0.313,0.0877323374152183
0.312,0.087585799396038
0.311,0.0874489098787307
0.31,0.0873018354177475
0.309,0.0871261805295944
0.308,0.0869866386055946
0.307,0.0868467092514038
0.306,0.0867246463894844
0.305,0.0864841118454933
0.304,0.0862808749079704
0.303,0.0861380025744438
0.302,0.085963636636734
0.301,0.0858100727200508
0.3,0.0856168419122695
0.299,0.0854808762669563
0.298,0.0853384733200073
0.297,0.0851684585213661
0.296,0.0850115269422531
0.295,0.0849073007702827
0.294,0.0847538784146308
0.293,0.0845941007137298
0.292,0.0844923108816146
0.291,0.0843536183238029
0.29,0.0842001810669899
0.289,0.084026850759983
0.288,0.0838959589600563
0.287,0.0837686732411384
0.286,0.0836661234498024
0.285,0.0835268720984458
0.284,0.0833970084786415
0.283,0.0832634568214416
0.282,0.0831157267093658
0.281,0.0829856693744659
0.28,0.0828717723488807
0.279,0.082720547914505
0.278,0.0826309025287628
0.277,0.0824189558625221
0.276,0.0822941437363624
0.275,0.0821520984172821
0.274,0.0819955989718437
0.273,0.0818521752953529
0.272,0.0817572772502899
0.271,0.0816258937120437
0.27,0.0815853253006935
0.269,0.0814441889524459
0.268,0.0812931507825851
0.267,0.0811204761266708
0.266,0.0810204744338989
0.265,0.0809018686413765
0.264,0.0807650610804557
0.263,0.0807016268372535
0.262,0.0805152878165245
0.261,0.0803797021508216
0.26,0.0802180171012878
0.259,0.0800557285547256
0.258,0.0798824578523635
0.257,0.0797307789325714
0.256,0.0796324983239173
0.255,0.0794914960861206
0.254,0.0793602615594863
0.253,0.0791620314121246
0.252,0.0790007412433624
0.251,0.0788579359650611
0.25,0.0787128582596778
0.249,0.0785802975296974
0.248,0.0784197151660919
0.247,0.0782862156629562
0.246,0.078089453279972
0.245,0.0779529437422752
0.244,0.0778213813900947
0.243,0.0776651427149772
0.242,0.0775190889835357
0.241,0.0773944705724716
0.24,0.0772065371274948
0.239,0.0770565196871757
0.238,0.0769103094935417
0.237,0.0767652913928031
0.236,0.0766619071364402
0.235,0.0764900967478752
0.234,0.0763386934995651
0.233,0.0762018337845802
0.232,0.0760336816310882
0.231,0.0758726224303245
0.23,0.0757590606808662
0.229,0.0755949690937995
0.228,0.0754692256450653
0.227,0.0753546208143234
0.226,0.0752363950014114
0.225,0.0751222968101501
0.224,0.0749966502189636
0.223,0.0748499780893325
0.222,0.0747152715921402
0.221,0.0745817720890045
0.22,0.0744132623076438
0.219,0.0742316469550132
0.218,0.074097454547882
0.217,0.0739650875329971
0.216,0.073863074183464
0.215,0.0737330168485641
0.214,0.0735859349370002
0.213,0.0734566450119018
0.212,0.0733071714639663
0.211,0.0731609389185905
0.21,0.0730076506733894
0.209,0.072861336171627
0.208,0.0727011039853096
0.207,0.072541706264019
0.206,0.0723858028650283
0.205,0.0722802430391311
0.204,0.0721379071474075
0.203,0.0720073208212852
0.202,0.0718661844730377
0.201,0.0717136785387992
0.2,0.0715741142630577
0.199,0.0714468583464622
0.198,0.0712909623980522
0.197,0.0711437538266182
0.196,0.0709935426712036
0.195,0.0708417147397995
0.194,0.0707129016518592
0.193,0.0705616399645805
0.192,0.0704778507351875
0.191,0.0703598782420158
0.19,0.0702123567461967
0.189,0.0700669959187507
0.188,0.0699077621102333
0.187,0.0697755143046379
0.186,0.0696543827652931
0.185,0.0695000141859054
0.184,0.0693769305944442
0.183,0.0692835077643394
0.182,0.0691946521401405
0.181,0.0688399225473404
0.18,0.0686888694763183
0.179,0.068556822836399
0.178,0.0684235617518425
0.177,0.0682883858680725
0.176,0.0681237876415252
0.175,0.0679660737514495
0.174,0.0678532943129539
0.173,0.0677181705832481
0.172,0.0675939470529556
0.171,0.0674681961536407
0.17,0.0674321576952934
0.169,0.06728395819664
0.168,0.0671447589993476
0.167,0.067006915807724
0.166,0.0668701753020286
0.165,0.0667335838079452
0.164,0.0665995478630065
0.163,0.0664455592632293
0.162,0.0663394853472709
0.161,0.0662110596895217
0.16,0.0660656094551086
0.159,0.0659300237894058
0.158,0.0657762512564659
0.157,0.0656675919890403
0.156,0.0655248388648033
0.155,0.0653657615184784
0.154,0.0652361288666725
0.153,0.0650987699627876
0.152,0.0649646148085594
0.151,0.0648294985294342
0.15,0.0646925121545791
0.149,0.0645436346530914
0.148,0.0644131377339363
0.147,0.0642152577638626
0.146,0.0640875995159149
0.145,0.0639289170503616
0.144,0.063772365450859
0.143,0.0636473596096038
0.142,0.063504122197628
0.141,0.0633314847946167
0.14,0.063195563852787
0.139,0.0630474910140037
0.138,0.062916100025177
0.137,0.0627708062529563
0.136,0.062648244202137
0.135,0.0624984204769134
0.134,0.0623708441853523
0.133,0.0622121654450893
0.132,0.06207737326622
0.131,0.0619329586625099
0.13,0.0618026331067085
0.129,0.0616648644208908
0.128,0.0615088902413845
0.127,0.0613825283944606
0.126,0.0612644851207733
0.125,0.061198353767395
0.124,0.0609767101705074
0.123,0.0608582384884357
0.122,0.0607001818716526
0.121,0.0605655461549758
0.12,0.0604176558554172
0.119,0.0602653101086616
0.118,0.0601135604083538
0.117,0.0599629059433937
0.116,0.0598172470927238
0.115,0.0596909560263156
0.114,0.059551790356636
0.113,0.0594244077801704
0.112,0.0592993013560771
0.111,0.0591286830604076
0.11,0.058981966227293
0.109,0.0588618218898773
0.108,0.0587199293076992
0.107,0.0585464462637901
0.106,0.0584101006388664
0.105,0.0582568310201168
0.104,0.0581002384424209
0.103,0.0579388290643692
0.102,0.0577702112495899
0.101,0.0576357133686542
0.1,0.0574817173182964
0.099,0.0573172867298126
0.098,0.0571461655199527
0.097,0.0569991506636142
0.096,0.0568392761051654
0.095,0.0567102469503879
0.094,0.0565466210246086
0.093,0.0563930757343769
0.092,0.0562527179718017
0.091,0.0560930371284484
0.09,0.0559192523360252
0.089,0.0557463057339191
0.088,0.0556073188781738
0.087,0.0554703027009964
0.086,0.0553035289049148
0.085,0.0551200583577156
0.084,0.0549384541809558
0.083,0.0547912009060382
0.082,0.0546319596469402
0.081,0.0544869005680084
0.08,0.0543034933507442
0.079,0.0541781410574913
0.078,0.0540107637643814
0.077,0.0538837127387523
0.076,0.0537362806499004
0.075,0.0535897947847843
0.074,0.0534246191382408
0.073,0.0532838031649589
0.072,0.053104680031538
0.071,0.0529278740286827
0.07,0.0527277253568172
0.069,0.0525667294859886
0.068,0.0523820295929908
0.067,0.0521877817809581
0.066,0.0520255230367183
0.065,0.0518028698861599
0.064,0.0516582541167736
0.063,0.0514374896883964
0.062,0.0512624569237232
0.061,0.0511280447244644
0.06,0.0509426295757293
0.059,0.0506844483315944
0.058,0.0504847913980484
0.057,0.0503038242459297
0.056,0.0500558875501155
0.055,0.0498418062925338
0.054,0.0496535971760749
0.053,0.0494610965251922
0.052,0.0492401048541069
0.051,0.049043595790863
0.05,0.0487813092768192
0.049,0.0485641174018383
0.048,0.0483217388391494
0.047,0.048120018094778
0.046,0.0479737594723701
0.045,0.0477515235543251
0.044,0.047537874430418
0.043,0.0473171733319759
0.042,0.047117456793785
0.041,0.0468685328960418
0.04,0.0466582253575325
0.039,0.0464131534099578
0.038,0.046202365309
0.037,0.0460076890885829
0.036,0.0457478389143943
0.035,0.0454618893563747
0.034,0.0451674647629261
0.033,0.0448846966028213
0.032,0.044637057930231
0.031,0.0442993752658367
0.03,0.0439232848584651
0.029,0.0436197407543659
0.028,0.0432739183306694
0.027,0.0429591313004493
0.026,0.042560551315546
0.025,0.0422041229903698
0.024,0.0418681763112545
0.023,0.041469931602478
0.022,0.0410824343562126
0.021,0.040752850472927
0.02,0.040291029959917
0.019,0.0398854985833168
0.018,0.0394390597939491
0.017,0.0389705151319503
0.016,0.0384892076253891
0.015,0.0379753485321998
0.014,0.0375368371605873
0.013,0.0370720736682415
0.012,0.036450657993555
0.011,0.0358943976461887
0.01,0.0353110469877719
0.009,0.0347564630210399
0.008,0.0341869443655014
0.007,0.0334126241505146
0.006,0.0326371416449546
0.005,0.0317549929022789
0.004,0.0308299381285905
0.003,0.0298953559249639
0.002,0.0273216944187879
0.001,0.0
"""

#Address Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_address_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskAddress_XGB_DEV_EXA_FMV_NSR_SMS_VCA_VCI_VNA_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.name="Address Risk Model (International) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.version="7.0"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.identifier="RiskAddress_XGB_DEV_EXA_FMV_NSR_SMS_VCA_VCI_VNA_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.params="""{"VCAVL.240099":null,"EXAVL.100008":null,"SMSVL.100023":null,"NSRVL.400008":null,"VCAVL.220902":null,"FMVAL.300082":null,"SMSVL.100026":null,"VNAVL.200007":null,"VCIVL.220907":null,"NSRVL.400001":null,"FMVAL.900017":null,"NSRVL.400006":null,"FMVAL.300083":null,"SMSVL.100027":null,"EXAVL.100002":null,"FMVAL.300022":null,"VCIVL.220906":null,"SMSVL.100021":null,"NSRVL.400003":null,"NSRVL.400007":null,"EXAVL.100006":null,"EXAVL.100007":null,"EXAVL.100000":null,"EXAVL.100005":null,"SMSVL.100025":null,"VCAVL.250099":null,"DEVAL.100029":null,"SMSVL.100011":null,"VCAVL.200002":null,"VCIVL.220105":null,"VCAVL.200005":null,"SMSVL.100004":null,"FMVAL.900019":null,"SMSVL.100020":null,"NSRVL.400005":null,"DEVAL.100031":null,"FMVAL.900016":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_address_int.low_risk.threshold=0.7507440447807312
datasci.model.defaults.v_3_0_0.pii_risk_address_int.high_risk.threshold=0.8536642789840698
datasci.model.defaults.v_3_0_0.pii_risk_address_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_address_int.quantile_mapping="""
quantile,score
0.999,0.9830442070961
0.998,0.9673813581466676
0.997,0.9566068649291992
0.996,0.948103368282318
0.995,0.9398042559623718
0.994,0.9331302046775818
0.993,0.9267323613166808
0.992,0.9211539030075072
0.991,0.9169634580612184
0.99,0.9128097891807556
0.989,0.9081830382347108
0.988,0.9045042991638184
0.987,0.9010014533996582
0.986,0.8987687826156616
0.985,0.8937651515007019
0.984,0.8899661302566528
0.983,0.8864817023277283
0.982,0.8831483125686646
0.981,0.8797873854637146
0.98,0.8766241073608398
0.979,0.8738760352134705
0.978,0.8673502802848816
0.977,0.8669694066047668
0.975,0.8669435381889343
0.973,0.8633930683135986
0.972,0.8602303266525269
0.971,0.8566792011260986
0.97,0.8536642789840698
0.969,0.8536435961723328
0.968,0.85113525390625
0.967,0.848621129989624
0.966,0.8465138673782349
0.965,0.8435532450675964
0.964,0.8411499857902527
0.963,0.8389254808425903
0.962,0.8366329073905945
0.961,0.8345696330070496
0.96,0.8328973650932312
0.959,0.8309244513511658
0.958,0.8290573358535767
0.957,0.8274863362312317
0.956,0.8257061243057251
0.955,0.8239971399307251
0.954,0.8224125504493713
0.953,0.8202726244926453
0.952,0.8186508417129517
0.951,0.816865861415863
0.95,0.8154308795928955
0.949,0.8141295909881592
0.948,0.8129687905311584
0.947,0.8116873502731323
0.946,0.8102093935012817
0.945,0.8090306520462036
0.944,0.8074285984039307
0.943,0.8059707283973694
0.942,0.8044505715370178
0.941,0.802842378616333
0.94,0.8018283843994141
0.939,0.8002651333808899
0.938,0.7986015677452087
0.937,0.79764324426651
0.936,0.7972684502601624
0.935,0.7966391444206238
0.934,0.7954883575439453
0.933,0.794039785861969
0.932,0.7928192019462585
0.931,0.7914536595344543
0.93,0.7904254794120789
0.929,0.7894271016120911
0.928,0.7881190776824951
0.927,0.7864707708358765
0.926,0.7858675718307495
0.925,0.7847270369529724
0.924,0.7834556102752686
0.923,0.7822309732437134
0.922,0.7810200452804565
0.921,0.7794797420501709
0.92,0.778429388999939
0.919,0.7772459983825684
0.918,0.7760384678840637
0.917,0.7746261358261108
0.916,0.7734025716781616
0.915,0.7722440958023071
0.914,0.7710165977478027
0.913,0.7698476314544678
0.912,0.7686057686805725
0.911,0.7676962018013
0.91,0.7661112546920776
0.909,0.7645096182823181
0.908,0.7629286646842957
0.907,0.7613325715065002
0.906,0.7599829435348511
0.905,0.7584580183029175
0.904,0.7570517659187317
0.903,0.7555472254753113
0.902,0.7540034651756287
0.901,0.7523880004882812
0.9,0.7507440447807312
0.899,0.7492907643318176
0.898,0.7476769089698792
0.897,0.7466398477554321
0.896,0.7450407147407532
0.895,0.7434784770011902
0.894,0.7422305941581726
0.893,0.7407742738723755
0.892,0.7390739917755127
0.891,0.7373544573783875
0.89,0.7359687089920044
0.889,0.7344868183135986
0.888,0.7330492734909058
0.887,0.7314888834953308
0.886,0.7302044630050659
0.885,0.7289258241653442
0.884,0.7274778485298157
0.883,0.7263558506965637
0.882,0.7256970405578613
0.881,0.7242813110351562
0.88,0.7230420112609863
0.879,0.721609354019165
0.878,0.7205005288124084
0.877,0.719202995300293
0.876,0.7181195020675659
0.875,0.7166650295257568
0.874,0.7154788374900818
0.873,0.714423656463623
0.872,0.7128154635429382
0.871,0.7115362286567688
0.87,0.7101466059684753
0.869,0.7087283730506897
0.868,0.7077597975730896
0.867,0.7063729166984558
0.866,0.705007016658783
0.865,0.7038249373435974
0.864,0.7025993466377258
0.863,0.7009985446929932
0.862,0.6999226808547974
0.861,0.6994026899337769
0.86,0.6985815763473511
0.859,0.6972835063934326
0.858,0.6961625218391418
0.857,0.6947413086891174
0.856,0.6939070820808411
0.855,0.6925427317619324
0.854,0.6909769773483276
0.853,0.6898548007011414
0.852,0.6886877417564392
0.851,0.6871460676193237
0.85,0.6856811046600342
0.849,0.6847482323646545
0.848,0.6832659840583801
0.847,0.6816017031669617
0.846,0.6801583170890808
0.845,0.6787047386169434
0.844,0.677373468875885
0.843,0.676004946231842
0.842,0.6746842265129089
0.841,0.67335444688797
0.84,0.6720070838928223
0.839,0.6703915596008301
0.838,0.6689501404762268
0.837,0.667669951915741
0.836,0.6664708256721497
0.835,0.664940595626831
0.834,0.6635131239891052
0.833,0.6621308922767639
0.832,0.6607353687286377
0.831,0.6593141555786133
0.83,0.6582386493682861
0.829,0.6569234132766724
0.828,0.6556538939476013
0.827,0.6543366312980652
0.826,0.6530125141143799
0.825,0.6518503427505493
0.824,0.6503080725669861
0.823,0.6490413546562195
0.822,0.647638201713562
0.821,0.6463298797607422
0.82,0.6450332403182983
0.819,0.6435847878456116
0.818,0.6422207355499268
0.817,0.6405993103981018
0.816,0.639276385307312
0.815,0.6380686163902283
0.814,0.6367131471633911
0.813,0.635567843914032
0.812,0.6343869566917419
0.811,0.6332874298095703
0.81,0.6318344473838806
0.809,0.6307013630867004
0.808,0.6295301914215088
0.807,0.6284250020980835
0.806,0.6276342272758484
0.805,0.6262027621269226
0.804,0.6250864863395691
0.803,0.6236045360565186
0.802,0.6225035786628723
0.801,0.6212769150733948
0.8,0.6201061010360718
0.799,0.6189671754837036
0.798,0.6176398396492004
0.797,0.6166136860847473
0.796,0.6151048541069031
0.795,0.6139087080955505
0.794,0.6131203174591064
0.793,0.6124364137649536
0.792,0.6111221313476562
0.791,0.6100482940673828
0.79,0.6088749766349792
0.789,0.6078593134880066
0.788,0.6068329215049744
0.787,0.6055983304977417
0.786,0.6045628190040588
0.785,0.6036500334739685
0.784,0.6023833751678467
0.783,0.6015099287033081
0.782,0.600357174873352
0.781,0.5993770956993103
0.78,0.598129153251648
0.779,0.5970013737678528
0.778,0.5960121750831604
0.777,0.5951672196388245
0.776,0.5943008661270142
0.775,0.5933734774589539
0.774,0.592365562915802
0.773,0.5917283892631531
0.772,0.5906813740730286
0.771,0.58965665102005
0.77,0.5887115597724915
0.769,0.588136613368988
0.768,0.5875135660171509
0.767,0.5865602493286133
0.766,0.5864285826683044
0.765,0.5858075022697449
0.764,0.5848393440246582
0.763,0.5843018293380737
0.762,0.5835295915603638
0.761,0.5825821161270142
0.76,0.5816152691841125
0.759,0.5809471011161804
0.758,0.5799340605735779
0.757,0.57906574010849
0.756,0.5782005190849304
0.755,0.5770606994628906
0.754,0.5766310691833496
0.753,0.5734184384346008
0.751,0.5730574727058411
0.75,0.5722230672836304
0.749,0.5713211894035339
0.748,0.5707436203956604
0.747,0.5697598457336426
0.746,0.569096565246582
0.745,0.5677917003631592
0.744,0.5674627423286438
0.743,0.5648770332336426
0.741,0.5644223690032959
0.74,0.5634292960166931
0.739,0.5625433921813965
0.738,0.5618077516555786
0.737,0.5605686902999878
0.736,0.5597578883171082
0.735,0.5589461326599121
0.734,0.5574966669082642
0.733,0.5570592880249023
0.732,0.5565429329872131
0.731,0.556048572063446
0.73,0.5556723475456238
0.729,0.554665207862854
0.728,0.5540358424186707
0.727,0.5536866188049316
0.726,0.5529691576957703
0.725,0.5525081157684326
0.724,0.5525036454200745
0.723,0.5507889986038208
0.722,0.5507880449295044
0.721,0.5495017766952515
0.72,0.5488932132720947
0.719,0.5482891798019409
0.718,0.5474947094917297
0.717,0.5466615557670593
0.716,0.5456206798553467
0.715,0.5451239943504333
0.714,0.544409990310669
0.713,0.5442273616790771
0.712,0.5428348779678345
0.711,0.5425295233726501
0.708,0.5397852659225464
0.707,0.5390545129776001
0.706,0.5384423732757568
0.705,0.5377925634384155
0.704,0.5369277596473694
0.703,0.5359581708908081
0.702,0.5354042053222656
0.701,0.5346982479095459
0.7,0.5346967577934265
0.699,0.5331985354423523
0.698,0.5327056050300598
0.697,0.5319253206253052
0.696,0.5315471291542053
0.695,0.5305581092834473
0.694,0.5299356579780579
0.693,0.5290685296058655
0.692,0.5288008451461792
0.691,0.5277114510536194
0.69,0.5272969603538513
0.689,0.5266284942626953
0.688,0.5260748863220215
0.687,0.5256012082099915
0.686,0.5250744819641113
0.685,0.5244240164756775
0.684,0.5239570140838623
0.683,0.5228738784790039
0.682,0.522684633731842
0.68,0.5226836800575256
0.679,0.5203961133956909
0.678,0.5198226571083069
0.677,0.5191423892974854
0.676,0.5187138915061951
0.675,0.5181032419204712
0.674,0.5175314545631409
0.673,0.5171737670898438
0.672,0.5168497562408447
0.671,0.5168477892875671
0.67,0.5153448581695557
0.669,0.5148788094520569
0.668,0.5142755508422852
0.667,0.5139068961143494
0.666,0.5130529999732971
0.665,0.5126171112060547
0.664,0.5123948454856873
0.663,0.5123948454856873
0.662,0.5109897255897522
0.661,0.5105442404747009
0.66,0.5100111961364746
0.659,0.5097942352294922
0.658,0.5094180703163147
0.657,0.5089413523674011
0.656,0.5082863569259644
0.655,0.5076727867126465
0.654,0.507352888584137
0.653,0.5064646601676941
0.652,0.506103515625
0.651,0.5055707097053528
0.65,0.5050482153892517
0.649,0.5048831105232239
0.648,0.5046632885932922
0.647,0.5043251514434814
0.646,0.5038723945617676
0.645,0.5035342574119568
0.644,0.5027761459350586
0.643,0.5022445321083069
0.642,0.501719057559967
0.641,0.501374363899231
0.64,0.5011815428733826
0.639,0.5008330345153809
0.638,0.5004258155822754
0.637,0.5001429319381714
0.636,0.4996047019958496
0.635,0.4992394745349884
0.634,0.4988228380680084
0.633,0.4984407424926758
0.632,0.4977060854434967
0.631,0.4973475039005279
0.63,0.4968350529670715
0.629,0.4965098798274994
0.628,0.4961315393447876
0.627,0.4956818222999573
0.626,0.4952675402164459
0.625,0.4949080348014831
0.624,0.4945282638072967
0.623,0.4939727187156677
0.622,0.4934535622596741
0.621,0.4928057789802551
0.62,0.4921612441539764
0.619,0.4916751980781555
0.618,0.4912690818309784
0.617,0.4910717904567718
0.616,0.4909534752368927
0.614,0.4895093441009521
0.613,0.4884728193283081
0.612,0.487946093082428
0.611,0.4873247742652893
0.61,0.4866784512996673
0.609,0.4860420823097229
0.608,0.4857414960861206
0.607,0.4851650297641754
0.606,0.4849603176116943
0.605,0.4849603176116943
0.604,0.4839348495006561
0.603,0.4836912453174591
0.602,0.4832266569137573
0.601,0.4826248586177826
0.6,0.4822330474853515
0.599,0.4820581376552582
0.597,0.481068879365921
0.596,0.4805683493614197
0.595,0.4801191985607147
0.594,0.4801191985607147
0.593,0.4801120460033417
0.592,0.4797545373439789
0.591,0.4795281291007995
0.59,0.4790463745594024
0.589,0.4788938462734222
0.588,0.4783927798271179
0.587,0.4780409932136535
0.586,0.4774759709835052
0.585,0.476918876171112
0.584,0.4763273298740387
0.583,0.4760008156299591
0.582,0.4758580327033996
0.581,0.4750464558601379
0.58,0.4750091135501861
0.579,0.4749511480331421
0.578,0.4746793806552887
0.577,0.4744206368923187
0.576,0.4741595983505249
0.575,0.4737212061882019
0.571,0.4727774858474731
0.567,0.4726462960243225
0.566,0.4723559916019439
0.565,0.4722911119461059
0.563,0.4706794917583465
0.562,0.4702498316764831
0.561,0.470199316740036
0.56,0.4699408710002899
0.559,0.4696054458618164
0.558,0.4693540930747986
0.557,0.4687142372131347
0.556,0.4684553146362304
0.555,0.4680719673633575
0.554,0.467645913362503
0.553,0.4671495854854584
0.552,0.4665185511112213
0.551,0.466416448354721
0.55,0.466416448354721
0.549,0.4659802913665771
0.548,0.4657545387744903
0.547,0.4651489555835724
0.546,0.4648767411708832
0.545,0.4648570716381073
0.544,0.4645133316516876
0.543,0.4639630019664764
0.542,0.4633927345275879
0.541,0.4630092978477478
0.54,0.4625645577907562
0.539,0.4620572030544281
0.538,0.461742639541626
0.537,0.4614728987216949
0.536,0.4612491428852081
0.535,0.4612491428852081
0.534,0.4609217345714569
0.533,0.4603570699691772
0.532,0.4600452184677124
0.531,0.4596759974956512
0.53,0.4592715203762054
0.529,0.4588622748851776
0.528,0.4588020443916321
0.527,0.4578917026519775
0.526,0.4574790596961975
0.525,0.4573078453540802
0.524,0.4568743705749511
0.523,0.4564231336116791
0.522,0.4564231336116791
0.521,0.4561980366706848
0.52,0.455852061510086
0.519,0.4553481340408325
0.518,0.4551846981048584
0.517,0.4546882808208465
0.516,0.454076886177063
0.515,0.4536679089069366
0.514,0.4532272517681122
0.513,0.4527723789215088
0.512,0.4519599080085754
0.511,0.451735258102417
0.51,0.4515411555767059
0.509,0.4513806700706482
0.508,0.4513806700706482
0.482,0.4513806700706482
0.455,0.435930997133255
0.454,0.4353179335594177
0.453,0.4348925352096557
0.452,0.4343893229961395
0.451,0.4339996874332428
0.45,0.4337459206581116
0.449,0.4332999885082245
0.448,0.4330336451530456
0.447,0.4329528510570526
0.446,0.4328346848487854
0.445,0.432347297668457
0.444,0.4320696592330932
0.443,0.4315902888774872
0.442,0.4312835931777954
0.441,0.4311460256576538
0.42,0.431145578622818
0.398,0.4172432124614715
0.397,0.4162550270557403
0.396,0.4158060550689697
0.395,0.4153168797492981
0.394,0.4151634275913238
0.393,0.4149763286113739
0.392,0.4146225154399872
0.391,0.4146173596382141
0.39,0.4139348864555359
0.389,0.4135995507240295
0.388,0.4131816923618316
0.387,0.4128134250640869
0.386,0.4122419655323028
0.385,0.4119737446308136
0.384,0.411313533782959
0.383,0.410816490650177
0.382,0.4103178083896637
0.381,0.4097478985786438
0.38,0.4093571901321411
0.379,0.4092494249343872
0.378,0.4084904491901397
0.377,0.4078971743583679
0.376,0.4077228307723999
0.374,0.4050379395484924
0.371,0.404685914516449
0.37,0.4041971266269684
0.369,0.4038900732994079
0.367,0.4028828740119934
0.366,0.4025164246559143
0.365,0.4019305109977722
0.364,0.4013244807720184
0.363,0.4007535874843597
0.362,0.4004392027854919
0.361,0.400384247303009
0.36,0.3992465436458587
0.359,0.3985138535499573
0.358,0.3982440531253814
0.357,0.3974967300891876
0.356,0.3971541821956634
0.355,0.3969485759735107
0.354,0.3953070938587188
0.352,0.3949978947639465
0.351,0.3946983516216278
0.349,0.3928502202033996
0.348,0.3926937282085418
0.347,0.3923459649085998
0.346,0.3915870785713196
0.345,0.3912648856639862
0.344,0.3898977041244507
0.343,0.3897331655025482
0.342,0.389380931854248
0.34,0.3865423798561096
0.338,0.3863777816295624
0.337,0.3859058320522308
0.336,0.3851116299629211
0.335,0.3841726779937744
0.334,0.383615493774414
0.333,0.3833125531673431
0.332,0.3821013271808624
0.331,0.3814795315265655
0.33,0.3809230327606201
0.329,0.3809201419353485
0.328,0.3793916702270508
0.327,0.3788410425186157
0.326,0.3783228993415832
0.325,0.377988189458847
0.323,0.3779863119125366
0.321,0.3752125203609466
0.32,0.3745517432689667
0.319,0.3740745484828949
0.318,0.3740740120410919
0.317,0.3728016316890716
0.316,0.3718757033348083
0.315,0.3709558248519897
0.314,0.3705476224422455
0.313,0.3692081272602081
0.312,0.3684606552124023
0.311,0.3676483035087585
0.31,0.3669398725032806
0.309,0.3660812377929687
0.308,0.3654226064682007
0.307,0.3649258017539978
0.306,0.3638963401317596
0.305,0.3631437420845032
0.304,0.3619436621665954
0.303,0.3611689209938049
0.302,0.3605402410030365
0.301,0.3591854572296142
0.3,0.3585347235202789
0.299,0.3579180538654327
0.298,0.356871098279953
0.297,0.3553666174411773
0.296,0.3543000221252441
0.295,0.3533005714416504
0.294,0.3522701561450958
0.293,0.3512309789657593
0.292,0.350699782371521
0.291,0.3498267531394958
0.29,0.3484892845153808
0.289,0.3471197783946991
0.288,0.3461599051952362
0.287,0.3448560833930969
0.286,0.3442083299160003
0.285,0.3433344960212707
0.284,0.3423426747322082
0.283,0.3414783775806427
0.282,0.340490847826004
0.281,0.3395242094993591
0.28,0.3388798236846924
0.279,0.3380392789840698
0.278,0.3370607495307922
0.277,0.3362875878810882
0.276,0.3351903259754181
0.275,0.3342801928520202
0.274,0.3332678377628326
0.273,0.3324727118015289
0.272,0.3314952552318573
0.271,0.3305707573890686
0.27,0.3295659720897674
0.269,0.3284211456775665
0.268,0.3276225924491882
0.267,0.3267917037010193
0.266,0.3258133828639984
0.265,0.3247463703155517
0.264,0.3240214586257934
0.263,0.32298544049263
0.262,0.3219374120235443
0.261,0.3210646510124206
0.26,0.3205263912677765
0.259,0.3194208145141601
0.258,0.3188023865222931
0.257,0.3179716169834137
0.256,0.3169094026088714
0.255,0.3161341249942779
0.254,0.3146914541721344
0.253,0.3136849105358124
0.252,0.3127982914447784
0.251,0.3118060827255249
0.25,0.3106902539730072
0.249,0.3097632229328155
0.248,0.3086500465869903
0.247,0.3076507747173309
0.246,0.3066110908985138
0.245,0.305726945400238
0.244,0.304382860660553
0.243,0.3035542666912079
0.242,0.3023484647274017
0.241,0.3017411231994629
0.24,0.3006143569946289
0.239,0.2993821501731872
0.238,0.2985236942768097
0.237,0.2975268363952636
0.236,0.2966260015964508
0.235,0.2954815924167633
0.234,0.2947733104228973
0.233,0.2935007512569427
0.232,0.2923937141895294
0.231,0.2912174463272095
0.23,0.2902868092060089
0.229,0.2890964448451996
0.228,0.2880866229534149
0.227,0.2868900895118713
0.226,0.2862724959850311
0.225,0.2856683135032654
0.224,0.2845129668712616
0.223,0.2836983799934387
0.222,0.2830589115619659
0.221,0.2820018529891968
0.22,0.2808427512645721
0.219,0.2796318233013153
0.218,0.2784467339515686
0.217,0.2774536907672882
0.216,0.2762057781219482
0.215,0.2752464115619659
0.214,0.2744318544864654
0.213,0.2727649509906769
0.212,0.2719943225383758
0.211,0.2710117697715759
0.21,0.2699836194515228
0.209,0.2691987454891205
0.208,0.2683312892913818
0.207,0.2675306499004364
0.206,0.2667694389820099
0.205,0.2661254107952118
0.204,0.2652438580989837
0.203,0.2640952467918396
0.202,0.2626546919345855
0.201,0.2617264688014984
0.2,0.2615030407905578
0.199,0.2606257796287536
0.198,0.2595825791358948
0.197,0.2591773569583893
0.196,0.2591773569583893
0.195,0.2581714689731598
0.194,0.2569086849689483
0.193,0.2559544742107391
0.192,0.2554630637168884
0.191,0.254913717508316
0.19,0.2541986107826233
0.189,0.2537332475185394
0.188,0.2529258728027344
0.187,0.2515372335910797
0.186,0.2506352066993713
0.185,0.2500039935111999
0.184,0.2488552778959274
0.183,0.2476935535669326
0.182,0.2470146566629409
0.181,0.2461355924606323
0.18,0.2450043261051178
0.179,0.2441139221191406
0.178,0.2434570640325546
0.177,0.2426929175853729
0.176,0.2418124675750732
0.175,0.2418086528778076
0.174,0.2400886714458465
0.173,0.2396379262208938
0.172,0.2384450584650039
0.171,0.2374867051839828
0.17,0.2368683665990829
0.169,0.2353676408529281
0.168,0.2348381429910659
0.167,0.2337541729211807
0.166,0.2327108532190323
0.165,0.2317587435245514
0.164,0.2308739125728607
0.163,0.229509025812149
0.162,0.2288034409284591
0.161,0.227763146162033
0.16,0.226833313703537
0.159,0.2262649834156036
0.157,0.2238776981830597
0.156,0.2237338721752166
0.155,0.2224563956260681
0.154,0.2216852754354477
0.153,0.2204053848981857
0.152,0.2202041447162628
0.151,0.2189170718193054
0.15,0.2177881449460983
0.149,0.217061847448349
0.148,0.2163042873144149
0.147,0.2159712016582489
0.146,0.2149989008903503
0.145,0.2139230668544769
0.144,0.2137061655521392
0.143,0.2129240483045578
0.142,0.2123598456382751
0.141,0.2109236717224121
0.14,0.2100127935409546
0.139,0.2089227586984634
0.138,0.2078697681427002
0.137,0.2070689052343368
0.136,0.2058255672454834
0.135,0.2050394266843795
0.134,0.2041021436452865
0.133,0.2033118158578872
0.132,0.2022439837455749
0.131,0.2011231034994125
0.13,0.2003138363361358
0.129,0.1995400190353393
0.128,0.1980579048395156
0.127,0.1976208984851837
0.126,0.1966779679059982
0.125,0.1962508261203766
0.123,0.1917313486337661
0.121,0.1912079155445099
0.12,0.1906483322381973
0.119,0.190643697977066
0.118,0.1882636994123459
0.117,0.187684953212738
0.116,0.186389684677124
0.115,0.1853853613138198
0.114,0.1849598586559295
0.113,0.1833549737930297
0.112,0.1831812113523483
0.111,0.1831812113523483
0.109,0.1788291931152343
0.108,0.1776716262102127
0.107,0.1768820434808731
0.106,0.1758196204900741
0.105,0.1746918708086013
0.104,0.1744978278875351
0.103,0.1744973063468933
0.101,0.1709706783294677
0.1,0.1696830093860626
0.099,0.1694252341985702
0.097,0.1694145947694778
0.095,0.165242999792099
0.094,0.162623256444931
0.092,0.1614026874303817
0.091,0.160478800535202
0.09,0.1596941202878952
0.089,0.1588964164257049
0.088,0.1574894338846206
0.087,0.1560745537281036
0.086,0.1552959978580474
0.085,0.1549587696790695
0.084,0.1549560278654098
0.083,0.1531238704919815
0.081,0.1531098037958145
0.08,0.1487340331077575
0.079,0.1476224362850189
0.078,0.1466905772686004
0.073,0.135987639427185
0.068,0.1356926262378692
0.067,0.1339043229818344
0.066,0.1328899413347244
0.065,0.1319478750228881
0.061,0.1319478750228881
0.057,0.1220547184348106
0.056,0.1215147897601127
0.055,0.1193642169237136
0.054,0.11841481924057
0.053,0.118311807513237
0.05,0.1182846873998642
0.046,0.1084381267428398
0.045,0.1064556613564491
0.044,0.105698674917221
0.043,0.1053245216608047
0.042,0.1045777946710586
0.041,0.1032834872603416
0.04,0.102326326072216
0.039,0.1000917926430702
0.038,0.0991723015904426
0.037,0.0977326184511184
0.036,0.097481332719326
0.035,0.0974799618124961
0.034,0.0931013822555542
0.033,0.0928483456373214
0.032,0.0928447246551513
0.03,0.0874990001320838
0.029,0.0865348875522613
0.028,0.0859345123171806
0.027,0.0838495343923568
0.026,0.08238086104393
0.025,0.0810668244957923
0.023,0.0810663178563118
0.022,0.0755125880241394
0.021,0.0747304037213325
0.02,0.0727116018533706
0.019,0.0705611929297447
0.018,0.0701931342482566
0.017,0.0678475201129913
0.016,0.0647568255662918
0.015,0.0632029175758361
0.014,0.0613208524882793
0.013,0.0586528405547142
0.012,0.0559671260416507
0.011,0.0539834350347518
0.009,0.0448794700205326
0.008,0.0426531471312046
0.007,0.040104653686285
0.006,0.0381998531520366
0.005,0.0379358232021331
0.004,0.0264826864004135
0.003,0.023340793326497
0.002,0.0189888887107372
0.001,0.0
"""

#Email Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_email_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskEmail_XGB_DEV_EXE_FCE_FMV_NSR_SSE_TDE_VCE_20220311"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.name="Email Risk Model (International) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.version="8.0"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.identifier="RiskEmail_XGB_DEV_EXE_FCE_FMV_NSR_SSE_TDE_VCE_20220311"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.params="""{"EXEVL.100006":null,"TDEVL.200008":null,"TDEVL.500005":null,"SSEVL.100005":null,"FCEVL.100015":null,"FCEVL.100069":null,"FCEVL.100036":null,"TDEVL.100007":null,"FCEVL.100000":null,"FCEVL.100004":null,"FCEVL.100058":null,"TDEVL.100006":null,"DEVAL.100061":null,"EXEVL.100036":null,"SSEVL.100031":null,"NSRVL.300005":null,"SSEVL.100006":null,"SSEVL.100019":null,"FMVAL.300003":null,"TDEVL.200007":null,"FCEVL.100008":null,"EXEVL.100025":null,"DEVAL.100034":null,"NSRVL.300008":null,"EXEVL.100013":null,"FMVAL.600027":null,"SSEVL.100007":null,"SSEVL.100029":null,"FCEVL.100017":null,"FCEVL.100007":null,"TDEVL.400005":null,"EXEVL.100028":null,"SSEVL.100033":null,"TDEVL.100004":null,"TDEVL.100016":null,"EXEVL.100015":null,"DEVAL.100004":null,"FCEVL.100019":null,"TDEVL.400004":null,"VCEVL.200006":null,"DEVAL.100045":null,"EXEVL.100011":null,"FCEVL.100006":null,"EXEVL.100023":null,"FMVAL.600013":null,"FMVAL.600025":null,"FCEVL.100068":null,"DEVAL.100053":null,"TDEVL.100005":null,"DEVAL.100052":null,"NSRVL.100009":null,"SSEVL.100004":null,"NSRVL.300001":null,"FMVAL.600012":null,"FCEVL.100067":null,"EXEVL.100037":null,"SSEVL.100001":null,"FCEVL.100002":null,"EXEVL.100012":null,"VCEVL.200009":null,"FCEVL.100018":null,"EXEVL.100022":null,"SSEVL.100002":null,"EXEVL.100033":null,"FCEVL.100001":null,"EXEVL.100004":null,"FCEVL.100005":null,"EXEVL.100024":null,"VCEVL.211106":null,"EXEVL.100002":null,"VCEVL.240099":null,"TDEVL.100002":null,"NSRVL.300004":null,"FMVAL.600001":null,"TDEVL.100008":null,"TDEVL.400001":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_email_int.low_risk.threshold=0.9745648503303528
datasci.model.defaults.v_3_0_0.pii_risk_email_int.high_risk.threshold=0.9892882108688354
datasci.model.defaults.v_3_0_0.pii_risk_email_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_email_int.quantile_mapping="""
quantile,score
0.999,0.9946267008781432
0.998,0.9941422343254088
0.997,0.9939078092575072
0.996,0.9936543703079224
0.995,0.9933078289031982
0.994,0.9928594827651978
0.993,0.9926164746284484
0.992,0.9923700094223022
0.991,0.9921448826789856
0.99,0.9919346570968628
0.989,0.9917451739311218
0.988,0.991550087928772
0.987,0.9914186000823976
0.986,0.9912680387496948
0.985,0.9911590218544006
0.984,0.9909895658493042
0.983,0.9908437728881836
0.982,0.9907466173171996
0.981,0.990631341934204
0.98,0.990512192249298
0.979,0.990391969680786
0.978,0.9902495741844176
0.977,0.9901071786880492
0.976,0.9900012016296388
0.975,0.9898830056190492
0.974,0.9897537231445312
0.973,0.9896174669265748
0.972,0.9895116686820984
0.971,0.9894078969955444
0.97,0.9892882108688354
0.969,0.9891441464424132
0.968,0.9890426993370056
0.967,0.988934338092804
0.966,0.9888303875923156
0.965,0.9886571764945984
0.964,0.9885286688804626
0.963,0.9883508682250975
0.962,0.9882105588912964
0.961,0.9880141615867616
0.96,0.9878408312797546
0.959,0.9876922965049744
0.958,0.987542450428009
0.957,0.9873961806297302
0.956,0.9871972799301147
0.955,0.9870063066482544
0.954,0.986825168132782
0.953,0.9866699576377868
0.952,0.9865277409553528
0.951,0.9863852858543396
0.95,0.9862213134765624
0.949,0.9860799908638
0.948,0.9859551787376404
0.947,0.9857926964759828
0.946,0.9856138825416564
0.945,0.9854715466499328
0.944,0.9852983951568604
0.943,0.9851674437522888
0.942,0.984972596168518
0.941,0.9848072528839112
0.94,0.9845906496047974
0.939,0.9844592809677124
0.938,0.9842764139175416
0.937,0.9840736389160156
0.936,0.9838191866874696
0.935,0.983623206615448
0.934,0.9833973050117492
0.933,0.9832599759101868
0.932,0.9831017851829528
0.931,0.9829323291778564
0.93,0.9827894568443298
0.929,0.9825804829597472
0.928,0.9823681712150574
0.927,0.9822030067443848
0.926,0.9819703102111816
0.925,0.9817264676094056
0.924,0.9815250635147096
0.923,0.9813343286514282
0.922,0.9811018109321594
0.921,0.9808236360549928
0.92,0.9805893898010254
0.919,0.980322539806366
0.918,0.9800801873207092
0.917,0.979855179786682
0.916,0.9796263575553894
0.915,0.97941255569458
0.914,0.9791169762611388
0.913,0.9788418412208556
0.912,0.9786044359207152
0.911,0.9783541560173036
0.91,0.9780129790306092
0.909,0.9777878522872924
0.908,0.9774213433265686
0.907,0.9770734310150146
0.906,0.9767309427261353
0.905,0.9763656854629515
0.904,0.976024091243744
0.903,0.9757180213928224
0.902,0.9752996563911438
0.901,0.9749324321746826
0.9,0.9745648503303528
0.899,0.97408527135849
0.898,0.9737713932991028
0.897,0.9733358025550842
0.896,0.972984254360199
0.895,0.9726294279098512
0.894,0.9721883535385132
0.893,0.971786618232727
0.892,0.9713882803916932
0.891,0.9709389805793762
0.89,0.970458686351776
0.889,0.9700177907943726
0.888,0.969353199005127
0.887,0.9688066244125366
0.886,0.9682040214538574
0.885,0.9676923751831056
0.884,0.9671686887741088
0.883,0.9666807651519777
0.882,0.9662367105484008
0.881,0.9657093286514282
0.88,0.9651607871055604
0.879,0.9646402597427368
0.878,0.964103639125824
0.877,0.9635515809059144
0.876,0.963074028491974
0.875,0.962611436843872
0.874,0.9620861411094666
0.873,0.961527705192566
0.872,0.9610896706581116
0.871,0.9606165289878844
0.87,0.9601516723632812
0.869,0.959604799747467
0.868,0.9591064453125
0.867,0.9586076736450196
0.866,0.9581841230392456
0.865,0.9576879143714904
0.864,0.9571743607521056
0.863,0.9567953944206238
0.862,0.9564078450202942
0.861,0.9560911655426024
0.86,0.9557766318321228
0.859,0.9554212093353271
0.858,0.95507150888443
0.857,0.9547101855278016
0.856,0.9544256329536438
0.855,0.9541158080101012
0.854,0.953783631324768
0.853,0.9534220099449158
0.852,0.9530594348907472
0.851,0.9527631402015686
0.85,0.95237398147583
0.849,0.9520647525787354
0.848,0.951783299446106
0.847,0.9514157772064208
0.846,0.9511101841926576
0.845,0.950839638710022
0.844,0.950522243976593
0.843,0.9502573013305664
0.842,0.9499216675758362
0.841,0.949825406074524
0.84,0.9494733214378356
0.839,0.9491612315177916
0.838,0.9488497376441956
0.837,0.9485188722610474
0.836,0.9482558369636536
0.835,0.947942316532135
0.834,0.9476398825645448
0.833,0.9473448395729064
0.832,0.9470798373222352
0.831,0.9467175602912904
0.83,0.946483314037323
0.829,0.94620019197464
0.828,0.9459283351898192
0.827,0.9455904364585876
0.826,0.9453554749488832
0.825,0.9450703859329224
0.824,0.944724142551422
0.823,0.9444551467895508
0.822,0.944223165512085
0.821,0.9439858198165894
0.82,0.9436535239219666
0.819,0.9433906674385072
0.818,0.9430685639381408
0.817,0.9428114295005798
0.816,0.9425106644630432
0.815,0.9420989155769348
0.814,0.9417322278022766
0.813,0.9413938522338868
0.812,0.9411975145339966
0.811,0.9408788084983826
0.81,0.9406023621559144
0.809,0.9402875304222108
0.808,0.9399738907814026
0.807,0.9396948218345642
0.806,0.9394031167030334
0.805,0.9391191005706788
0.804,0.9389050006866456
0.803,0.9386656284332277
0.802,0.9383087754249572
0.801,0.938056707382202
0.8,0.9377169609069824
0.799,0.9374003410339355
0.798,0.9371749758720398
0.797,0.9368811249732972
0.796,0.936603844165802
0.795,0.936284065246582
0.794,0.9359871745109558
0.793,0.935653805732727
0.792,0.9353604316711426
0.791,0.9350371360778807
0.79,0.9347106218338012
0.789,0.9343411326408386
0.788,0.9340525269508362
0.787,0.9337401986122132
0.786,0.9334561228752136
0.785,0.9331777095794678
0.784,0.93303781747818
0.783,0.9327256679534912
0.782,0.9323861002922058
0.781,0.9321202635765076
0.78,0.9318429827690125
0.779,0.9315268397331238
0.778,0.9312520623207092
0.777,0.9309282898902892
0.776,0.9307153224945068
0.775,0.9303430318832396
0.774,0.9299681782722472
0.773,0.9297068119049072
0.772,0.92929208278656
0.771,0.9289279580116272
0.77,0.9285811185836792
0.769,0.9282383918762208
0.768,0.927872359752655
0.767,0.9276033043861388
0.766,0.927316427230835
0.765,0.9269307255744934
0.764,0.9265618324279784
0.763,0.9262419939041138
0.762,0.925917625427246
0.761,0.9255992770195008
0.76,0.925283908843994
0.759,0.9248773455619812
0.758,0.92466938495636
0.757,0.9242452383041382
0.756,0.9239715933799744
0.755,0.9235526919364928
0.754,0.9232332110404968
0.753,0.9229488968849182
0.752,0.922612190246582
0.751,0.9222684502601624
0.75,0.921894371509552
0.749,0.9214819669723512
0.748,0.9210475087165833
0.747,0.9206750988960266
0.746,0.9203029870986938
0.745,0.919889271259308
0.744,0.9195270538330078
0.743,0.919152557849884
0.742,0.9186684489250184
0.741,0.9182680249214172
0.74,0.9178873300552368
0.739,0.9174380898475648
0.738,0.9170482158660888
0.737,0.9166671633720398
0.736,0.9162437915802002
0.735,0.9158461093902588
0.734,0.9154471755027772
0.733,0.9150746464729308
0.732,0.9146281480789183
0.731,0.91416198015213
0.73,0.9136999249458312
0.729,0.9133175611495972
0.728,0.9128249883651732
0.727,0.9123337864875792
0.726,0.91189444065094
0.725,0.9115320444107056
0.724,0.9110684394836426
0.723,0.910651445388794
0.722,0.9102177023887634
0.721,0.9098336696624756
0.72,0.9092993140220642
0.719,0.9090060591697692
0.718,0.908608615398407
0.717,0.9081773161888124
0.716,0.9077006578445436
0.715,0.9071677327156068
0.714,0.9067059755325316
0.713,0.9062731862068176
0.712,0.905907154083252
0.711,0.905440866947174
0.71,0.9049742221832277
0.709,0.9044575691223145
0.708,0.9039674997329712
0.707,0.9035214185714722
0.706,0.9030529856681824
0.705,0.9026548266410828
0.704,0.9021986722946168
0.703,0.9018136858940125
0.702,0.9013809561729432
0.701,0.90091073513031
0.7,0.9005568623542786
0.699,0.9000608921051025
0.698,0.8996601104736328
0.697,0.8992281556129456
0.696,0.8987516164779663
0.695,0.898387610912323
0.694,0.8979465961456299
0.693,0.8974243998527527
0.692,0.8969859480857849
0.691,0.8964440822601318
0.69,0.8958069086074829
0.689,0.895357608795166
0.688,0.8948891162872314
0.687,0.8944570422172546
0.686,0.894012451171875
0.685,0.8934907913208008
0.684,0.8930146098136902
0.683,0.8926055431365967
0.682,0.8921086192131042
0.681,0.8917055130004883
0.68,0.8911989331245422
0.679,0.8906767964363098
0.678,0.8901200294494629
0.677,0.8895864486694336
0.676,0.8891509771347046
0.675,0.8887481093406677
0.674,0.888248860836029
0.673,0.887782871723175
0.672,0.8873283863067627
0.671,0.8868291974067688
0.67,0.8863220810890198
0.669,0.8858475685119629
0.668,0.8855047821998596
0.667,0.8849945664405823
0.666,0.8843482136726379
0.665,0.8839123249053955
0.664,0.8833770751953125
0.663,0.8828929662704468
0.662,0.8823733925819397
0.661,0.8819059133529663
0.66,0.8813650012016296
0.659,0.8807847499847412
0.658,0.8802872896194458
0.657,0.8797791600227356
0.656,0.8792745471000671
0.655,0.8788350224494934
0.654,0.878338634967804
0.653,0.8777943849563599
0.652,0.8773334622383118
0.651,0.8768674731254578
0.65,0.8764247298240662
0.649,0.8759018182754517
0.648,0.8752902150154114
0.647,0.8746593594551086
0.646,0.8741769194602966
0.645,0.8737103939056396
0.644,0.8731696605682373
0.643,0.8726657032966614
0.642,0.8722428679466248
0.641,0.871780514717102
0.64,0.8712937235832214
0.639,0.8707387447357178
0.638,0.8702797293663025
0.637,0.8697181344032288
0.636,0.869310200214386
0.635,0.8687338829040527
0.634,0.8682772517204285
0.633,0.8677894473075867
0.632,0.8671905398368835
0.631,0.8666743040084839
0.63,0.8662036657333374
0.629,0.8656080961227417
0.628,0.8651540279388428
0.627,0.8646922707557678
0.626,0.8642338514328003
0.625,0.8637340664863586
0.624,0.8633155822753906
0.623,0.8628906607627869
0.622,0.8624566793441772
0.621,0.8618230223655701
0.62,0.8612935543060303
0.619,0.8607051372528076
0.618,0.8601539134979248
0.617,0.8596628308296204
0.616,0.8591119647026062
0.615,0.858683705329895
0.614,0.8581253886222839
0.613,0.8576202988624573
0.612,0.8570523262023926
0.611,0.8564679622650146
0.61,0.8559613823890686
0.609,0.8553425073623657
0.608,0.8548423051834106
0.607,0.8543075919151306
0.606,0.853750467300415
0.605,0.8532240390777588
0.604,0.8526188135147095
0.603,0.8520087599754333
0.602,0.8514529466629028
0.601,0.8509587049484253
0.6,0.8503131866455078
0.599,0.8496691584587097
0.598,0.849190890789032
0.597,0.8485842347145081
0.596,0.8480783700942993
0.595,0.8474311232566833
0.594,0.8469438552856445
0.593,0.8464454412460327
0.592,0.8458694815635681
0.591,0.845291018486023
0.59,0.8446719646453857
0.589,0.8440850973129272
0.588,0.8435266017913818
0.587,0.842866837978363
0.586,0.8423991203308105
0.585,0.8418708443641663
0.584,0.8412582278251648
0.583,0.8408012986183167
0.582,0.8402970433235168
0.581,0.8397542834281921
0.58,0.8393101692199707
0.579,0.8387672901153564
0.578,0.8383492231369019
0.577,0.8377779126167297
0.576,0.8372728824615479
0.575,0.8367634415626526
0.574,0.8362101912498474
0.573,0.8355543613433838
0.572,0.8350762128829956
0.571,0.8344319462776184
0.57,0.8339004516601562
0.569,0.8332622051239014
0.568,0.8327251076698303
0.567,0.8322630524635315
0.566,0.8316890001296997
0.565,0.8310218453407288
0.564,0.8303456902503967
0.563,0.8297364115715027
0.562,0.8291165232658386
0.561,0.8286513090133667
0.56,0.8282415866851807
0.559,0.8276568055152893
0.558,0.8270482420921326
0.557,0.8264313340187073
0.556,0.8258660435676575
0.555,0.8252843022346497
0.554,0.8247846961021423
0.553,0.8241292238235474
0.552,0.8235077261924744
0.551,0.8229525685310364
0.55,0.8225123882293701
0.549,0.8218908309936523
0.548,0.8214361071586609
0.547,0.8208801746368408
0.546,0.820311427116394
0.545,0.8198035359382629
0.544,0.8192833065986633
0.543,0.8187493681907654
0.542,0.8183221220970154
0.541,0.8178889155387878
0.54,0.8173338770866394
0.539,0.8166977763175964
0.538,0.816128134727478
0.537,0.8155568242073059
0.536,0.8150656223297119
0.535,0.8143837451934814
0.534,0.813839852809906
0.533,0.8133246302604675
0.532,0.8127697706222534
0.531,0.8122989535331726
0.53,0.8117460608482361
0.529,0.8111168742179871
0.528,0.8105555772781372
0.527,0.8099226951599121
0.526,0.8093987703323364
0.525,0.8089420199394226
0.524,0.8084242939949036
0.523,0.807876706123352
0.522,0.8072918653488159
0.521,0.8068172335624695
0.52,0.8062142133712769
0.519,0.8056159019470215
0.518,0.8050817251205444
0.517,0.8045529723167419
0.516,0.8040134310722351
0.515,0.8033463954925537
0.514,0.8026549816131592
0.513,0.8020790815353394
0.512,0.8015340566635132
0.511,0.8010070323944092
0.51,0.800451397895813
0.509,0.7998197078704834
0.508,0.7993049621582031
0.507,0.7987244129180908
0.506,0.7981520295143127
0.505,0.797607421875
0.504,0.7970390319824219
0.503,0.796480119228363
0.502,0.7958235144615173
0.501,0.7952033877372742
0.5,0.7945639491081238
0.499,0.7939942479133606
0.498,0.7933862805366516
0.497,0.7928696274757385
0.496,0.7923110127449036
0.495,0.7917618751525879
0.494,0.791169285774231
0.493,0.7904981374740601
0.492,0.7898994088172913
0.491,0.7893251180648804
0.49,0.7887389659881592
0.489,0.788230299949646
0.488,0.7876998782157898
0.487,0.7871714234352112
0.486,0.7866674065589905
0.485,0.7861251831054688
0.484,0.7854887843132019
0.483,0.7848451733589172
0.482,0.7842085957527161
0.481,0.7837066650390625
0.48,0.783098042011261
0.479,0.7825894951820374
0.478,0.7820398211479187
0.477,0.7815654277801514
0.476,0.7810392379760742
0.475,0.780410885810852
0.474,0.7798945307731628
0.473,0.7793743014335632
0.472,0.7788049578666687
0.471,0.7782212495803833
0.47,0.7777142524719238
0.469,0.7770759463310242
0.468,0.7764747142791748
0.467,0.7758222818374634
0.466,0.7753083109855652
0.465,0.7747974395751953
0.464,0.7743257880210876
0.463,0.7738011479377747
0.462,0.7731691598892212
0.461,0.7724568843841553
0.46,0.7718797922134399
0.459,0.7713025808334351
0.458,0.7707915902137756
0.457,0.7702967524528503
0.456,0.7696964740753174
0.455,0.7691355347633362
0.454,0.7685180306434631
0.453,0.7680140137672424
0.452,0.7675192952156067
0.451,0.7667776942253113
0.45,0.7662506103515625
0.449,0.7656927108764648
0.448,0.765065610408783
0.447,0.7645079493522644
0.446,0.7639384269714355
0.445,0.7633752822875977
0.444,0.7628399133682251
0.443,0.7621510624885559
0.442,0.7615677714347839
0.441,0.7609871625900269
0.44,0.7603616714477539
0.439,0.7597817778587341
0.438,0.7591698169708252
0.437,0.7584701776504517
0.436,0.7578814029693604
0.435,0.757231593132019
0.434,0.7567304372787476
0.433,0.7560344934463501
0.432,0.7553874850273132
0.431,0.7546551823616028
0.43,0.7540551424026489
0.429,0.753559947013855
0.428,0.7528820037841797
0.427,0.7523853182792664
0.426,0.7517542839050293
0.425,0.7511783242225647
0.424,0.7505919933319092
0.423,0.7500880360603333
0.422,0.7495187520980835
0.421,0.7489535212516785
0.42,0.7482714056968689
0.419,0.7476269602775574
0.418,0.7470900416374207
0.417,0.7464340925216675
0.416,0.7458263039588928
0.415,0.7451829314231873
0.414,0.7445639371871948
0.413,0.7439584136009216
0.412,0.7431941032409668
0.411,0.7426120042800903
0.41,0.7418320178985596
0.409,0.7410500645637512
0.408,0.7404740452766418
0.407,0.7398877739906311
0.406,0.7393107414245605
0.405,0.7386374473571777
0.404,0.7379170060157776
0.403,0.7373735904693604
0.402,0.7368086576461792
0.401,0.7361220717430115
0.4,0.7355149388313293
0.399,0.7348690629005432
0.398,0.7343354821205139
0.397,0.733814001083374
0.396,0.7332010865211487
0.395,0.7326757907867432
0.394,0.7319567799568176
0.393,0.7314093112945557
0.392,0.7307674288749695
0.391,0.7301920652389526
0.39,0.7294465899467468
0.389,0.7287818193435669
0.388,0.7281779646873474
0.387,0.727538526058197
0.386,0.7269198298454285
0.385,0.726284384727478
0.384,0.7256928086280823
0.383,0.7250953316688538
0.382,0.7243241667747498
0.381,0.7236175537109375
0.38,0.722975492477417
0.379,0.7224327921867371
0.378,0.7216684818267822
0.377,0.7209630012512207
0.376,0.720302164554596
0.375,0.719516396522522
0.374,0.7188693881034851
0.373,0.7181175351142883
0.372,0.7175112366676331
0.371,0.7168634533882141
0.37,0.7162505984306335
0.369,0.7155883312225342
0.368,0.714897632598877
0.367,0.7141599059104919
0.366,0.7136086821556091
0.365,0.7130201458930969
0.364,0.7124870419502258
0.363,0.7118380069732666
0.362,0.7111403942108154
0.361,0.7103064060211182
0.36,0.7094986438751221
0.359,0.7087323069572449
0.358,0.7079485058784485
0.357,0.7071727514266968
0.356,0.7064831852912903
0.355,0.7057254910469055
0.354,0.704997181892395
0.353,0.7041091322898865
0.352,0.7033753395080566
0.351,0.7027677297592163
0.35,0.7020539045333862
0.349,0.701108992099762
0.348,0.7002388834953308
0.347,0.6992578506469727
0.346,0.6979197263717651
0.345,0.6962162256240845
0.344,0.6946433186531067
0.343,0.6927715539932251
0.342,0.6910662055015564
0.341,0.6893261671066284
0.34,0.6876708269119263
0.339,0.685678243637085
0.338,0.6837948560714722
0.337,0.6814194321632385
0.336,0.679387629032135
0.335,0.6774864792823792
0.334,0.6751524209976196
0.333,0.6732219457626343
0.332,0.6712827682495117
0.331,0.6691543459892273
0.33,0.666486918926239
0.329,0.6644368171691895
0.328,0.6624358892440796
0.327,0.660423755645752
0.326,0.6582353115081787
0.325,0.6555524468421936
0.324,0.6532065868377686
0.323,0.651208758354187
0.322,0.6486964821815491
0.321,0.6461983919143677
0.32,0.6440473794937134
0.319,0.6419345736503601
0.318,0.6397438645362854
0.317,0.6375420093536377
0.316,0.6351723670959473
0.315,0.6325446367263794
0.314,0.6292967200279236
0.313,0.6264986991882324
0.312,0.6237962245941162
0.311,0.6207607388496399
0.31,0.6180967688560486
0.309,0.6160616278648376
0.308,0.614071249961853
0.307,0.6123080253601074
0.306,0.609726071357727
0.305,0.6086925268173218
0.304,0.6079997420310974
0.303,0.6068431735038757
0.302,0.605480968952179
0.301,0.604152262210846
0.3,0.6028140783309937
0.299,0.6014288663864136
0.298,0.5996046662330627
0.297,0.5978929400444031
0.296,0.5966488718986511
0.295,0.5960004329681396
0.294,0.5953904986381531
0.293,0.5945503115653992
0.292,0.5938211679458618
0.291,0.5932542681694031
0.29,0.5926530361175537
0.289,0.5918277502059937
0.288,0.5911698937416077
0.287,0.5903524160385132
0.286,0.5896455645561218
0.285,0.588965892791748
0.284,0.5883357524871826
0.283,0.5876027345657349
0.282,0.5868353843688965
0.281,0.5862071514129639
0.28,0.5855448246002197
0.279,0.584762692451477
0.278,0.5841581225395203
0.277,0.583524227142334
0.276,0.582791268825531
0.275,0.5821045637130737
0.274,0.5815129280090332
0.273,0.5807849168777466
0.272,0.5799700617790222
0.271,0.5791947841644287
0.27,0.5784889459609985
0.269,0.5777319073677063
0.268,0.5769975781440735
0.267,0.5763323903083801
0.266,0.5755073428153992
0.265,0.5749120712280273
0.264,0.5741445422172546
0.263,0.5733550190925598
0.262,0.5725072026252747
0.261,0.5717421770095825
0.26,0.5709163546562195
0.259,0.5702916383743286
0.258,0.5695826411247253
0.257,0.5687806606292725
0.256,0.5680264830589294
0.255,0.5671532154083252
0.254,0.5664019584655762
0.253,0.5656746625900269
0.252,0.564949095249176
0.251,0.5642467141151428
0.25,0.563601016998291
0.249,0.5629205107688904
0.248,0.5620524287223816
0.247,0.561241626739502
0.246,0.5604842901229858
0.245,0.5598537921905518
0.244,0.5591334104537964
0.243,0.5582689046859741
0.242,0.5574504733085632
0.241,0.5567753314971924
0.24,0.5559777617454529
0.239,0.5552257299423218
0.238,0.5544567704200745
0.237,0.5536172986030579
0.236,0.5528450012207031
0.235,0.5522770285606384
0.234,0.5513708591461182
0.233,0.5504541397094727
0.232,0.5496957898139954
0.231,0.5489317178726196
0.23,0.5480349659919739
0.229,0.5473924875259399
0.228,0.5466943979263306
0.227,0.5458922982215881
0.226,0.545206606388092
0.225,0.5443527102470398
0.224,0.5435469150543213
0.223,0.5427653789520264
0.222,0.5419679880142212
0.221,0.541235625743866
0.22,0.540538489818573
0.219,0.5398420095443726
0.218,0.5391877889633179
0.217,0.5384549498558044
0.216,0.5378541946411133
0.215,0.5370384454727173
0.214,0.5362628698348999
0.213,0.5355432629585266
0.212,0.5346992611885071
0.211,0.5323728919029236
0.21,0.5315743088722229
0.209,0.5283093452453613
0.208,0.5274532437324524
0.207,0.5267000198364258
0.206,0.5259957909584045
0.205,0.5250146985054016
0.204,0.5242056846618652
0.203,0.5231711864471436
0.202,0.5221099853515625
0.201,0.5211912989616394
0.2,0.5203512907028198
0.199,0.5192920565605164
0.198,0.5182322263717651
0.197,0.5174584984779358
0.196,0.5163623094558716
0.195,0.5154227018356323
0.194,0.514419674873352
0.193,0.5135026574134827
0.192,0.5126211047172546
0.191,0.5115959644317627
0.19,0.5107628107070923
0.189,0.5099937319755554
0.188,0.5089104771614075
0.187,0.5080356001853943
0.186,0.5070648193359375
0.185,0.5060917735099792
0.184,0.5050144791603088
0.183,0.5042200684547424
0.182,0.503329873085022
0.181,0.5023356080055237
0.18,0.5014610886573792
0.179,0.5005422234535217
0.178,0.4995346963405609
0.177,0.4986289441585541
0.176,0.4976312518119812
0.175,0.4964808523654938
0.174,0.4955320358276367
0.173,0.4944963157176971
0.172,0.4935081303119659
0.171,0.4925319850444793
0.17,0.4913948774337768
0.169,0.4904382526874542
0.168,0.4894824922084808
0.167,0.4885153174400329
0.166,0.4874790608882904
0.165,0.4866428971290588
0.164,0.4855308532714844
0.163,0.4846703112125397
0.162,0.4835704565048218
0.161,0.4825258553028106
0.16,0.4815665483474731
0.159,0.48039710521698
0.158,0.479152649641037
0.157,0.4778082966804504
0.156,0.4766306579113006
0.155,0.4755558967590332
0.154,0.4744581580162048
0.153,0.4734082221984863
0.152,0.4724337756633758
0.151,0.4713548421859741
0.15,0.4702935814857483
0.149,0.469249278306961
0.148,0.4681984186172485
0.147,0.467193603515625
0.146,0.466187059879303
0.145,0.4649397730827331
0.144,0.4638700783252716
0.143,0.4626805782318115
0.142,0.4615622758865356
0.141,0.4605612754821777
0.14,0.4597445726394653
0.139,0.4587045013904571
0.138,0.4579822421073913
0.137,0.4569554030895233
0.136,0.4559722542762756
0.135,0.4550348818302154
0.134,0.4539240896701813
0.133,0.4526872634887695
0.132,0.4516052901744842
0.131,0.4505267441272735
0.13,0.4496097266674042
0.129,0.4485931694507599
0.128,0.4473990201950073
0.127,0.4463711380958557
0.126,0.4450307488441467
0.125,0.4438648819923401
0.124,0.4426600337028503
0.123,0.4416567683219909
0.122,0.4402706027030945
0.121,0.4390320479869842
0.12,0.4378115832805633
0.119,0.4368284046649933
0.118,0.4357131719589233
0.117,0.4347384572029114
0.116,0.4336102306842804
0.115,0.4325035214424133
0.114,0.431209921836853
0.113,0.4299277663230896
0.112,0.428741455078125
0.111,0.4273850321769714
0.11,0.4260600507259369
0.109,0.424763560295105
0.108,0.4235714972019195
0.107,0.4224527180194855
0.106,0.4214721322059631
0.105,0.4203813970088959
0.104,0.4191440045833587
0.103,0.4177476167678833
0.102,0.4165583848953247
0.101,0.4152339994907379
0.1,0.4140801429748535
0.099,0.4128437340259552
0.098,0.411681205034256
0.097,0.4105020761489868
0.096,0.4091891348361969
0.095,0.407721996307373
0.094,0.4064987897872925
0.093,0.4051377177238464
0.092,0.4037542343139648
0.091,0.4023529589176178
0.09,0.4009946584701538
0.089,0.3998652398586273
0.088,0.3983897566795349
0.087,0.3974610269069671
0.086,0.3959658443927765
0.085,0.3942922949790954
0.084,0.3927665650844574
0.083,0.3912537097930908
0.082,0.3894433379173279
0.081,0.3880766034126282
0.08,0.3863488435745239
0.079,0.3848066627979278
0.078,0.3834125101566314
0.077,0.3820904493331909
0.076,0.3803150951862335
0.075,0.3786589205265045
0.074,0.3768672347068786
0.073,0.3753072619438171
0.072,0.3734138011932373
0.071,0.3717053234577179
0.07,0.369688332080841
0.069,0.3676797747611999
0.068,0.3656879365444183
0.067,0.3634332120418548
0.066,0.3611243367195129
0.065,0.3588551580905914
0.064,0.3566454946994781
0.063,0.354030430316925
0.062,0.3513917028903961
0.061,0.3487825989723205
0.06,0.3461207747459411
0.059,0.3431337773799896
0.058,0.3402921259403229
0.057,0.3377613425254822
0.056,0.3346793353557586
0.055,0.3316464424133301
0.054,0.3294158279895782
0.053,0.3264980316162109
0.052,0.3235078155994415
0.051,0.3207087814807892
0.05,0.3177165687084198
0.049,0.3149529993534088
0.048,0.3126184046268463
0.047,0.3095733523368835
0.046,0.3069785535335541
0.045,0.3043548762798309
0.044,0.3015701472759247
0.043,0.2991997301578522
0.042,0.2969039976596832
0.041,0.2944153249263763
0.04,0.2921086251735687
0.039,0.2892205119132995
0.038,0.2869962751865387
0.037,0.284531831741333
0.036,0.2818567156791687
0.035,0.2792093753814697
0.034,0.2762026190757751
0.033,0.2732788026332855
0.032,0.2699902057647705
0.031,0.2673105895519256
0.03,0.2638788819313049
0.029,0.2607062757015228
0.028,0.2578806281089782
0.027,0.2552720904350281
0.026,0.2521741688251495
0.025,0.2493307143449783
0.024,0.245944932103157
0.023,0.2427808940410614
0.022,0.2402146011590957
0.021,0.2373397648334503
0.02,0.2330639213323593
0.019,0.2295775115489959
0.018,0.2262651324272155
0.017,0.2224967926740646
0.016,0.2184387296438217
0.015,0.2146351933479309
0.014,0.2108270525932312
0.013,0.2077525556087494
0.012,0.2043730914592743
0.011,0.2001692503690719
0.01,0.196865975856781
0.009,0.1936569809913635
0.008,0.190575435757637
0.007,0.1873094290494918
0.006,0.1830955743789672
0.005,0.1784524470567703
0.004,0.1720014959573745
0.003,0.1635862439870834
0.002,0.152490884065628
0.001,0.0
"""

#Phone Risk International model
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link/predictors/predict/RiskPhone_XGB_DEV_EXP_FMV_NSR_VCP_VNP_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.name="Phone Risk Model (International) Norm"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.version="6.0"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.identifier="RiskPhone_XGB_DEV_EXP_FMV_NSR_VCP_VNP_20220312"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.params="""{"EXPVL.200001":null,"VCPVL.230310":null,"EXPVL.200006":null,"EXPVL.300006":null,"NSRVL.200020":null,"EXPVL.200011":null,"NSRVL.200010":null,"EXPVL.200003":null,"EXPVL.100006":null,"FMVAL.300043":null,"EXPVL.100004":null,"FMVAL.100143":null,"NSRVL.200028":null,"FMVAL.300051":null,"EXPVL.300010":null,"NSRVL.200017":null,"EXPVL.200002":null,"EXPVL.100008":null,"EXPVL.200019":null,"NSRVL.200004":null,"FMVAL.300046":null,"NSRVL.200029":null,"EXPVL.200049":null,"EXPVL.300001":null,"FMVAL.300042":null,"EXPVL.300007":null,"FMVAL.300041":null,"NSRVL.200030":null,"NSRVL.200006":null,"FMVAL.300045":null,"EXPVL.100005":null,"NSRVL.200014":null,"NSRVL.100005":null,"NSRVL.200031":null,"EXPVL.300004":null,"NSRVL.200032":null,"FMVAL.300044":null,"VCPVL.210412":null,"NSRVL.100004":null,"EXPVL.200017":null,"EXPVL.300002":null,"EXPVL.300005":null,"EXPVL.300011":null,"DEVAL.100027":null,"VNPVL.210612":null,"FMVAL.300049":null,"EXPVL.200018":null,"EXPVL.200004":null,"EXPVL.200009":null,"NSRVL.200012":null,"EXPVL.100001":null,"NSRVL.200018":null,"FMVAL.300047":null,"NSRVL.200003":null,"VCPVL.240099":null,"VCPVL.220306":null,"EXPVL.100003":null,"EXPVL.300009":null,"NSRVL.200001":null,"NSRVL.200023":null,"EXPVL.200016":null,"NSRVL.200008":null,"EXPVL.100002":null,"NSRVL.200011":null,"NSRVL.200021":null,"EXPVL.200005":null,"EXPVL.100000":null,"EXPVL.100007":null,"EXPVL.300003":null}"""
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.low_risk.threshold=0.5593202710151672
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.high_risk.threshold=0.8217212557792664
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.model_type="h2o"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.model_format="mojo"
datasci.model.defaults.v_3_0_0.pii_risk_phone_int.quantile_mapping="""
quantile,score
0.999,0.9774532318115234
0.998,0.9681516289711
0.997,0.9578863382339478
0.996,0.951068639755249
0.995,0.9434687495231628
0.994,0.9346969723701476
0.993,0.926925778388977
0.992,0.9198764562606812
0.991,0.9134157299995422
0.99,0.9092705249786376
0.989,0.9046441912651062
0.988,0.8990271091461182
0.987,0.8953170776367188
0.986,0.8902590274810791
0.985,0.8839913606643677
0.984,0.8795111775398254
0.983,0.8710697293281555
0.982,0.8667480945587158
0.981,0.862739086151123
0.98,0.8569929003715515
0.979,0.8524168729782104
0.978,0.8497506380081177
0.977,0.8471278548240662
0.976,0.8443647623062134
0.975,0.8392114043235779
0.974,0.8349870443344116
0.973,0.83159339427948
0.972,0.8288379907608032
0.971,0.8255077004432678
0.97,0.8217212557792664
0.969,0.8174300193786621
0.968,0.8134108185768127
0.967,0.8099411725997925
0.966,0.8061397671699524
0.965,0.799710214138031
0.964,0.7953954339027405
0.963,0.7913540601730347
0.962,0.7872915267944336
0.961,0.7835375666618347
0.96,0.78220534324646
0.959,0.7778218984603882
0.958,0.7749236822128296
0.957,0.7706349492073059
0.956,0.7663346529006958
0.955,0.7616294622421265
0.954,0.7503878474235535
0.953,0.747366726398468
0.952,0.7428862452507019
0.951,0.7377612590789795
0.95,0.7347100377082825
0.949,0.7303746342658997
0.948,0.72685706615448
0.947,0.7230267524719238
0.946,0.7178871631622314
0.945,0.7130454778671265
0.944,0.7050544619560242
0.942,0.7039984464645386
0.941,0.6989330053329468
0.94,0.6932950019836426
0.939,0.6892582774162292
0.938,0.6856529712677002
0.937,0.6798795461654663
0.936,0.6756125688552856
0.935,0.669307291507721
0.934,0.6636977195739746
0.933,0.6605017185211182
0.932,0.6563557386398315
0.931,0.6531355381011963
0.93,0.649294376373291
0.929,0.6465816497802734
0.928,0.6414967775344849
0.927,0.6375651359558105
0.926,0.6336173415184021
0.925,0.6280736327171326
0.924,0.6245622038841248
0.923,0.6206828951835632
0.922,0.6171076893806458
0.921,0.6147391200065613
0.92,0.6115880012512207
0.919,0.6064403653144836
0.918,0.6018258333206177
0.917,0.5997357368469238
0.916,0.5968369841575623
0.915,0.5938377380371094
0.914,0.5914255380630493
0.913,0.5901111960411072
0.912,0.5885913372039795
0.911,0.5865364670753479
0.91,0.5836673974990845
0.908,0.5792324542999268
0.907,0.5782324075698853
0.906,0.5753065943717957
0.905,0.5728203654289246
0.904,0.5703599452972412
0.903,0.5674341320991516
0.902,0.5648907423019409
0.901,0.5615904331207275
0.9,0.5593202710151672
0.899,0.5578690767288208
0.898,0.5557369589805603
0.897,0.5538415312767029
0.896,0.5527675151824951
0.895,0.5499047636985779
0.894,0.5482054352760315
0.893,0.5468652844429016
0.892,0.5436675548553467
0.891,0.5412880778312683
0.89,0.5387807488441467
0.889,0.5364710688591003
0.888,0.5331338047981262
0.887,0.5307547450065613
0.886,0.5281187295913696
0.885,0.5260850787162781
0.884,0.5233598351478577
0.883,0.5210695266723633
0.882,0.5180882811546326
0.881,0.5164684057235718
0.88,0.5145589709281921
0.879,0.5118411183357239
0.878,0.5105707049369812
0.877,0.5079813003540039
0.876,0.5059323310852051
0.875,0.5045425295829773
0.874,0.5030770897865295
0.873,0.4991182088851928
0.871,0.4976532757282257
0.87,0.4928869605064392
0.869,0.4905441999435425
0.868,0.4878617525100708
0.867,0.4861909747123718
0.866,0.4840224683284759
0.865,0.4821662008762359
0.864,0.4784732162952423
0.863,0.4763014614582062
0.862,0.473836749792099
0.861,0.4716143310070038
0.86,0.4692172706127167
0.859,0.4668341279029846
0.858,0.464720070362091
0.857,0.4622650742530823
0.856,0.4601663649082184
0.855,0.4575801789760589
0.854,0.4556619524955749
0.853,0.4532925188541412
0.852,0.4506546854972839
0.851,0.4485009908676147
0.85,0.4463663399219513
0.849,0.4446501433849334
0.848,0.4425128400325775
0.847,0.4406167566776275
0.846,0.4384604096412658
0.845,0.4340545535087585
0.844,0.4308903515338897
0.843,0.4271508157253265
0.842,0.4249201118946075
0.841,0.4229891300201416
0.84,0.4211892485618591
0.839,0.4192919135093689
0.838,0.4174838364124298
0.837,0.4157674908638
0.836,0.413511723279953
0.835,0.4116066694259643
0.834,0.4093216359615326
0.833,0.4071552753448486
0.832,0.4052704870700836
0.831,0.4032586812973022
0.83,0.4011981189250946
0.829,0.3994519412517547
0.828,0.3968531191349029
0.827,0.3950970470905304
0.826,0.3933049738407135
0.825,0.3913661539554596
0.824,0.3895303010940552
0.823,0.3876897096633911
0.822,0.3860793113708496
0.821,0.384091317653656
0.82,0.3819631040096283
0.819,0.3789766132831573
0.818,0.3784964680671692
0.817,0.3768439888954162
0.816,0.374370127916336
0.815,0.3724481463432312
0.814,0.3707903325557709
0.813,0.3693121671676636
0.812,0.3679763972759247
0.811,0.3664238750934601
0.81,0.3633068203926086
0.809,0.3553015291690826
0.803,0.3551600873470306
0.802,0.3535381555557251
0.801,0.3523314595222473
0.8,0.3507944941520691
0.799,0.3494420051574707
0.798,0.348145991563797
0.797,0.346429705619812
0.796,0.3450971841812134
0.795,0.3436353802680969
0.794,0.3422168791294098
0.793,0.3407768011093139
0.792,0.3396655321121216
0.791,0.3380103409290313
0.79,0.336138367652893
0.789,0.3348161578178406
0.788,0.3334215581417084
0.787,0.3319694697856903
0.786,0.3297726809978485
0.785,0.3287614285945892
0.784,0.3270117342472076
0.783,0.3257515132427215
0.782,0.3245945870876312
0.781,0.323274701833725
0.78,0.3222431540489197
0.779,0.3205127716064453
0.778,0.3200292885303497
0.777,0.3191704154014587
0.776,0.3178974092006683
0.775,0.3128798902034759
0.774,0.3119477033615112
0.773,0.3107214868068695
0.772,0.3092913925647735
0.771,0.308136522769928
0.77,0.3067654371261596
0.769,0.3053730726242065
0.768,0.3043250143527984
0.767,0.3026874363422394
0.766,0.3015232682228088
0.765,0.3004589080810547
0.764,0.2993519306182861
0.763,0.2981896996498108
0.762,0.2969758808612823
0.761,0.2959365844726562
0.76,0.2948010563850403
0.759,0.2937407791614532
0.758,0.2924281656742096
0.757,0.2912680804729461
0.756,0.2903074026107788
0.755,0.2892143428325653
0.754,0.2881601750850677
0.753,0.2873688638210296
0.752,0.2858831286430359
0.751,0.2845712900161743
0.75,0.2836921215057373
0.749,0.2827061116695404
0.748,0.2816830575466156
0.747,0.2806309759616852
0.746,0.2804154455661773
0.745,0.2783366441726684
0.744,0.2774788737297058
0.743,0.2761826515197754
0.742,0.2753986716270447
0.741,0.2746224403381347
0.74,0.2735640406608581
0.739,0.2726403176784515
0.738,0.2718648016452789
0.737,0.2703485488891601
0.736,0.2673808634281158
0.734,0.2672316133975982
0.733,0.2663911879062652
0.732,0.2655724883079529
0.731,0.2645915746688843
0.73,0.2638444602489471
0.729,0.2629996836185455
0.728,0.2621454298496246
0.727,0.2613655030727386
0.726,0.2606264054775238
0.725,0.2599064409732818
0.724,0.258916825056076
0.723,0.2582017481327057
0.722,0.2573599517345428
0.72,0.255612850189209
0.719,0.2547745108604431
0.718,0.2540759444236755
0.717,0.2535146176815033
0.716,0.2528210878372192
0.715,0.2521196603775024
0.714,0.2506472468376159
0.713,0.2503676414489746
0.712,0.2497103065252304
0.711,0.247211143374443
0.709,0.2466790676116943
0.708,0.2456734925508499
0.707,0.2450606375932693
0.706,0.2441833615303039
0.705,0.2429742664098739
0.704,0.2425305545330047
0.703,0.2420735657215118
0.702,0.2416655570268631
0.701,0.2407967299222946
0.7,0.2400082796812057
0.699,0.2392801791429519
0.698,0.2391936182975769
0.692,0.2317246943712234
0.691,0.2310831844806671
0.69,0.2304434478282928
0.689,0.2298004776239395
0.688,0.2291878163814544
0.687,0.2286016792058944
0.686,0.2281713932752609
0.685,0.2275721281766891
0.684,0.226836547255516
0.683,0.2263187319040298
0.682,0.2258056104183197
0.681,0.2251301556825637
0.68,0.2245326936244964
0.679,0.2232150286436081
0.677,0.2229660749435424
0.676,0.2223257571458816
0.675,0.2217221707105636
0.674,0.2212131023406982
0.673,0.220436692237854
0.672,0.2199765741825103
0.671,0.2193505465984344
0.67,0.2187772989273071
0.669,0.2180783748626709
0.668,0.2174378782510757
0.667,0.2169527858495712
0.666,0.2161998152732849
0.665,0.2157252430915832
0.653,0.2094996869564056
0.652,0.2089812308549881
0.651,0.208476185798645
0.65,0.2079348564147949
0.649,0.2074899524450302
0.648,0.2070596367120742
0.647,0.206493929028511
0.646,0.2060781419277191
0.645,0.2055515050888061
0.644,0.2049895823001861
0.643,0.2044768780469894
0.642,0.2041355818510055
0.641,0.2036290764808654
0.64,0.2030558437108993
0.639,0.2030100375413894
0.638,0.2020007520914077
0.637,0.2015425264835357
0.636,0.2010686844587326
0.635,0.2004823237657547
0.634,0.2000351846218109
0.633,0.1993998438119888
0.632,0.1988552510738372
0.631,0.1983861923217773
0.63,0.1979002803564071
0.629,0.1973993331193924
0.628,0.1969814151525497
0.627,0.1964682191610336
0.626,0.1958880722522735
0.625,0.1954101920127868
0.624,0.1949229687452316
0.623,0.1945216953754425
0.622,0.1940698027610778
0.621,0.1936578750610351
0.62,0.1930993497371673
0.619,0.1926750540733337
0.618,0.1922084540128708
0.617,0.1917617619037628
0.616,0.1913271248340606
0.615,0.1908714920282364
0.614,0.1903648823499679
0.613,0.1898811757564544
0.612,0.1893657594919204
0.611,0.1889662593603134
0.61,0.1885926574468612
0.609,0.1881505697965622
0.608,0.1877027750015258
0.607,0.1872947514057159
0.606,0.1868379563093185
0.605,0.1859847903251648
0.604,0.185679629445076
0.603,0.1852496862411499
0.602,0.1847945898771286
0.601,0.1844383329153061
0.6,0.1840521395206451
0.599,0.1835797876119613
0.598,0.1831398904323578
0.597,0.182753250002861
0.596,0.1822902113199234
0.595,0.1819098740816116
0.594,0.1815582066774368
0.593,0.1810790449380874
0.592,0.1806213855743408
0.591,0.1801488846540451
0.59,0.1797068268060684
0.589,0.1792829185724258
0.588,0.1788720935583114
0.587,0.1784449368715286
0.586,0.1780662387609481
0.585,0.177763819694519
0.584,0.1773475855588913
0.583,0.1769523918628692
0.582,0.1765689551830291
0.581,0.1761649250984192
0.58,0.1754068285226822
0.579,0.174946591258049
0.578,0.1744881421327591
0.577,0.1741163283586502
0.576,0.1739716678857803
0.575,0.1734872907400131
0.574,0.1730814129114151
0.573,0.1726385205984115
0.572,0.1722451150417328
0.571,0.1718295812606811
0.57,0.1714710295200348
0.569,0.1710216552019119
0.568,0.1706079989671707
0.567,0.1703155785799026
0.566,0.169920414686203
0.565,0.1694640666246414
0.564,0.168703556060791
0.563,0.1683073341846466
0.562,0.1680056601762771
0.561,0.1676464080810547
0.56,0.1672900021076202
0.559,0.1672512590885162
0.558,0.1662924438714981
0.557,0.1658947169780731
0.556,0.1655462086200714
0.555,0.1651525646448135
0.554,0.1647435575723648
0.553,0.1643885970115661
0.552,0.1640234291553497
0.551,0.1636668890714645
0.55,0.1633123308420181
0.549,0.1630378514528274
0.548,0.1626051217317581
0.547,0.1622113287448883
0.546,0.1618435382843017
0.545,0.1614996343851089
0.544,0.1611268222332
0.543,0.160735473036766
0.542,0.1602428257465362
0.541,0.1599525660276413
0.54,0.1596180498600006
0.539,0.1592173427343368
0.538,0.1588678210973739
0.537,0.1585593372583389
0.536,0.1582125276327133
0.535,0.1577930450439453
0.534,0.1574989110231399
0.533,0.1571462154388427
0.532,0.1567976325750351
0.531,0.1564952433109283
0.53,0.1560431122779846
0.529,0.1556146144866943
0.528,0.1552604436874389
0.527,0.1549117565155029
0.526,0.1542624086141586
0.525,0.1539326608180999
0.524,0.1535883992910385
0.523,0.1532370746135711
0.522,0.1528811603784561
0.521,0.1525090485811233
0.52,0.152141660451889
0.519,0.1517674773931503
0.518,0.1514502614736557
0.517,0.1510796099901199
0.516,0.1499725878238678
0.514,0.1496469676494598
0.513,0.1494750827550888
0.512,0.1491463184356689
0.511,0.1488019526004791
0.51,0.1485175937414169
0.504,0.144648939371109
0.503,0.1444106251001358
0.502,0.1440374255180359
0.501,0.1436771005392074
0.5,0.143341988325119
0.499,0.143024057149887
0.498,0.1426717042922973
0.497,0.1423760801553726
0.496,0.1421082615852356
0.495,0.1418108344078064
0.494,0.1414851546287536
0.493,0.1412381678819656
0.492,0.1409405469894409
0.491,0.140633150935173
0.49,0.1403158903121948
0.489,0.1400534510612487
0.488,0.1397301852703094
0.487,0.1394393742084503
0.486,0.1391476839780807
0.485,0.1388406604528427
0.484,0.1385314762592315
0.483,0.1382012218236923
0.482,0.1378725916147232
0.481,0.1375337243080139
0.48,0.1372334063053131
0.479,0.136701688170433
0.478,0.136461079120636
0.477,0.1361408680677414
0.476,0.1358387768268585
0.475,0.135587602853775
0.474,0.135587602853775
0.473,0.1355581730604171
0.472,0.1352516561746597
0.471,0.1349470019340515
0.47,0.1346132904291153
0.469,0.1343027055263519
0.468,0.1340880393981933
0.467,0.1337696015834808
0.466,0.1334655135869979
0.465,0.1331853121519088
0.464,0.1330976635217666
0.461,0.1312988251447677
0.46,0.1310170888900756
0.459,0.1307390034198761
0.458,0.1304331570863723
0.457,0.1301963031291961
0.456,0.1299512088298797
0.455,0.129724308848381
0.454,0.129496544599533
0.453,0.1292371302843094
0.452,0.1289722621440887
0.451,0.1287001818418502
0.45,0.1285196393728256
0.449,0.1278164386749267
0.448,0.1275159120559692
0.447,0.1272554397583007
0.446,0.127056211233139
0.445,0.1268433630466461
0.444,0.1266670823097229
0.443,0.1263753175735473
0.442,0.1261072009801864
0.441,0.1257712244987487
0.44,0.1255033314228058
0.439,0.1253044158220291
0.438,0.1250935643911361
0.437,0.1248362138867378
0.436,0.1246083453297615
0.435,0.1243620440363884
0.434,0.1241608634591102
0.433,0.1238782629370689
0.432,0.1237468644976615
0.431,0.1234362795948982
0.43,0.1231891885399818
0.429,0.1228830143809318
0.428,0.1228387653827667
0.427,0.122599683701992
0.426,0.1223760247230529
0.425,0.1221675425767898
0.424,0.1219530552625656
0.423,0.1217022091150283
0.422,0.1214134097099304
0.421,0.121192343533039
0.42,0.1210119128227233
0.419,0.1206892281770706
0.418,0.1204854547977447
0.417,0.1202554181218147
0.416,0.1201115772128105
0.415,0.1199195459485054
0.414,0.119725577533245
0.413,0.1195258349180221
0.412,0.119156502187252
0.411,0.1190057694911956
0.41,0.1188015788793563
0.409,0.1188015788793563
0.408,0.1186316311359405
0.407,0.1184166744351387
0.406,0.1181967109441757
0.405,0.1179788559675216
0.404,0.1178046092391014
0.403,0.1176297292113304
0.402,0.1174211949110031
0.401,0.1171599477529525
0.4,0.1169410496950149
0.399,0.1167181208729744
0.398,0.1165201738476753
0.397,0.1162730306386947
0.396,0.1160632148385047
0.395,0.1158122047781944
0.393,0.1156052350997924
0.392,0.115506999194622
0.391,0.1153031960129737
0.39,0.1151010170578956
0.389,0.114887423813343
0.388,0.114642821252346
0.387,0.1144215241074562
0.386,0.1141810566186904
0.385,0.1141698434948921
0.384,0.1134544014930725
0.383,0.1133444979786872
0.382,0.1132367551326751
0.381,0.1127903759479522
0.38,0.1125540360808372
0.379,0.1123249530792236
0.378,0.1120559126138687
0.377,0.1118176355957984
0.376,0.1115869730710983
0.375,0.1115278601646423
0.374,0.1110567301511764
0.373,0.1109417825937271
0.372,0.1105147376656532
0.371,0.1103411689400672
0.37,0.1100178360939025
0.369,0.1097754389047622
0.368,0.1095585748553276
0.367,0.1093984916806221
0.366,0.1092258393764495
0.365,0.1089935451745987
0.364,0.1088834255933761
0.351,0.1040133312344551
0.35,0.1037903130054473
0.349,0.1035816296935081
0.348,0.1033684015274047
0.347,0.1031613498926162
0.346,0.1029608845710754
0.345,0.1027802154421806
0.344,0.1026202216744422
0.343,0.1024505123496055
0.342,0.102250725030899
0.341,0.1020478457212448
0.34,0.101840503513813
0.339,0.1016315221786499
0.338,0.1014188826084137
0.337,0.1012285575270652
0.336,0.1010735854506492
0.335,0.1008687168359756
0.334,0.1006687730550766
0.333,0.1004553660750389
0.332,0.1002411991357803
0.331,0.1000887975096702
0.33,0.0999481454491615
0.329,0.0997635424137115
0.328,0.0995766818523407
0.327,0.0991525575518608
0.326,0.0990176647901535
0.325,0.098818838596344
0.324,0.0982330739498138
0.323,0.0981998816132545
0.322,0.0980905219912529
0.321,0.0979130491614341
0.32,0.0976753830909729
0.319,0.0974688082933425
0.318,0.0972940325736999
0.317,0.0971207320690155
0.316,0.0969177857041359
0.315,0.0967351794242858
0.314,0.0965548828244209
0.313,0.096367597579956
0.312,0.0961957648396492
0.311,0.0960652530193328
0.31,0.0958864614367485
0.309,0.0957013890147209
0.308,0.0955031812191009
0.307,0.0953231379389762
0.306,0.0951921492815017
0.305,0.0949357077479362
0.304,0.0947347953915596
0.303,0.0945626348257064
0.302,0.0943780094385147
0.301,0.0941869392991066
0.3,0.0940139964222908
0.299,0.0938445627689361
0.298,0.0936695858836174
0.297,0.0934648290276527
0.296,0.093287207186222
0.295,0.0931247547268867
0.294,0.0929470360279083
0.293,0.0927713438868522
0.292,0.0925692692399025
0.291,0.0924112945795059
0.29,0.0922584533691406
0.289,0.0921231359243393
0.288,0.0919510051608085
0.287,0.0917773023247718
0.286,0.0916450396180152
0.285,0.0914931818842887
0.284,0.0913060307502746
0.283,0.0911029800772667
0.282,0.0909370183944702
0.281,0.0907711759209632
0.28,0.0906257256865501
0.279,0.0904499813914299
0.278,0.0903212577104568
0.277,0.0901256203651428
0.276,0.0899606496095657
0.275,0.0897993594408035
0.274,0.0896205231547355
0.273,0.0894665047526359
0.272,0.0892785042524337
0.271,0.0891744196414947
0.27,0.089010365307331
0.269,0.0888406634330749
0.268,0.0886821746826171
0.267,0.0885249972343444
0.266,0.0884014144539833
0.265,0.0882526189088821
0.264,0.0880825519561767
0.263,0.0879736095666885
0.262,0.0877731218934059
0.261,0.0876079127192497
0.26,0.0874444022774696
0.259,0.0872810930013656
0.258,0.0871295109391212
0.257,0.0869688540697097
0.256,0.0868072733283042
0.255,0.0866764858365058
0.254,0.0865239202976226
0.253,0.0863430351018905
0.252,0.0861829370260238
0.251,0.0860098600387573
0.25,0.0858455225825309
0.249,0.0856948569416999
0.248,0.0855433493852615
0.247,0.0854249596595764
0.246,0.0852386280894279
0.245,0.0850726217031478
0.244,0.0849486216902732
0.243,0.0847968012094497
0.242,0.0846377685666084
0.241,0.0844948589801788
0.24,0.084325686097145
0.239,0.0841756984591484
0.238,0.084034152328968
0.237,0.0838756188750267
0.236,0.0837158188223838
0.235,0.0835547149181366
0.234,0.0833963602781295
0.233,0.0832647085189819
0.232,0.0831923633813858
0.231,0.0830337703227996
0.23,0.082880288362503
0.229,0.0827369466423988
0.228,0.0825959891080856
0.227,0.0824527665972709
0.226,0.0823221430182457
0.225,0.0821735486388206
0.224,0.0820106044411659
0.223,0.0818672999739646
0.222,0.0817278772592544
0.221,0.0815853253006935
0.22,0.0815853253006935
0.219,0.0814146548509597
0.218,0.0812602490186691
0.217,0.0811107531189918
0.216,0.0810217335820198
0.215,0.0808790177106857
0.214,0.0807253271341323
0.213,0.0805664360523223
0.212,0.0804255902767181
0.211,0.08028444647789
0.21,0.0801470130681991
0.209,0.0799792259931564
0.208,0.0798274874687194
0.207,0.0796753242611885
0.206,0.0795367956161499
0.205,0.0793769881129264
0.204,0.0792213678359985
0.203,0.0790537148714065
0.202,0.0789051949977874
0.201,0.078738085925579
0.2,0.078586496412754
0.199,0.078439824283123
0.198,0.0782699212431907
0.197,0.0781335830688476
0.196,0.0779938548803329
0.195,0.0778478905558586
0.194,0.0776902809739112
0.193,0.0775370448827743
0.192,0.0773261711001396
0.191,0.0772160217165947
0.19,0.0770611613988876
0.189,0.0769262909889221
0.188,0.0767674222588539
0.187,0.0766640082001686
0.186,0.0765359550714492
0.185,0.0763716176152229
0.184,0.0762171223759651
0.183,0.0760701522231102
0.182,0.0759823098778724
0.181,0.0756394490599632
0.18,0.0755006745457649
0.179,0.0753379166126251
0.178,0.0752213150262832
0.177,0.0750640556216239
0.176,0.0749038532376289
0.175,0.0747557431459426
0.174,0.0746008977293968
0.173,0.0744378790259361
0.172,0.0742928385734558
0.171,0.0741288512945175
0.17,0.0739912390708923
0.169,0.0738600119948387
0.168,0.0737157091498375
0.167,0.0735615417361259
0.166,0.0734165087342262
0.165,0.0732685029506683
0.164,0.0731126144528389
0.163,0.072936326265335
0.162,0.0728075802326202
0.161,0.0726406276226043
0.16,0.072490356862545
0.159,0.0723394006490707
0.158,0.0721549913287162
0.157,0.0720118135213851
0.156,0.0718419328331947
0.155,0.0716699808835983
0.154,0.0715475082397461
0.153,0.071410596370697
0.152,0.0712712779641151
0.151,0.0711236968636512
0.15,0.0709790140390396
0.149,0.0708074197173118
0.148,0.0706703290343284
0.147,0.0705064386129379
0.146,0.0703400894999504
0.145,0.0701808705925941
0.144,0.0700125470757484
0.143,0.0698737278580665
0.142,0.0697236955165863
0.141,0.0695630833506584
0.14,0.0693915411829948
0.139,0.0692352801561355
0.138,0.0690707564353942
0.137,0.0689105466008186
0.136,0.0687581747770309
0.135,0.0685920342803001
0.134,0.0684437677264213
0.133,0.0682754293084144
0.132,0.0681511685252189
0.131,0.0679633244872093
0.13,0.0678099989891052
0.129,0.0676370412111282
0.128,0.0674785152077674
0.127,0.0673359110951423
0.126,0.067185565829277
0.125,0.0671116933226585
0.124,0.0668782517313957
0.123,0.0667192265391349
0.122,0.0665388703346252
0.121,0.0663680955767631
0.12,0.0661883428692817
0.119,0.0660198703408241
0.118,0.0658587515354156
0.117,0.0656952336430549
0.116,0.0655300319194793
0.115,0.0653766840696334
0.114,0.0652191042900085
0.113,0.0650647059082984
0.112,0.0648983865976333
0.111,0.0647326335310936
0.11,0.0645663887262344
0.109,0.0644105523824691
0.108,0.0642505288124084
0.107,0.0640854686498642
0.106,0.0639261752367019
0.105,0.0637595951557159
0.104,0.0635784864425659
0.103,0.0634005516767501
0.102,0.0632267072796821
0.101,0.0630668476223945
0.1,0.0629026070237159
0.099,0.0627410188317298
0.098,0.0625743567943573
0.097,0.062410831451416
0.096,0.0622370839118957
0.095,0.0620696358382701
0.094,0.061900358647108
0.093,0.0617268942296505
0.092,0.0615483298897743
0.091,0.0613842010498046
0.09,0.0612100847065448
0.089,0.0610478594899177
0.088,0.0608712546527385
0.087,0.060695756226778
0.086,0.0605240911245346
0.085,0.060343112796545
0.084,0.06016755849123
0.083,0.059127427637577
0.082,0.0589522682130336
0.081,0.0587862394750118
0.08,0.0586063750088214
0.079,0.0584419555962085
0.078,0.0582759082317352
0.077,0.0580900721251964
0.076,0.0578986667096614
0.075,0.0577254518866539
0.074,0.0575399473309516
0.073,0.0573688745498657
0.072,0.0571900345385074
0.071,0.057000458240509
0.07,0.0568033754825592
0.069,0.0566062927246093
0.068,0.0564051419496536
0.067,0.0562135763466358
0.066,0.0560209900140762
0.065,0.0557990707457065
0.064,0.0556253008544445
0.063,0.0554365254938602
0.062,0.0552460961043834
0.061,0.0550690479576587
0.06,0.0548819974064827
0.059,0.0546577759087085
0.058,0.0544571615755558
0.057,0.0542422421276569
0.056,0.0540806539356708
0.055,0.0538647808134555
0.054,0.0536648519337177
0.053,0.0534759536385536
0.052,0.0532443858683109
0.051,0.0530261322855949
0.05,0.0527902916073799
0.049,0.0525533519685268
0.048,0.0523383505642414
0.047,0.0521257631480693
0.046,0.0519142933189868
0.045,0.0516804605722427
0.044,0.0514487437903881
0.043,0.0512210875749588
0.042,0.0509844422340393
0.041,0.0507653281092643
0.04,0.0505310073494911
0.039,0.0502878949046134
0.038,0.0500400848686695
0.037,0.0498149171471595
0.036,0.0495643205940723
0.035,0.0492859780788421
0.034,0.0490338541567325
0.033,0.048737209290266
0.032,0.0484478734433651
0.031,0.0481591932475566
0.03,0.0478462576866149
0.029,0.0475618578493595
0.028,0.0472560003399848
0.027,0.0469267182052135
0.026,0.0465728268027305
0.025,0.0462581552565097
0.024,0.0458954572677612
0.023,0.0455117300152778
0.022,0.0451432354748249
0.021,0.044813547283411
0.02,0.0444122031331062
0.019,0.0440280735492706
0.018,0.0435424260795116
0.017,0.0431106686592102
0.016,0.0426473058760166
0.015,0.0421706996858119
0.014,0.0416129939258098
0.013,0.0410321876406669
0.012,0.0404344983398914
0.011,0.0397999249398708
0.01,0.0391439869999885
0.009,0.0384983196854591
0.008,0.0377335175871849
0.007,0.0367887318134307
0.006,0.0359023213386535
0.005,0.0347769856452941
0.004,0.0334241017699241
0.003,0.0320321097970008
0.002,0.0298493802547454
0.001,0.0
"""



datasci.model.defaults.dynamic.control.center {
    s3 {
        bucketName = "globalconfig-************-us-east-1"
      }
    memcached {
        host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#================= Model Config V3 #=================


#================= Mail Config =================#
socure.support.email="<EMAIL>"
socure.sales.email="<EMAIL>"
mailgun.apikey="""ENC(TPxc50u3XqYgh2XBSA8xhYvZ0DAXjLAtgAzqpl7F41Zad5pjKX1okJSL55wolVMieUSXmvqFMqRxpVPpNaq/qKJqRZs=)"""
mailgun.domain="socure.com"
mailgun.from="Socure Support <<EMAIL>>"
#================= Mail Config =================#

domain.skip.secret="""ENC(Vn5YeXEUnEijKbc/FxIjklP12T+1+wI2vhobRtFk8CyHD3Tp5KZq0Vl6kIx3lnpUZLAFsrsgGSJQF0e3ZTAz5Nh4bdA=)"""

pipl.fields.match_score=1.0
pipl.fields.partial_match_score=0.66
fullcontact.fields.match_score=0.925
fullcontactv3.fields.match_score=0.925
#============= Rate Limit Service Configuration =============
rate.limit.service.url="https://socure-rate-limiting-dev2.us-east-1.elasticbeanstalk.com"
rate.limit.service.disabled="false"
rate.limit.memcache.timeout.ms=100

# Reason Code Conflict Resolver - Remove right side reason code(s) if left side reason code(s) found.
rc.conflict.resolver.remove.R940="R901, R920"
rc.conflict.resolver.remove.R186="R926, R960"
rc.conflict.resolver.remove.I196="R926, R960"
rc.conflict.resolver.remove.R553="I551, I552, I553"
rc.conflict.resolver.remove.R603.R604.R605="I614, I615"
rc.conflict.resolver.remove.R703="R701, R702, R704, I701, I702, I703 , I704 ,I705, I706, I707, I708"
rc.conflict.resolver.remove.R705="I328"
rc.conflict.resolver.remove.I256="I329"
rc.conflict.resolver.remove.I157="I331"
rc.conflict.resolver.remove.I603="I601"

# Reason Code Conflict Resolver - Add right side reason code(s) if left side reason code(s) found.
rc.conflict.resolver.add.I256="I325"
rc.conflict.resolver.add.I157="I326"

#============= R703 based correlation score name-address configuration ================
co.name.address.r703.score=0.0
#============= R703 based correlation score name-address configuration ================
#==audit failure bucket
s3.transaction_error_bucket="dev-audit-errors-************-us-east-1"
s3.transaction_error_bucket_kms="arn:aws:kms:us-east-1:************:key/mrk-f7533e358fbe4b778086164272468e47"

#============= Datadog config =============#
datadog.series_url="https://app.datadoghq.com/api/v1/series"
datadog.api_key="""ENC(txplN99kcSqL2soYfl3V4MzJFfOLPQxHlR1wR1muuQg4sMMYBZ5ky5yPFS4KKB2Rzmbb1zqtEnLbCjrp1zq3yA==)"""
#============= Datadog config =============#

#============= Mailgun config =============#
mailgun.endpoint="https://api.mailgun.net/v2/socure.com/messages"
mailgun.key="""ENC(TPxc50u3XqYgh2XBSA8xhYvZ0DAXjLAtgAzqpl7F41Zad5pjKX1okJSL55wolVMieUSXmvqFMqRxpVPpNaq/qKJqRZs=)"""
mailgun.domain_name="socure.com"
#============= Datadog config =============#

#============= Model monitoring error reporting =============#
error.reporter.model.monitoring.subject="Model Monitoring Metrics Reporting Failure - Stage"
error.reporter.model.monitoring.from="<EMAIL>"
error.reporter.model.monitoring.to="<EMAIL>"
error.reporter.model.monitoring.cc=""
error.reporter.model.monitoring.bcc=""
#============= Model monitoring error reporting =============#

#============= Document Verification =============#
document.verification {
  docvOrchestra {
    endpoint = "https://dv-orchestra-stage.us-east-1.elasticbeanstalk.com/"
    endpoint2 = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link"
    hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/dev/hmac"
    }
      metrics.enabled = false
    dynamic.control.center {
          s3 {
            bucketName = "globalconfig-************-us-east-1"
          }
          memcached {
            host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
            port=11211
            ttl=86400
          }
          local {
            cache.timeout.minutes=2
          }
    }
  }
  idrnd {
    endpoint = "https://idrnd-wrapper.webapps.us-east-1.product-dev.socure.link/"
    endpoint2 = "https://idrnd-wrapper.webapps.us-east-1.product-dev.socure.link/"
    hmac {
      realm="Socure"
      version = "1.0"
      strength=512
      aws.secrets.manager.id="idrnd-wrapper/dev/hmac"
    }
  }
  txn.case.workflow.service {
     endpoint = "http://transaction-case-workflow-service"
     endpoint2 = "http://transaction-case-workflow-service"
     hmac {
       realm = "Socure"
       version = "1.0"
       ttl = 5
       time.interval = 5
       strength = 512
       aws.secrets.manager.id = "txn-case-workflow-service/dev/hmac-354cabcc"
       secret.refresh.interval = 5000
     }
  }
  customer.customization {
    endpoint="http://customer-asset-storage"
    endpoint2="http://customer-asset-storage"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "customer-asset-storage-service/dev/hmac-5830e86d"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    metrics.enabled = false
  }
  memcached {
    endpoint="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port=11211
    ttl=86400
  }
  dynamic.control.center {
    s3 {
      bucketName = "globalconfig-************-us-east-1"
    }
    memcached {
      host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
      port=11211
      ttl=86400
    }
    local {
      cache.timeout.minutes=2
    }
  }
}
#============= Document Verification =============#

#============= Image auditing bucket configuration =============#
idplus.audit.image.bucket="idplus-audit-************-us-east-1"
idplus.audit.image.encryption.mode="aws:kms"
idplus.audit.image.kms.id="arn:aws:kms:us-east-1:************:alias/socure/idplus-service-app"
#============= Image auditing bucket configuration =============#


#============= Infutor =============#
infutor.username=socurexml3
infutor.password="""ENC(/KwdMMGQX5lU4KlgwFs9FuGpbUsiQNyGR3W6hbd1KH351UCq+xn+84N2j4usDoA=)"""
infutor.phoneowneridvalidation.endpoint="https://xml.yourdatadelivery.com/PhoneOwnerIDValidation"
infutor.reverseemailappend.endpoint="https://xml.yourdatadelivery.com/ReverseEmailAppend"
infutor.inrichmentaddress.endpoint="https://xml.yourdatadelivery.com/InrichmentTrueIdentity"
infutor.idverify.endpoint="https://xml.yourdatadelivery.com/IDVerify"
infutor.cookierejectcode=102
infutor.authenticate.endpoint="https://xml.yourdatadelivery.com/Authenticate"
infutor.session.refresh.interval="15 minutes"
infutor.session.refresh.timeout="5 seconds"
infutor.dns.domain="xml.yourdatadelivery.com"
infutor.dns.resolver.delay=5000
infutor.dns.resolver.period=5000

#============= Hygenics =============#
hygenics.endpoint="https://ivs.hygenicsdata.com/service/api.json"
hygenics.apikey="""ENC(K4LY2VXx81L1Grmh2GKAy0kCsnSXqfdJFCwyXwlDRpNc5ka5a0iSPC2mPXl3e5JDVR+6l7KYYNy8mlCSegi1LA==)"""
hygenics.apiversion=1

#============= Twilio =============#
twilio.userKey=AC6d3949d0bb1547f810e2052834499ed7
twilio.authToken="""ENC(WDTG6zVB6C4okggqbd77nsEVE6EB6V7ECN5iQBy7MruT7ZFwttDXOOgAlt3znGnYeTRS1bLSPcQFQ0UATrx/KQ==)"""
twilio.phonevalidation.endpoint="https://lookups.twilio.com/v1/PhoneNumbers"
twilio.addOnsCommaSeparatedList="telo_opencnam,icehook_scout"

#============= InfoGroup =============#
info.group.endpoint="https://api.infoconnect.com/v3/match"
info.group.apiKey="""ENC(dDcAOvi9TCDmVNj8BFMqsz4fDZQ8ExThiRJkUA2varKaFKUUex8cKGbywOOUmUSVYRazLv4I1xJ7zFZVoxYZRw==)"""
info.group.limit=20
info.group.confidence=90

#============= TowerData =============#
towerdata.endpoint="https://api.towerdata.com/v5/eppend"
towerdata.apiKey="""ENC(hLGMSAeq8KrByJa5y1zGY54kugqNEmp5iVFqEPlzXHSpgInZf/5d+pxkMkfoj1FZoB2hAXiuTNqMllQa+/dFvw==)"""

#============= TowerData Alternate =============#
towerdata.endpoint_alternate="https://api.towerdata.com/v5/eppend"
towerdata.apiKey_alternate="""ENC(Z9VyPywV0Wq4eucvj/Qmmo9izoy5yj/h9pL4MZbGOtzyYm4uJZunTTT19AIW+3alfB6No64GB3QzDXazm09cxg==)"""
towerdata.alternate.accounts=[370,3954,7425,1447,2036,2086,2558]

#============= Telesign =============#
telesign.userKey=FFA090A3-7A11-425E-8762-5CFBBDFFB849
telesign.authToken="""ENC(D/Fh8Ni5a/4pacz+H3qVUwz/K5Ulu+AByoYUa78qKVX5bAMCPFxdh6R0w6kYa1LwCmcKxzpY6Ur4XTnG71m4pA==)"""
telesign.phonevalidation.endpoint="https://rest-ww.telesign.com/v1/phoneid"
telesign.addOnsCommaSeparatedList="contact"

telesign.scoreapi.endpoint="https://rest-ww.telesign.com/v1/score"

#================ Payfone Configuration ================#
payfone {
  username = """ENC(gNc6hyTjValj0vuERUj+ENpcy6w0C/H+mAJYIIZR/S5JEhl41bgRnreJo+EKwtzZRugRt+1VppSrXiwi9uaiyu9h/6E=)"""
  password = """ENC(KQlzeP2p1iRNSW/MdhZnLqUzIw5rLdjAeZhEBQMzINf8DwKSV22hHHMKw4WktQy4WdRx2lG02yC2BR/71QOxYjSPXbk=)"""
  clientId = "socure"
  apiClientId = """ENC(+Uy6diqfu0QqPLNuJu2UzkmJLjT3IiXs8kJGh9T5kodxUbyTwyrjitJPvCk/cJfeR6bbBA==)"""
  payfoneVersion = "v1"
  fetchDetail = true
  fetchTrustScore = true

  authentication.endpoint = "https://oapi.payfone.com/token"
  verify.endpoint = "https://oapi.payfone.com/identity"
  verify.alternate.endpoint = "http://************:8080/payfone/identity"
  getIntelligence.endpoint = "https://api.payfone.com/intelligence/2015/07/15/getIntelligence"
  trustv2.endpoint = "https://oapi.payfone.com/trust/v2"

  pfivl.use.rulecode = false
  pfvvl.use.rulecode = false
}
#================ Payfone Configuration ================#

#========Heavy Load Clients=======#

audit.heavyload.accountids="128"

#========Chime Model Accounts=======#

cip.decision.model_mapping {
  CHIME_MODEL = [246, 683, 686]
  BLUE_VINE_MODEL = [133, 738, 883, 490]
  VARO_MODEL = [882, 1484, 1481]
  PUBLIC_MODEL = [370]
  DAVE_MODEL = [3408]
  OXYGEN_MODEL = [3544]
  GENESIS_BLOCK_MODEL = [3587]
  Q2_HOSTED_LOGIC_MODEL = [3666]
  MEED_BANK_MODEL = [3680]
  BILLGO_MODEL = [1710]
  INVSTR_BANK_MODEL = [2298]
  CREDIT_KARMA_MODEL = [1482]
  GBI_MODEL = [1483]
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-dev-************-us-east-1"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-dev-************-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ Transaction Billing ======================#

transaction-billing {

  maxRetries = 10

  aws {
    sqs {
      region=us-east-1
      queueName="batch-job-subscription-biller-dev.fifo"
      maxRetries=5
      backoff {
        min=10
        max=10
      }
    }
  }
}

#================ File storage download config ================#
file.storage.download {
  endpoint = "https://file-storage-download-stage.us-east-1.elasticbeanstalk.com"
  endpoint2 = "https://file-download.webapps.us-east-1.product-dev.socure.link"

  hmac {
    secret.key = """ENC(71qcBldu1kRXjGoEZbXP4p4Lqj7D/olKKyr/VM+NZUHVb8e+yJfIxyq4tTHDtb8eQ13yHcPdsLVYr4lI9w+72g==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
  dynamic.control.center {
      s3 {
        bucketName = "globalconfig-************-us-east-1"
      }
      memcached {
        host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
        port=11211
        ttl=86400
      }
      local {
        cache.timeout.minutes=2
      }
    }
}
#================ File storage download config ================#

#================ AlertList config ================#
alertlist {

  consortiumDatasetId = "dst-muRhSjLUZN"
  bancorpDatasetId = "dst-O1Mjc0GhHo"
  bancorpAccountIds = "133,490,686,882,883,1481"

  endpoint = "https://alertlist-matcher.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://alertlist-matcher.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.key = """ENC(CHmT4UIibDBg9DSlDduQkn8jvG0B3WqRaJ1f42NQpS7V5IFVZ7/6ljzsQJ7bJk7P9J0V2R0DC7kggnMqheAwbTwQjgp7kylq1oX8Xo4cTTjRZDFGy3iGNKbr2Kh+L7Mj)"""
  }
}
#================ AlertList config ================#

#================ Healthcheck config ================#
healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<95.0" //should be less than 90%
    non_heap.used_max_percentage = "<90.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}
#================ Healthcheck config ================#

#================ Address Service config ================#
address-service {
  #main branch deployed environment url
  endpoint = "https://address.webapps.us-east-1.product-dev.socure.link"
  #mock endpoint for vendor api call
  alternateEndpoint = "https://address.webapps.us-east-1.product-dev.socure.link"
  #whitesource branch deployed parallel environment url
  parallelEndpoint = "https://address.webapps.us-east-1.product-dev.socure.link"
  #default fallback url when dynamic control center cannot produce evaluated endpoint
  defaultEndpoint = "https://address.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="address-service/dev/hmac-63dc98"
    secret.refresh.interval=5000
  }
}

#===================Device 2.0 configs <START>==========================

device.read.service {
    host = "https://device-v2-read.us-east-1.device-dev.socure.link"
    host2 = "http://device-v2-read-service-private"
    hmac {
        ttl=5
        time.interval=5
        realm="Socure"
        version="1.0"
        strength=512
        aws.secrets.manager.id="arn:aws:secretsmanager:us-east-1:************:secret:behavioral-d5519d-device-v2-read-service-hmac-secret-2oYv5M"
        secret.refresh.interval=5000
    }

    hmac2 {
      ttl=5
      time.interval=5
      realm="Socure"
      version = "1.0"
      strength=512
      aws.secrets.manager.id="behavioral-d5519d-device-v2-read-service-hmac-secret"
      secret.refresh.interval=5000
    }

    dynamic.control.center{
      s3 {
        bucketName="globalconfig-************-us-east-1"
      }
      memcached {
        host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
        port=11211
        ttl=86400
      }
      local.cache {
        timeout.minutes=2
      }
    }
 }

#===================Device 2.0 configs <END>==========================

socuredb.hibernate.c3p0.connectionCustomizerClassName="me.socure.c3p0.ReadOnlyConnectionCustomizer"


module.decisioning.adp.account.ids=[133,342]
module.decisioning.adp.test.account.ids=[686,2036,1506]

#================ Device Fingerprint config ================#
device.fingerprint {
  //use internal endpoint here
  endpoint = "http://device-risk-rulecode-service"
  endpoint2 = "http://device-risk-rulecode-service"
  hmac {
    realm="Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="device-risk-rulecode/dev/hmac"
    secret.refresh.interval=5000
  }

  hmac2 {
    realm="Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="device-risk-rulecode/dev/hmac"
    secret.refresh.interval=5000
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#================ Device Fingerprint config ================#

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
  hmac {
    secret.key="""ENC(tDQyn4rBrAqmMSAO9GTDWGk6nRJnZTceGIyOEqvcorqnQ7lG0AIPxoHQ/9u4pMHAReCPtzz+2Psc577Su4Smuw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#================ Reasoncode Service config ================#
#================ ecbsv service config ================#
ecbsv.security {
  endpoint = "https://ecbsv-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://ecbsv-service.webapps.us-east-1.product-dev.socure.link"
  groupName = "IdplusServicesRamp"
  flagName = "EcbsvService_Ramp"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "ecbsv-service/dev/hmac-7276df66"
    secret.refresh.interval = 5000
  }
}
#================ ecbsv service config ================#
#================ watchlist private service config ================#
globalwatchlist.service {
  endpoint="https://watchlist-private-stage-1.us-east-1.elasticbeanstalk.com/api/"
  endpoint2="https://watchlist-private-service.webapps.us-east-1.product-dev.socure.link/api/"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="watchlist-private/dev/hmac-feec12"
    secret.refresh.interval=5000
  }
}
#================ watchlist private service config ================#
#=======================custom watchlist========================#
custom.watchlist.manager {
  endpoint = "https://custom-wl-manager-stage.us-east-1.elasticbeanstalk.com"
  endpoint2 = "https://custom-wl-manager.webapps.us-east-1.product-dev.socure.link"
  groupName = "IdplusServicesRamp"
  flagName = "CustomWLManager_Ramp"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="custom-wl-manager/dev/hmac-596233"
    secret.refresh.interval=5000
  }
}

#================ mla service config ================#
mla.security {
  endpoint="https://mla-service-dev.us-east-1.elasticbeanstalk.com"
  endpoint2="https://mla-service-dev-parallel.us-east-1.elasticbeanstalk.com"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="mla-service/dev/hmac"
    secret.refresh.interval=5000
  }
}
#================ mla service config ================#
#================ KYC Service config ================#
kyc.service {
  //internal communication should use the internal endpoint
  endpoint = "https://kyc-search-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://kyc-search-service.webapps.us-east-1.product-dev.socure.link"
  alternateEndpoint = "https://mock-service.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="KycSearchService_Ramp"
  hmac {
    secret.key="""ENC(yFg9jaVtVxRq4ZJ4Brnqcf18F2alPXxAhN6YbtqV0ZxYuQLnFcUgHEWSQ40c2RyPFRFjpjkSYb0a)"""
    strength=512
    ttl=5
    interval=5
    realm="Socure"
    version = "1.0"
  }
}
#================ KYC Service config ================#

#================ Smarty Streets ================#

smartystreets.service {
  endpoint = "https://smartystreets.webapps.us-east-1.product-dev.socure.link"
}

#================ Smarty Streets ================#

#================ Transaction Auditing ==========#

transaction.auditing {
  endpoint = "https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    secret.key="""ENC(ngnqI9BQGfah6319284scKFZ+zdKuZFEymhamGdZKHEYBPFmsl8pZGIp9Bso5QzG97MdilDmkco=)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}

#================ Transaction Auditing ==========#

#================ Digital Envoy =================#
digital-envoy {
  endpoint = "http://************:8080/webservice/query"
  token = "digital-envoy-api/dev/token-fb621f"
}

#================ Digital Envoy =================#

#================ Rulecode Service config ================#
rulecode.service {
  endpoint = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
    secret.refresh.interval=5000
  }
}
#================ Rulecode Service config ================#

#=============== Event auiding service ===================#
event.auditing{
  security{
    endpoint = "https://socure-event-auditing-stage.us-east-1.elasticbeanstalk.com/api"
    endpoint2 = "https://event-auditing-service.webapps.us-east-1.product-dev.socure.link/api"
    hmac {
      realm="Socure"
      version=1.0
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="event-auditing/dev/hmac-3ed08d"
      secret.refresh.interval=5000
    }
  }
}
#=============== Event auiding service ===================#

#=================StepUp Service=====================#
stepUp.service {
  endpoint="https://step-up-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "step-up-service/dev/hmac-7c48d6"
    secret.refresh.interval = 5000
    realm = "Socure"
    version = "1.0"
  }
}
#=================StepUp Service=====================#

h2omlpredictor.service.url = "https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link"
h2omlpredictor.service.endpoint2 = "https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link"
h2omlpredictor.service.throttling.enabled=false

modelmanagement {
  endpoint="https://model-management.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://model-management.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval = 5000
    aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
  }
  enableModelManagement = "true"
  metrics.enabled = false
}

#============== Transaction Auditing SNS =================#
transaction.auditing.client.sns {
  sns.topic = "idplus-audit-dev"
  sns.regions = [
    "us-east-1",
    "us-west-2",
    "us-east-2"
  ]
  s3.bucket.fallback {
    name = "dev-audit-errors-************-us-east-1"
    region = "us-east-1"
    kms {
      id = "arn:aws:kms:us-east-1:************:key/mrk-f7533e358fbe4b778086164272468e47"
    }
  }
  s3.bucket.large.files {
    name = "sqs-storage-dev-************-us-east-1"
    region = "us-east-1"
  }
  retry {
    initial.backoff = "2 seconds"
    max.backoff = "32 seconds"
    multiplier = 2
    max.attempts = 10
  }
  cache {
    duration.hours = 1
  }
}
#============== Transaction Auditing SNS =================#

enc.key="""ENC(8eiMhMi3+YmM3h69iaVpVRrgcr1lMYr1N/7YTBCJhjpF+BTpXw==)"""

#============= Feedback Service ==============
feedback.service.endpoint="https://feedback-service.webapps.us-east-1.product-dev.socure.link"
feedback.service.endpoint2="https://feedback-service.webapps.us-east-1.product-dev.socure.link"
feedback.service.disabled="true"
feedback.service.timeout.ms=2000
#=============== Feedback Service ============

#=============== Velocity Service ============
velocity {
  endpoint = "http://velocity-service"
  endpoint2 = "http://velocity-service"
  hmac {
    ttl=5
    time.interval=5
    realm="Socure"
    version = "1.0"
    strength=512
    aws.secrets.manager.id="velocity-service/dev/hmac"
    secret.refresh.interval=5000
  }

  hmac2 {
    ttl=5
    time.interval=5
    realm="Socure"
    version = "1.0"
    strength=512
    aws.secrets.manager.id="velocity-service/dev/hmac"
    secret.refresh.interval=5000
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="localhost"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#=============== Velocity Service ============

rate.limiter {
  #=============== Token Bucket Rate Limiting Config Start ============
  token.bucket {
    redis.uri = "redis://dev-api-rate-limit.fxpzap.ng.0001.use1.cache.amazonaws.com"
    local.cache.max.size = 200 //used for maintaining list of policies
    cache.ttl = "5 minutes"
    cache.refresh.ttl = "4 minutes"
  }
  #=============== Token Bucket Rate Limiting Config End ============
  policies {
    cache.enabled = true
    fetch.timeout.ms = 200
    memcached {
      host = "product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port = 11211
      ttl = "24 hours"
    }
  }
}

#============== Vendor Call Data Auditing SNS =================#
vendor.call.auditing.client.sns {
  sns.topic = "socure-biller-service-dev-sns"
  sns.regions = [
    "us-east-1",
    "us-west-2"
  ]
  s3.bucket.fallback {
    name = "vendor-call-audit-errors-dev-************-us-east-1"
    region = "us-east-1"
    kms.id = "arn:aws:kms:us-east-1:************:alias/socure/s3"
  }
  retry {
    initial.backoff = "2 seconds"
    max.backoff = "32 seconds"
    multiplier = 2
    max.attempts = 10
  }
}
#============== Vendor Call Data Auditing SNS =================#

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-east-1"
  }
  memcached {
    host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
    port=11211
    ttl="24 hours"
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Control Center==========================#

#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-************-us-east-1"
      }
    memcached {
        host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

#============= API Rate Limit Timeout ==============
api.transaction.timeout.ms=3000
api.subaccount.timeout.ms=3000
api.reasoncode.timeout.ms=3000
api.event.timeout.ms=60000
api.eventKey.timeout.ms=3000
api.feedback.timeout.ms=3000
api.custom.watchlist.timeout.ms=10000
#============= API Rate Limit Timeout ==============

parameter.validation.flag.fetch.timeout = 50

#============= Internal Worklog ==============
internalWorkLogs {
  storage.logs.ttl = "2 minutes"
}
#============= Internal Worklog ==============

actorException.fallback.enabled=true
actorException.test.account.ids="7802"

#=============== Chronos Velocity Service ===============#
chronos.velocity.service {
  endpoint = "http://chronos-dpi-velocity-service"
  hmac {
    ttl=5
    time.interval=5
    realm="Socure"
    version = "1.0"
    strength=512
    aws.secrets.manager.id="dpi-velocity-service/dev/hmac-9131b467"
    secret.refresh.interval=5000
  }
}
chronos.olap.service {
  endpoint = "https://chronos-olap.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://chronos-olap.webapps.us-east-1.product-dev.socure.link"
  groupName = "IdplusServicesRamp"
  flagName = "ChronosOlapService_Ramp"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "chronos-olap-service/dev/hmac"
    secret.refresh.interval = 5000
  }
}
#=============== Chronos Velocity Service ===============#
chronos.olap.v3.service {
  endpoint = "https://chronos-olap-v3.eks.us-east-1.product-dev.socure.link"
}

#=============== IdPlus Public Service ===============#
idplus.public.service {
  endpoint = "https://idplus-public-service-dev.us-east-1.elasticbeanstalk.com"
  timeout = 4 # in Sec's
}
#=============== IdPlus Public Service ===============#


#=============== Features with Default Vendors =====
idPlus.defaultVendors = "NSRVL,FCEVL,TDEVL,NSIVL,NSGVL,SSEVL,EXXVL,EXVAL"
idPlus.featuresWithDefaultVendors = "FraudScore,PhoneRiskScore,AddressRiskScore,EmailRiskScore,NameEmailCorrelation,NamePhoneCorrelation,NameAddressCorrelation,AllCorrelations,Synthetic"
#=============== Features with Default Vendors =====

#=============== Default models resolved for given Features ===========
#=============== If any one of the below Features is requested, then the models associated with other remaining features in the list will be resolved ========
modelMapping.featuresRequireAllModelsLookup = "FraudScore,AddressRiskScore,EmailRiskScore,PhoneRiskScore,NameEmailCorrelation,NamePhoneCorrelation,NameAddressCorrelation,AllCorrelations,AuthScore,Synthetic"
#=============== Default models resolved for given Features ===========

#==================== AI Gateway Service ======================#
ai.gateway.service {
  endpoint = "http://ai-gateway-service"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="ai-gateway-service/dev/hmac-33a3712f"
    secret.refresh.interval=5000
  }
  grpc {
    host = "ai-gateway-service"
    port = "443"
    useTLS = true
  }
  use.grpc = false
}
#==================== AI Gateway Service ======================#

#==================== Custom Healthcheck Service ======================#
healthcheck {
  custom {
    apiKey = "2172b30b-5375-4660-8137-dcbd3e6e424d"
    request {
      userConsent=true
      firstName="Ann"
      surName="Greenberg"
      dob="1961-01-01"
      forceRefresh=true
      email="<EMAIL>"
      physicalAddress="33je iej2jf"
      ipAddress="*************"
      mobileNumber="**********"
      state="AZ"
      city="Newyork"
      zip="83838"
      modules=["fraud"]
      country="US"
      debug=true
    }
  }
}
#==================== Custom Healthcheck Service ======================#

# IDCORE-364 debug node mismatch fix
use.api.transaction.from.mdc = "false"

# IDCORE-545 push model score as metrics
model.scores.metrics.enabled = "true"

#==================== PII Standardization Service ======================#
pii.standardization.service {
  endpoint = "https://pii-standardization-service.eks.us-east-1.product-dev.socure.link"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id = "pii-standardization-service/dev/hmac-defcde"
    secret.refresh.interval = 5000
  }
}
#==================== PII Standardization Service ======================#
#==================== Suppressed Vendors ======================#
suppressed.vendors = "WPVAL,DFPVL,PFIVL,PFVVL,SSEVL,CVSVC,CVASVC,OTWVL,ARVAL,EPVAL,CGVAL,ENSVL,SCPVL,TWDVL,CONVL"
#==================== Suppressed Vendors ======================#

#==================== Self Warmup ======================#
self {
  warmup {
    enable = false
    apiKey = "2172b30b-5375-4660-8137-dcbd3e6e424d"
    num.requests = 10
    request.interval.in.ms = 5000
    request {
      userConsent=true
      firstName="Ann"
      surName="Greenberg"
      dob="1961-01-01"
      forceRefresh=true
      email="<EMAIL>"
      physicalAddress="33je iej2jf"
      ipAddress="*************"
      mobileNumber="**********"
      state="AZ"
      city="Newyork"
      zip="83838"
      modules=["fraud"]
      country="US"
      debug=true
    }
  }
}
#==================== Self Warmup ======================#

timezone.resolution.cache {
  enabled = "true"
  size = 5000
  refresh.ttl.minutes = 90
  expire.ttl.minutes = 120
}

#======================== E-KYC SLA ========================#
ekyc.sla.api.v2.scheduler.timeout ="18000"
ekyc.sla.api.v2.overall.timeout ="19000"
ekyc.sla.api.system.overall.timeout=20000
ekyc.sla.worker.EMAIL_VAL.timeout=5000
ekyc.sla.worker.BMEVL.timeout=5000
ekyc.sla.worker.FORM_VAL.timeout=5000
ekyc.sla.worker.FB_VAL.timeout=5000
ekyc.sla.worker.PIPL_EMAIL_VAL.timeout=5000
ekyc.sla.worker.PIPL_ADDRESS_VAL.timeout=5000
ekyc.sla.worker.GPLUS_VAL.timeout=5000
ekyc.sla.worker.PIPL_EMAIL_VAL_NOT_SPONS.timeout=5000
ekyc.sla.worker.PIPL_ADDRESS_VAL_NOT_SPONS.timeout=5000
ekyc.sla.worker.FB_VAL_ID.timeout=5000
ekyc.sla.worker.TW_VAL.timeout=5000
ekyc.sla.worker.TW_VAL_ID.timeout=5000
ekyc.sla.worker.LN_VAL.timeout=5000
ekyc.sla.worker.LN_VAL_ID.timeout=5000
ekyc.sla.worker.TOWER_DATA_VAL.timeout=5000
ekyc.sla.worker.FULL_CONTACT_VAL.timeout=5000
ekyc.sla.worker.FCVAL_V3.timeout=5000
ekyc.sla.worker.FULL_CONTACT_VAL_PHONE.timeout=5000
ekyc.sla.worker.PP_VAL.timeout=5000
ekyc.sla.worker.FB_VAL_DB.timeout=1000
ekyc.sla.worker.EMAIL_FORM_VAL.timeout=5000
ekyc.sla.worker.WLVAL.timeout=5000
ekyc.sla.worker.COMPLY_WLVAL.timeout=5000
ekyc.sla.worker.InternalWatchlistPrivate.timeout=5000
ekyc.sla.worker.InternalEntityMonitoring.timeout=5000
ekyc.sla.worker.SSVAL.timeout=5000
ekyc.sla.worker.KYC_VAL.timeout=5000
ekyc.sla.worker.ACVAL.timeout=5000
ekyc.sla.worker.IMVAL.timeout=5000
ekyc.sla.worker.LIANL.timeout=5000
ekyc.sla.worker.PYVAL.timeout=5000
ekyc.sla.worker.PNANL.timeout=5000
ekyc.sla.worker.XNANL.timeout=5000
ekyc.sla.worker.PB_VAL.timeout=5000
ekyc.sla.worker.WPVAL.timeout=5000
ekyc.sla.worker.WPIVL.timeout=5000
ekyc.sla.worker.NSVAL.timeout=5000
ekyc.sla.worker.VDANL.timeout=5000
ekyc.sla.worker.LXVAL.timeout=5000
ekyc.sla.worker.INANL.timeout=5000
ekyc.sla.worker.PCVAL.timeout=5000
ekyc.sla.worker.FVANL.timeout=5000
ekyc.sla.worker.PNVAL.timeout=4000
ekyc.sla.worker.FBANL.timeout=4000
ekyc.sla.worker.TWANL.timeout=4000
ekyc.sla.worker.GPANL.timeout=4000
ekyc.sla.worker.AUVAL.timeout=4000
ekyc.sla.worker.IFVAL.timeout=4000
ekyc.sla.worker.HDVAL.timeout=4000
ekyc.sla.worker.BLVAL.timeout=1000
ekyc.sla.worker.TIVAL.timeout=4000
ekyc.sla.worker.IGVAL.timeout=4000
ekyc.sla.worker.TSVAL.timeout=4000
ekyc.sla.worker.IDVVL.timeout=4000
ekyc.sla.worker.MLAVL.timeout=4000
ekyc.sla.worker.TSSVL.timeout=4000
ekyc.sla.worker.ALVAL.timeout=4000
ekyc.sla.worker.TDVAL.timeout=4000
ekyc.sla.worker.AIVAL.timeout=4000
ekyc.sla.worker.FALVL.timeout=4000
ekyc.sla.worker.CVVAL.timeout=4000
ekyc.sla.worker.PFIVL.timeout=4000
ekyc.sla.worker.PFVVL.timeout=4000
ekyc.sla.worker.DFPVL.timeout=1800
ekyc.sla.worker.VEIVL.timeout=1000
ekyc.sla.worker.EXVAL.timeout=4000
ekyc.sla.worker.EXXVL.timeout=4000
ekyc.sla.worker.VRVAL.timeout=4000
ekyc.sla.worker.SMSVL.timeout=1000
ekyc.sla.worker.EXPVL.timeout=4000
ekyc.sla.worker.EXAVL.timeout=4000
ekyc.sla.worker.EXSVL.timeout=4000
ekyc.sla.worker.EXEVL.timeout=4000
ekyc.sla.worker.SSEVL.timeout=4000
ekyc.sla.worker.TDEVL.timeout=4000
ekyc.sla.worker.IFEVL.timeout=4000
ekyc.sla.worker.IFAVL.timeout=4000
ekyc.sla.worker.IFPVL.timeout=4000
ekyc.sla.worker.IFIVL.timeout=4000
ekyc.sla.worker.DEVAL.timeout=1000
ekyc.sla.worker.ECBSV.timeout=1000
ekyc.sla.worker.PRVAL.timeout=4000
ekyc.sla.worker.RCSVC.timeout=4000
ekyc.sla.worker.FPSVC_FRAUD.timeout=4000
ekyc.sla.worker.IDRND.timeout=4000
ekyc.sla.worker.SECVL.timeout=4000
ekyc.sla.worker.SRVVL.timeout=4000
ekyc.sla.worker.FCEVL.timeout=4000
ekyc.sla.worker.FCEVL_SOCIAL_PROFILES.timeout=4000
ekyc.sla.worker.NSRVL.timeout=4000
ekyc.sla.worker.ASAVL.timeout=4000
ekyc.sla.worker.NSIVL.timeout=4000
ekyc.sla.worker.NSGVL.timeout=4000
ekyc.sla.worker.ATTMVL.timeout=4000
ekyc.sla.worker.ENFORMION.timeout=4000
ekyc.sla.worker.VCAVL.timeout=4000
ekyc.sla.worker.VNAVL.timeout=4000
ekyc.sla.worker.VCEVL.timeout=4000
ekyc.sla.worker.VNEVL.timeout=4000
ekyc.sla.worker.VCPVL.timeout=4000
ekyc.sla.worker.VNPVL.timeout=4000
ekyc.sla.worker.VCIVL.timeout=4000
ekyc.sla.worker.VNIVL.timeout=4000
ekyc.sla.worker.VCSVL.timeout=4000
ekyc.sla.worker.VCRVL.timeout=4000
ekyc.sla.worker.VNRVL.timeout=4000
ekyc.sla.worker.AID6VAL.timeout=4000
ekyc.sla.worker.BBVAL.timeout=4000
ekyc.sla.worker.CVSVC.timeout=4000
ekyc.sla.worker.AISVC.timeout=4000
ekyc.sla.worker.BNYVL.timeout=4000
ekyc.sla.worker.MBTVL.timeout=4000
ekyc.sla.worker.TRICE.timeout=4000
ekyc.sla.worker.EXNVL.timeout=4000
ekyc.sla.worker.SYVAL.timeout=4000
ekyc.sla.worker.ENPVL.timeout=4000
ekyc.sla.worker.VCDVL.timeout=4000
ekyc.sla.worker.VAIVL.timeout=4000
ekyc.sla.worker.SOCVL.timeout=1000
ekyc.sla.worker.CVASVC.timeout=1000
ekyc.sla.worker.VACAVL.timeout=4000
ekyc.sla.worker.VANAVL.timeout=4000
ekyc.sla.worker.VACEVL.timeout=4000
ekyc.sla.worker.VANEVL.timeout=4000
ekyc.sla.worker.VACPVL.timeout=4000
ekyc.sla.worker.VANPVL.timeout=4000
ekyc.sla.worker.VACIVL.timeout=4000
ekyc.sla.worker.VANIVL.timeout=4000
ekyc.sla.worker.VACSVL.timeout=4000
ekyc.sla.worker.VACDVL.timeout=4000
ekyc.sla.worker.VAAIVL.timeout=4000
ekyc.sla.worker.LSNVL.timeout=4000
ekyc.sla.worker.GA_SVC.timeout=15000
ekyc.sla.worker.MELVL.timeout=15000
ekyc.sla.worker.MDVAL.timeout=15000
ekyc.sla.worker.KYCSVC.timeout=18000
ekyc.sla.worker.DZVAL.timeout=18000
ekyc.sla.worker.FPFVL.timeout=4000
ekyc.sla.worker.NPLVL.timeout=4000
ekyc.sla.worker.FAVAL.timeout=4000
ekyc.sla.worker.CGVAL.timeout=4000
ekyc.sla.worker.ARVAL.timeout=4000
ekyc.sla.worker.EPVAL.timeout=4000
ekyc.sla.worker.S4SVC.timeout=4000
ekyc.sla.worker.OTWVL.timeout=4000
ekyc.sla.worker.SCPVL.timeout=4000
ekyc.sla.worker.TWDVL.timeout=4000
ekyc.sla.worker.ENSVL.timeout=4000
ekyc.sla.worker.EFXUK.timeout=15000
ekyc.sla.worker.EDMVL.timeout=15000
ekyc.sla.worker.TWIVL.timeout=4000
ekyc.sla.worker.CONVL.timeout=4000
ekyc.sla.worker.MNIVL.timeout=15000
ekyc.sla.worker.RCSVC_INT.timeout=30000
ekyc.sla.worker.IMIVL.timeout=15000

#======================== E-KYC SLA ========================#

#======================== SAI SLA ========================#
sai.sla.api.v2.scheduler.timeout ="18000"
sai.sla.api.v2.overall.timeout ="19000"
sai.sla.api.system.overall.timeout=20000
sai.sla.worker.EMAIL_VAL.timeout=5000
sai.sla.worker.BMEVL.timeout=5000
sai.sla.worker.FORM_VAL.timeout=5000
sai.sla.worker.FB_VAL.timeout=5000
sai.sla.worker.PIPL_EMAIL_VAL.timeout=5000
sai.sla.worker.PIPL_ADDRESS_VAL.timeout=5000
sai.sla.worker.GPLUS_VAL.timeout=5000
sai.sla.worker.PIPL_EMAIL_VAL_NOT_SPONS.timeout=5000
sai.sla.worker.PIPL_ADDRESS_VAL_NOT_SPONS.timeout=5000
sai.sla.worker.FB_VAL_ID.timeout=5000
sai.sla.worker.TW_VAL.timeout=5000
sai.sla.worker.TW_VAL_ID.timeout=5000
sai.sla.worker.LN_VAL.timeout=5000
sai.sla.worker.LN_VAL_ID.timeout=5000
sai.sla.worker.TOWER_DATA_VAL.timeout=5000
sai.sla.worker.FULL_CONTACT_VAL.timeout=5000
sai.sla.worker.FCVAL_V3.timeout=5000
sai.sla.worker.FULL_CONTACT_VAL_PHONE.timeout=5000
sai.sla.worker.PP_VAL.timeout=5000
sai.sla.worker.FB_VAL_DB.timeout=1000
sai.sla.worker.EMAIL_FORM_VAL.timeout=5000
sai.sla.worker.WLVAL.timeout=5000
sai.sla.worker.COMPLY_WLVAL.timeout=5000
sai.sla.worker.InternalWatchlistPrivate.timeout=5000
sai.sla.worker.InternalEntityMonitoring.timeout=5000
sai.sla.worker.SSVAL.timeout=5000
sai.sla.worker.KYC_VAL.timeout=5000
sai.sla.worker.ACVAL.timeout=5000
sai.sla.worker.IMVAL.timeout=5000
sai.sla.worker.LIANL.timeout=5000
sai.sla.worker.PYVAL.timeout=5000
sai.sla.worker.PNANL.timeout=5000
sai.sla.worker.XNANL.timeout=5000
sai.sla.worker.PB_VAL.timeout=5000
sai.sla.worker.WPVAL.timeout=5000
sai.sla.worker.WPIVL.timeout=5000
sai.sla.worker.NSVAL.timeout=5000
sai.sla.worker.VDANL.timeout=5000
sai.sla.worker.LXVAL.timeout=5000
sai.sla.worker.INANL.timeout=5000
sai.sla.worker.PCVAL.timeout=5000
sai.sla.worker.FVANL.timeout=5000
sai.sla.worker.PNVAL.timeout=4000
sai.sla.worker.FBANL.timeout=4000
sai.sla.worker.TWANL.timeout=4000
sai.sla.worker.GPANL.timeout=4000
sai.sla.worker.AUVAL.timeout=4000
sai.sla.worker.IFVAL.timeout=4000
sai.sla.worker.HDVAL.timeout=4000
sai.sla.worker.BLVAL.timeout=1000
sai.sla.worker.TIVAL.timeout=4000
sai.sla.worker.IGVAL.timeout=4000
sai.sla.worker.TSVAL.timeout=4000
sai.sla.worker.IDVVL.timeout=4000
sai.sla.worker.MLAVL.timeout=4000
sai.sla.worker.TSSVL.timeout=4000
sai.sla.worker.ALVAL.timeout=4000
sai.sla.worker.TDVAL.timeout=4000
sai.sla.worker.AIVAL.timeout=4000
sai.sla.worker.FALVL.timeout=4000
sai.sla.worker.CVVAL.timeout=4000
sai.sla.worker.PFIVL.timeout=4000
sai.sla.worker.PFVVL.timeout=4000
sai.sla.worker.DFPVL.timeout=1800
sai.sla.worker.VEIVL.timeout=1000
sai.sla.worker.EXVAL.timeout=4000
sai.sla.worker.EXXVL.timeout=4000
sai.sla.worker.VRVAL.timeout=4000
sai.sla.worker.SMSVL.timeout=1000
sai.sla.worker.EXPVL.timeout=4000
sai.sla.worker.EXAVL.timeout=4000
sai.sla.worker.EXSVL.timeout=4000
sai.sla.worker.EXEVL.timeout=4000
sai.sla.worker.SSEVL.timeout=4000
sai.sla.worker.TDEVL.timeout=4000
sai.sla.worker.IFEVL.timeout=4000
sai.sla.worker.IFAVL.timeout=4000
sai.sla.worker.IFPVL.timeout=4000
sai.sla.worker.IFIVL.timeout=4000
sai.sla.worker.DEVAL.timeout=1000
sai.sla.worker.ECBSV.timeout=1000
sai.sla.worker.PRVAL.timeout=4000
sai.sla.worker.RCSVC.timeout=4000
sai.sla.worker.FPSVC_FRAUD.timeout=4000
sai.sla.worker.IDRND.timeout=4000
sai.sla.worker.SECVL.timeout=4000
sai.sla.worker.SRVVL.timeout=4000
sai.sla.worker.FCEVL.timeout=4000
sai.sla.worker.FCEVL_SOCIAL_PROFILES.timeout=4000
sai.sla.worker.NSRVL.timeout=4000
sai.sla.worker.ASAVL.timeout=4000
sai.sla.worker.NSIVL.timeout=4000
sai.sla.worker.NSGVL.timeout=4000
sai.sla.worker.ATTMVL.timeout=4000
sai.sla.worker.ENFORMION.timeout=4000
sai.sla.worker.VCAVL.timeout=4000
sai.sla.worker.VNAVL.timeout=4000
sai.sla.worker.VCEVL.timeout=4000
sai.sla.worker.VNEVL.timeout=4000
sai.sla.worker.VCPVL.timeout=4000
sai.sla.worker.VNPVL.timeout=4000
sai.sla.worker.VCIVL.timeout=4000
sai.sla.worker.VNIVL.timeout=4000
sai.sla.worker.VCSVL.timeout=4000
sai.sla.worker.VCRVL.timeout=4000
sai.sla.worker.VNRVL.timeout=4000
sai.sla.worker.AID6VAL.timeout=4000
sai.sla.worker.BBVAL.timeout=4000
sai.sla.worker.CVSVC.timeout=4000
sai.sla.worker.AISVC.timeout=10000
sai.sla.worker.BNYVL.timeout=4000
sai.sla.worker.MBTVL.timeout=4000
sai.sla.worker.TRICE.timeout=7000
sai.sla.worker.EXNVL.timeout=4000
sai.sla.worker.SYVAL.timeout=4000
sai.sla.worker.ENPVL.timeout=4000
sai.sla.worker.VCDVL.timeout=4000
sai.sla.worker.VAIVL.timeout=4000
sai.sla.worker.SOCVL.timeout=1000
sai.sla.worker.CVASVC.timeout=1000
sai.sla.worker.VACAVL.timeout=4000
sai.sla.worker.VANAVL.timeout=4000
sai.sla.worker.VACEVL.timeout=4000
sai.sla.worker.VANEVL.timeout=4000
sai.sla.worker.VACPVL.timeout=4000
sai.sla.worker.VANPVL.timeout=4000
sai.sla.worker.VACIVL.timeout=4000
sai.sla.worker.VANIVL.timeout=4000
sai.sla.worker.VACSVL.timeout=4000
sai.sla.worker.VACDVL.timeout=4000
sai.sla.worker.VAAIVL.timeout=4000
sai.sla.worker.LSNVL.timeout=4000
sai.sla.worker.GA_SVC.timeout=15000
sai.sla.worker.MELVL.timeout=15000
sai.sla.worker.MDVAL.timeout=15000
sai.sla.worker.KYCSVC.timeout=18000
sai.sla.worker.DZVAL.timeout=18000
sai.sla.worker.FPFVL.timeout=4000
sai.sla.worker.NPLVL.timeout=4000
sai.sla.worker.FAVAL.timeout=4000
sai.sla.worker.CGVAL.timeout=4000
sai.sla.worker.ARVAL.timeout=4000
sai.sla.worker.EPVAL.timeout=4000
sai.sla.worker.S4SVC.timeout=4000
sai.sla.worker.OTWVL.timeout=4000
sai.sla.worker.SCPVL.timeout=4000
sai.sla.worker.TWDVL.timeout=4000
sai.sla.worker.ENSVL.timeout=4000
sai.sla.worker.EFXUK.timeout=15000
sai.sla.worker.EDMVL.timeout=15000
sai.sla.worker.TWIVL.timeout=4000
sai.sla.worker.CONVL.timeout=4000
sai.sla.worker.MNIVL.timeout=4000
sai.sla.worker.RCSVC_INT.timeout=4000
sai.sla.worker.IMIVL.timeout=4000

#======================== SAI SLA ========================#

#=================== Neg Pos ============================#
negPos.url="label-list-service"
negPos.port=81
negPos.useTLS=false

reasoncode.engine {
  enabled = false
  grpc {
    host = "reasoncode-engine.webapps.us-east-1.product-dev.socure.link"
    port = "443"
    use.tls = true
  }
  flag.name = "reason-code-refactoring"
}

feature.platform {
  enabled = true
  grpc {
    host = "feature-platform-service.webapps.us-east-1.product-dev.socure.link"
    port = "443"
    use.tls = true
  }
}

launchdarkly {
  sdk.key = "ENC(TrxFojosIw6l3A3Bt8udc4Hzbr0fCgLst7mxoN8FoisAdndsWNbuk4fbB998X787EvGdtAl7VMcHEl2diXMMYYIhubVwYmjK)"
  use.fedramp.version = false
}

#==================== Rulecode Precision customised for Vendor, defaults to 4======================#
ARVAL.score.precision = 8
CGVAL.score.precision = 8
OTWVL.score.precision = 8
EPVAL.score.precision = 8

vendor.resolution.newflow.enabled=true


kyc.v2.service {
  host = "https://kyc-v2-service.webapps.us-east-1.product-dev.socure.link"
}

mfa.orchestrator.service {
  endpoint = "https://mfa-orchestrator-service.webapps.us-east-1.product-dev.socure.link"
}

idplus.transaction.processor {
  enabled = true
  flag {
    name = "enable-idplus-transaction-processor"
    timeout.millis = 50
  }
  host = "https://idplus-transaction-processor.webapps.us-east-1.product-dev.socure.link"
  host2 = "http://localhost:7000"
  modules.supported = ["alertlist", "fraud", "synthetic", "addressrisk", "emailrisk", "phonerisk", "decision"]
  exclude.headers = ["host", "content-length", "connection", "keep-alive"]
}

thirdparty.auditing.v2 {
    enabled = false
    flagWaitTimeoutMillis = 100
    aws {
        msk.cluster {
            bootstrap.servers {
              primary = "boot-wgls83kf.c3.kafka-serverless.us-east-1.amazonaws.com:9098"
            }
            topic.name = "thirdparty-auditing-dev"
        }
        s3 {
            region = "us-east-1"
            bucket = "thirdparty-stats-errors-dev-************-us-east-1"
            prefix = "send_errors"
            kmsArn = "arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
        }
    }
}