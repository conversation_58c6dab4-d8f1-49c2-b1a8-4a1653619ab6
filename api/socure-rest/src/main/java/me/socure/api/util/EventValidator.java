package me.socure.api.util;

import me.socure.api.constant.ErrorMessage;
import me.socure.api.constant.Operation;
import me.socure.api.constant.SubscriptionType;
import me.socure.common.json.bean.watchlist.MonitorEventRequestParameter;
import me.socure.common.json.bean.watchlist.WatchlistMatchItem;
import me.socure.common.request.api.SocureKeyExtractor;
import me.socure.service.factory.watchlistprivate.WatchlistPrivateService;
import me.socure.util.EnvUtil;
import me.socure.util.Utilities;
import me.socure.validator.exceptions.ValidationException;
import me.socure.watchlist.common.util.EntityIdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import scala.Option;
import scala.collection.JavaConversions;
import scala.collection.immutable.Set;
import scala.compat.java8.FutureConverters;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Class to validate events received by api/event  endpoint in ID+.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class EventValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(EventValidator.class);
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    public void validate(final MonitorEventRequestParameter inputParameter) throws ValidationException {
        if (inputParameter == null) {
            throw new ValidationException(ErrorMessage.MISSING_FIELD);
        }
        validateUserConsent(inputParameter.userConsent());
        validateOperation(inputParameter.operation());
        validateEntityIds(inputParameter);
        validateSubscriptionType(inputParameter.subscriptionType());
        validateReferenceId(inputParameter.referenceId());
    }

    public void validateUserConsent(final Option<Object> userConsentOptional) throws ValidationException {
        if (userConsentOptional == null || !userConsentOptional.isDefined()) {
            throw new ValidationException(ErrorMessage.MISSING_FIELD);
        }
        Object declaredObject = userConsentOptional.get();
        if (!(declaredObject instanceof Boolean) || declaredObject == null) {
            throw new ValidationException(ErrorMessage.INVALID_USER_CONSENT);
        }
    }

    public void validateOperation(final String operation) throws ValidationException {
        if (operation == null) {
            throw new ValidationException(ErrorMessage.MISSING_FIELD);
        } else if (!Operation.isValid(operation)) {
            throw new ValidationException(ErrorMessage.INVALID_OPERATION_FIELD);
        }
    }

    public void validateMonitoringStatus(final String monitoringStatus) throws ValidationException {
        if (monitoringStatus != null && !monitoringStatus.trim().equals("enabled")) {
            throw new ValidationException(ErrorMessage.INVALID_MONITORING_STATUS);
        }
    }

    public void validateSortDirection(final String sortDir) throws ValidationException {
        if (sortDir != null && !sortDir.trim().equals("asc") && !sortDir.trim().equals("desc")) {
            throw new ValidationException(ErrorMessage.INVALID_SORT_DIRECTION);
        }
    }

    public void validateDate(final String date) throws ValidationException {
        if (date != null) {
            try {
                DateTime converted = DateTime.parse(date, DateTimeFormat.forPattern(DATE_PATTERN));
                if (converted == null) {
                    throw new ValidationException(ErrorMessage.INVALID_DATE_FORMAT);
                } else if (DateTime.now(DateTimeZone.UTC).isBefore(converted)) {
                    throw new ValidationException(ErrorMessage.INVALID_DATE_VALUE);
                }
            } catch (ValidationException ex) {
                throw ex;
            } catch (Exception ex) {
                throw new ValidationException(ErrorMessage.INVALID_DATE_FORMAT);
            }
        }
    }

    public void validateDateFromTo(final String dateFrom, final String dateTo) throws ValidationException {
        if (dateFrom != null && dateTo != null) {
            DateTime convertedDateFrom = DateTime.parse(dateFrom, DateTimeFormat.forPattern(DATE_PATTERN));
            DateTime convertedDateTo = DateTime.parse(dateTo, DateTimeFormat.forPattern(DATE_PATTERN));
            if (convertedDateFrom.isAfter(convertedDateTo))
                throw new ValidationException(ErrorMessage.INVALID_DATE_FROM_TO_VALUE);
        }
    }

    public void validatePage(final String page) throws ValidationException {
        if (page != null && ((Utilities.WrapperUtil.parseInt(page.trim()) == null) || (Utilities.WrapperUtil.parseInt(page.trim()) <= 0 ))) {
            throw new ValidationException(ErrorMessage.INVALID_PAGE);
        }
    }

    public void validateSubscriptionType(final String subscriptionType) throws ValidationException {
        if (subscriptionType == null) {
            throw new ValidationException(ErrorMessage.MISSING_FIELD);
        } else if (!SubscriptionType.isValid(subscriptionType)) {
            throw new ValidationException(ErrorMessage.INVALID_SUBSCRIPTION_TYPE_FIELD);
        }
    }

    public boolean isValidApiKey(final String apiKey) {
        try {
            Optional<String> optional = SocureKeyExtractor.extract(apiKey);
            if (optional.isPresent()) {
                UUID.fromString(optional.get());
            } else {
                return false;
            }
        } catch(Exception ex) {
            return false;
        }
        return true;
    }

    public void validateReferenceId(final String referenceId) throws ValidationException {
        if (referenceId == null) {
            throw new ValidationException(ErrorMessage.MISSING_FIELD);
        }
        try {
            UUID.fromString(referenceId);
        } catch(Exception ex) {
            throw new ValidationException(ErrorMessage.ACCOUNT_NOT_ASSOCIATED, ex);
        }
    }

    public void validateEntityIds(final MonitorEventRequestParameter inputParameter) throws ValidationException {
        if (Operation.SUPPRESS.getOperationName().equals(inputParameter.operation())
                || Operation.UNSUPPRESS.getOperationName().equals(inputParameter.operation())) {
            Option<Set<String>> entityIds = inputParameter.entityIds();
            if (entityIds == null || !entityIds.isDefined() || entityIds.get().isEmpty()) {
                throw new ValidationException(ErrorMessage.MISSING_FIELD);
            }
        }
    }

    public void validateEntityIds(final String transactionId,
                                  final long accountId,
                                  final String envName,
                                  final Set<String> entitiesToBeValidated,
                                  final WatchlistPrivateService watchlistPrivateService,
                                  final EnvUtil envUtil) throws ValidationException {
        String encKey = envUtil.getProperty("enc.key", null);
        if(StringUtils.isNotEmpty(encKey)) {
            EntityIdGenerator entityIdGenerator = new EntityIdGenerator(encKey, "RC4");
            java.util.Set<String> nonInHouseEntityIds;
            try {
                nonInHouseEntityIds =
                    JavaConversions.setAsJavaSet(entitiesToBeValidated)
                        .stream()
                        .filter(entity -> !entityIdGenerator.decrypt(entity)._2().startsWith("IHE_"))
                        .collect(Collectors.toSet());
            }
            catch(Exception ex) {
                LOGGER.error("Error in decrypting entity ids {} due to ", entitiesToBeValidated, ex);
                throw new ValidationException(ErrorMessage.ACCOUNT_NOT_ASSOCIATED);
            }
            if(!nonInHouseEntityIds.isEmpty()) {
                java.util.Set<String> idsFromSnapshot = getIdsFromSnapshot(transactionId, accountId, envName, watchlistPrivateService);
                if(!idsFromSnapshot.containsAll(nonInHouseEntityIds)) {
                    LOGGER.info("Entity Ids retrieved from Snapshot: {} doesn't contain one (or) more Entity Ids passed in Input: {} for transactionId: {} accountId: {} envName: {}", idsFromSnapshot, entitiesToBeValidated, transactionId, accountId, envName);
                    throw new ValidationException(ErrorMessage.INVALID_ENTITY_SUPPRESSION_REQUEST);
                }
            }
        }
    }

    protected java.util.Set<String> getIdsFromSnapshot(final String transactionId,
        final long accountId,
        final String envName,
        final WatchlistPrivateService watchlistPrivateService) {
        try {
            final Map<String, java.util.Set<WatchlistMatchItem>> entityDetailsMap =
                FutureConverters.toJava(watchlistPrivateService.snapshotViewJava(transactionId,
                        accountId,
                        envName,
                        Option.apply(true)))
                    .toCompletableFuture()
                    .join()
                    .getGlobalWatchlist().getMatches();
            return entityDetailsMap.values()
                .stream()
                .flatMap(java.util.Set::stream)
                .map(eachWlItem -> eachWlItem.entityId())
                .collect(Collectors.toSet());
        }
        catch(Exception e) {
            LOGGER.info("Error occurred in retrieving EntityIds from Snapshot for transactionId:{} accountId: {} envName: {} due to ", transactionId, accountId, envName, e);
            return new java.util.HashSet<String>();
        }
    }

}
