<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


	<!--Account Service -->
	<bean id="activeUserClient" class="me.socure.account.service.factory.ActiveUserClientFactory"/>

	<bean id="businessUserRoleClient" class="me.socure.account.service.factory.BusinessUserRoleClientFactory"/>

	<bean id="adminUsersClient" class="me.socure.account.service.factory.AdminUsersClientFactory"/>

	<bean id="lockedUserClient" class="me.socure.account.service.factory.LockedUserClientFactory"/>

	<bean id="delegatedAdminClient" class="me.socure.account.service.factory.DelegatedAdminClientFactory"/>

	<bean id="accountSettingClient" class="me.socure.account.service.factory.AccountSettingClientFactory"/>

	<bean id="accountSettingsViewClient" class="me.socure.account.service.factory.AccountSettingsViewClientFactory"/>

	<bean id="dashboardUserClient" class="me.socure.account.service.factory.DashboardUserClientFactory"/>

	<bean id="manageAccountsClient" class="me.socure.account.service.factory.ManageAccountsClientFactory"/>

	<bean id="manageAccountsV2Client" class="me.socure.account.service.factory.ManageAccountsV2Factory"/>

	<bean id="leaderboardClient" class="me.socure.service.accountservice.LeaderboardClientFactory"/>

	<bean id="blacklistClient" class="me.socure.service.account.BlacklistClientFactory" />

	<bean id="accountInfoClient" class="me.socure.service.account.AccountInfoClientFactory" />

	<bean id="industryClient" class="me.socure.account.service.factory.IndustryManagementClientFactory" />

	<bean id="dashboardDomainClient" class="me.socure.account.service.factory.DashboardDomainClientFactory" />

	<bean id="pgpKeysClient" class="me.socure.account.service.factory.PGPKeyManagmentClientFactory" />

	<bean id="pgpSignaturePublicKeyManagementClient" class="me.socure.account.service.factory.PgpSignaturePublicKeyManagmentClientFactory" />

	<bean id="idpMetadataManagementClient" class="me.socure.account.service.factory.IdpMetadataManagementClientFactory" />

	<bean id="subAccountManagementClient" class="me.socure.account.service.factory.SubAccountManagementClientFactory" />

	<bean id="environmentSettingsClient" class="me.socure.account.service.factory.EnvironmentSettingsClientFactory" />

	<bean id="subscriptionsClient" class="me.socure.account.service.factory.SubscriptionsClientFactory" />

	<bean id="watchlistSourceClient" class="me.socure.account.service.factory.WatchlistSourceClientFactory" />

	<bean id="mlaFieldUpdateClient" class="me.socure.account.service.factory.MLAServiceClientFactory" />

	<bean id="einClient" class="me.socure.account.service.factory.EINServiceClientFactory" />

	<bean id="partnerAndSubAccountInfoClient" class="me.socure.account.service.factory.PartnerAndSubAccountInfoClientFactory" />

	<!-- Rate Limit Client-->
<!--	<bean id="ratelimitConfigClient" class="me.socure.account.service.factory.RateLimitConfigClientFactory"/>-->

	<!-- Dynamic Control Center Service Client -->
	<bean id="dynamicControlCenterServiceClient" class="me.socure.dynamic.control.center.factory.DynamicControlCenterServiceFactory"/>
	<bean id="dynamicControlCenterV2Upload" class="me.socure.dynamic.control.center.factory.DynamicControlCenterServiceFactoryV2"/>

</beans>
