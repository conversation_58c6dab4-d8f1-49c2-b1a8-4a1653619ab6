package me.socure.json.controllerv1.bean;

import me.socure.model.management.model.v2.Model;
import me.socure.model.management.client.dto.CreateModelRequest;
import scala.Option;

import java.util.Optional;

public class ModelManagementWrapperV2 {

    private String modelId;
    private String name;
    private boolean isLegacy;
    private Optional<String> commonName;
    private Optional<String> description;
    private Optional<String> defaultResponseName;
    private Optional<String> defaultResponseVersion;
    private String h2oIdentifier;
    private String engine;
    private Optional<Integer> featureId;
    private String params;
    private Optional<String> config;
    private boolean isDefault;
    private boolean isActive;
    private long createdAt;
    private long updatedAt;
    private boolean isImplicit;
    private Integer productId;

    public ModelManagementWrapperV2() {
    }

    public static ModelManagementWrapperV2 fromModel(me.socure.model.management.model.v2.Model model) {
        return new ModelManagementWrapperV2(model);
    }

    public ModelManagementWrapperV2(Model model) {
        this.modelId = model.modelId();
        this.name = model.name();
        this.isLegacy = model.isLegacy();
        this.commonName = model.commonName().isDefined() ? Optional.of(model.commonName().get()) : Optional.empty();
        this.description = model.description().isDefined() ? Optional.of(model.description().get()) : Optional.empty();
        this.defaultResponseName = model.defaultResponseName().isDefined() ? Optional.of(model.defaultResponseName().get()) : Optional.empty();
        this.defaultResponseVersion = model.defaultResponseVersion().isDefined() ? Optional.of(model.defaultResponseVersion().get()) : Optional.empty();
        this.h2oIdentifier = model.h2oIdentifier();
        this.engine = model.engine();
        this.featureId = model.featureId().isDefined() ? Optional.of(model.featureId().get()) : Optional.empty();
        this.params = model.params();
        this.config = model.config().isDefined() ? Optional.of(model.config().get()) : Optional.empty();
        this.isDefault = model.isDefault();
        this.isActive = model.isActive();
        this.createdAt = model.createdAt();
        this.updatedAt = model.updatedAt();
        this.isImplicit = model.isImplicit();
        this.productId = model.productId();
    }

    // Getters and Setters for all fields
    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isLegacy() {
        return isLegacy;
    }

    public void setLegacy(boolean legacy) {
        isLegacy = legacy;
    }

    public Optional<String> getCommonName() {
        return commonName;
    }

    public void setCommonName(Optional<String> commonName) {
        this.commonName = commonName;
    }

    public Optional<String> getDescription() {
        return description;
    }

    public void setDescription(Optional<String> description) {
        this.description = description;
    }

    public Optional<String> getDefaultResponseName() {
        return defaultResponseName;
    }

    public void setDefaultResponseName(Optional<String> defaultResponseName) {
        this.defaultResponseName = defaultResponseName;
    }

    public Optional<String> getDefaultResponseVersion() {
        return defaultResponseVersion;
    }

    public void setDefaultResponseVersion(Optional<String> defaultResponseVersion) {
        this.defaultResponseVersion = defaultResponseVersion;
    }

    public String getH2oIdentifier() {
        return h2oIdentifier;
    }

    public void setH2oIdentifier(String h2oIdentifier) {
        this.h2oIdentifier = h2oIdentifier;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public Optional<Integer> getFeatureId() {
        return featureId;
    }

    public void setFeatureId(Optional<Integer> featureId) {
        this.featureId = featureId;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public Optional<String> getConfig() {
        return config;
    }

    public void setConfig(Optional<String> config) {
        this.config = config;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isImplicit() {
        return isImplicit;
    }

    public void setImplicit(boolean implicit) {
        isImplicit = implicit;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * Convert this wrapper to a CreateModelRequest for the model management service
     */
    public CreateModelRequest toCreateModelRequest() {
        return new CreateModelRequest(
            this.name,
            this.commonName.orElse(""),
            this.description.isPresent() ? Option.apply(this.description.get()) : Option.empty(),
            this.h2oIdentifier,
            this.engine,
            this.featureId.map(String::valueOf).orElse(""),
            this.config.isPresent() ? Option.apply(this.config.get()) : Option.empty(),
            Option.apply(this.isImplicit),
            Option.apply(this.isDefault),
            this.defaultResponseName.orElse(""),
            this.defaultResponseVersion.orElse(""),
            this.productId != null ? this.productId : 0,
            true
        );
    }
}