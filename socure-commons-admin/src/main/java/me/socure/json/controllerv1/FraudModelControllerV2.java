package me.socure.json.controllerv1;

import au.com.bytecode.opencsv.CSVReader;
import com.google.api.client.repackaged.com.google.common.base.Strings;
import me.socure.account.service.resolver.ModelParametersResolver;
import me.socure.common.clock.RealClock;
import me.socure.common.json.bean.APIOutput;
import me.socure.common.publicid.PublicIdGenerator;
import me.socure.common.publicid.SecureRandomPublicIdGenerator;
import me.socure.constants.EnvironmentTypes;
import me.socure.datasci.model.cache.CacheAwareModelService;
import me.socure.datasci.model.domain.*;
import me.socure.datasci.model.service.AssociationService;
import me.socure.h2omlpredictor.client.H2OMLPredictorClient;
import me.socure.h2opredictor.client.H2OPredictorClient;
import me.socure.json.controllerv1.bean.ModelManagementWrapper;
import me.socure.json.controllerv1.bean.ModelManagementWrapperV2;
import me.socure.json.controllerv1.bean.ModelWrapper;
import me.socure.model.ErrorResponse;
import me.socure.model.management.client.ModelManagementClientV2;
import me.socure.model.management.client.dto.CreateModelRequest;

import me.socure.model.management.model.v2.Model;
import me.socure.service.constants.IConstants;
import me.socure.util.AccountJsonUtil;
import me.socure.util.EnvUtil;
import me.socure.util.SecurityUtil;
import me.socure.util.ValidatorUtil;
import me.socure.util.api.SpringAPIResultUtil;
import me.socure.util.func.UnsafeSupplier;
import me.socure.util.model.ModelManagementUtil;
import me.socure.util.model.ModelUtil;
import me.socure.util.model.ModelValidator;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import scala.Option;
import scala.Some;
import scala.collection.Seq;
import scala.collection.JavaConverters;
import scala.concurrent.Await;
import scala.concurrent.duration.FiniteDuration;
import scala.util.Either;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "api/v2/fraudmodel/*")
public class FraudModelControllerV2 {

    private static final Logger logger = LoggerFactory.getLogger(FraudModelControllerV2.class);

    @Autowired
    ValidatorUtil validatorUtil;

    @Autowired
    private EnvUtil envUtil;

    @Autowired
    private CacheAwareModelService modelService;

    @Autowired
    @Qualifier("h2oPredictorClient")
    private H2OPredictorClient h2oPredictorClient;

    @Autowired
    @Qualifier("h2oMLPredictorClient")
    private H2OMLPredictorClient h2oMLPredictorClient;

    @Autowired
    @Qualifier("modelManagementClientV2")
    private ModelManagementClientV2 modelManagementClientV2;

    @Autowired
    @Qualifier("cachedAssociationService")
    private AssociationService associationService;

    @Autowired
    private AccountJsonUtil accountJsonUtil;

    @Autowired
    @Qualifier("modelParametersResolver")
    private ModelParametersResolver modelParametersResolver;

    private static Map<Integer, String> featuresMap = null;

    @RequestMapping(value = "/list_features", method = RequestMethod.GET)
    public @ResponseBody ResponseEntity<Object> listFeatures() {
        if(featuresMap == null) {
            featuresMap = ModelUtil.featuresMap();
        }
        APIOutput apiOutput = new APIOutput(IConstants.API_OUTPUT_STATUS_OK, "");
        apiOutput.setData(featuresMap);
        return new ResponseEntity<Object>(apiOutput, HttpStatus.OK);
    }

    @RequestMapping(value = "/models", method = RequestMethod.GET)
    public @ResponseBody ResponseEntity<Object> getModels(
            @RequestParam(value = "productId", required = false) String productId,
            @RequestParam(value = "isActive", required = false) Boolean isActive,
            @RequestParam(value = "isDefault", required = false) Boolean isDefault) {

        return handleExceptions(() -> {
            APIOutput apiOutput = new APIOutput();

            // Validate input parameters
            if (isActive != null && isDefault != null) {
                return new ResponseEntity<>(new APIOutput(IConstants.API_OUTPUT_STATUS_ERROR,
                        "Either isActive or isDefault can be given, not both."), HttpStatus.BAD_REQUEST);
            }

            if (isDefault != null && isDefault && productId != null) {
                return new ResponseEntity<>(new APIOutput(IConstants.API_OUTPUT_STATUS_ERROR,
                        "ProductId should not be provided when isDefault is true."), HttpStatus.BAD_REQUEST);
            }

            // Handle logic based on flags
            if (Boolean.TRUE.equals(isDefault)) {
                scala.util.Either<ErrorResponse, scala.collection.immutable.List<Model>> result =
                        getFromFuture(modelManagementClientV2.getDefaultModels());

                if (result.isRight()) {
                    List<ModelManagementWrapperV2> wrappedModels = getModelManagementWrapperV2s(result);
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                    apiOutput.setData(wrappedModels);
                } else {
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                    apiOutput.setMsg("Error retrieving default models: " + result.left().get().message());
                }
            } else if (isActive != null) {
                if (productId == null) {
                    return new ResponseEntity<>(new APIOutput(IConstants.API_OUTPUT_STATUS_ERROR,
                            "ProductId is required when isActive is provided."), HttpStatus.BAD_REQUEST);
                }

                scala.util.Either<ErrorResponse, scala.collection.immutable.List<Model>> result =
                        getFromFuture(modelManagementClientV2.getModelsByProduct(productId, isActive));

                if (result.isRight()) {
                    List<ModelManagementWrapperV2> wrappedModels = getModelManagementWrapperV2s(result);
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                    apiOutput.setData(wrappedModels);
                } else {
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                    apiOutput.setMsg("Error retrieving models: " + result.left().get().message());
                }
            } else {
                if (productId == null) {
                    return new ResponseEntity<>(new APIOutput(IConstants.API_OUTPUT_STATUS_ERROR,
                            "ProductId is required."), HttpStatus.BAD_REQUEST);
                }

                scala.util.Either<ErrorResponse, scala.collection.immutable.List<Model>> result =
                        getFromFuture(modelManagementClientV2.getAllModelsByProduct(productId));

                if (result.isRight()) {
                    List<ModelManagementWrapperV2> wrappedModels = getModelManagementWrapperV2s(result);
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                    apiOutput.setData(wrappedModels);
                } else {
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                    apiOutput.setMsg("Error retrieving all models: " + result.left().get().message());
                }
            }

            return new ResponseEntity<>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
        });
    }

    @NotNull
    private static List<ModelManagementWrapperV2> getModelManagementWrapperV2s(Either<ErrorResponse, scala.collection.immutable.List<Model>> result) {
        List<Model> models = JavaConverters.seqAsJavaListConverter(result.right().get()).asJava();
        List<ModelManagementWrapperV2> wrappedModels = models.stream()
                .map(ModelManagementWrapperV2::fromModel)
                .collect(Collectors.toList());
        return wrappedModels;
    }

    @RequestMapping(value = "/all_models", method = RequestMethod.GET)
    public
    @ResponseBody
    ResponseEntity<Object> allModels(@RequestParam(value ="productId", required=true) String productId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;

            APIOutput apiOutput = new APIOutput();
            scala.util.Either<ErrorResponse, scala.collection.immutable.List<Model>> result = getFromFuture(modelManagementClientV2.getAllModelsByProduct(productId));

            if (result.isRight()) {
                List<Model> models = JavaConverters.seqAsJavaListConverter(result.right().get()).asJava();
                if (models != null && models.size() > 0) {
                    apiOutput.setData(models);
                } else {
                    apiOutput.setMsg("No Fraud Model Service found.");
                }
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error retrieving models: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/default_models", method = RequestMethod.GET)
    public
    @ResponseBody
    ResponseEntity<Object> defaultModels() {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;

            APIOutput apiOutput = new APIOutput();
            scala.util.Either<ErrorResponse, scala.collection.immutable.List<Model>> result = getFromFuture(modelManagementClientV2.getDefaultModels());

            if (result.isRight()) {
                List<Model> models = JavaConverters.seqAsJavaListConverter(result.right().get()).asJava();
                if (models != null && models.size() > 0) {
                    models.forEach(model -> {
                      System.out.println("-----"+model.modelId()+"--"+model.toString());
                    });
                    apiOutput.setData(models);
                } else {
                    apiOutput.setMsg("No default models found.");
                }
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error retrieving default models: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/model", method = RequestMethod.GET)
    public
    @ResponseBody
    ResponseEntity<Object> getModel(@RequestParam(value ="modelId", required=true) String modelId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;

            APIOutput apiOutput = new APIOutput();
            Model model;
            scala.util.Either<ErrorResponse, Model> result = getFromFuture(modelManagementClientV2.getModelsById(modelId));

            if (result.isRight()) {
                model = result.right().get();
                if (model != null) {
                    apiOutput.setData(model);
                } else {
                    apiOutput.setMsg("Model not found.");
                }
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error retrieving model: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/register", method = RequestMethod.POST, consumes = "application/json")
    public
    @ResponseBody
    ResponseEntity<Object> register(@RequestBody ModelManagementWrapperV2 modelWrapper) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            // Convert wrapper to CreateModelRequest
            CreateModelRequest createModelRequest = modelWrapper.toCreateModelRequest();
            scala.util.Either<ErrorResponse, Model> result = getFromFuture(modelManagementClientV2.registerModel(createModelRequest));

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Fraud Model Service created successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error creating model: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/mark_legacy", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> markLegacy(@RequestParam("modelId") String modelId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(modelManagementClientV2.markModelAsLegacy(modelId));

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Model marked as legacy successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error marking model as legacy: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/activate", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> activate(@RequestParam("modelId") String modelId) {
        return handleExceptions(() -> {
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(modelManagementClientV2.activateModel(modelId));

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg(result.right().get()); // Set message directly from the client response
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg(result.left().get().message()); // Set error message directly from the client response
            }

            return new ResponseEntity<>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
        });
    }

    @RequestMapping(value = "/deactivate", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> deactivate(@RequestParam("modelId") String modelId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(modelManagementClientV2.deactivateModel(modelId));

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg(result.left().get().message()); // Set error message directly from the client response
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/create_normalisation", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> createNormalisation(@RequestParam("modelId") String modelId,
                                              @RequestParam("name") String name,
                                              @RequestParam(value = "config", required=false) String config,
                                              @RequestParam(value = "file", required=false) MultipartFile file,
                                              @RequestParam(value = "isDefault", required=false, defaultValue = "false") Boolean isDefault,
                                              HttpServletRequest request) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            InputStream quantileMapping = null;
            if (file != null) {
                try {
                    if (!validateCSV(file.getInputStream())) {
                        throw new IllegalArgumentException("Invalid CSV file");
                    }
                    quantileMapping = file.getInputStream();
                } catch (Exception ex) {
                    logger.error("Error while validating csv file: " + ex);
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                    apiOutput.setMsg("Validation Error: Not a valid CSV file for quantile mappings");
                    responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.BAD_REQUEST);
                    return responseEntity;
                }
            }

            scala.util.Either<ErrorResponse, String> result = getFromFuture(
                modelManagementClientV2.createNormalisation(
                    modelId,
                    name,
                    Option.apply(config),
                    Option.apply(quantileMapping),
                    isDefault
                )
            );

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Normalisation created successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error creating normalisation: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/normalisations", method = RequestMethod.GET)
    public
    @ResponseBody
    ResponseEntity<Object> getNormalisations(@RequestParam("modelId") String modelId,
                                            @RequestParam(value = "isActive", required=false, defaultValue = "false") Boolean isActive) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, scala.collection.immutable.List<me.socure.model.management.model.v2.Normalisation>> result =
                getFromFuture(modelManagementClientV2.getNormalisations(modelId, isActive));

            if (result.isRight()) {
                List<me.socure.model.management.model.v2.Normalisation> normalisations =
                    JavaConverters.seqAsJavaListConverter(result.right().get()).asJava();
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setData(normalisations);
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error retrieving normalisations: " + result.left().get().message());
            }


            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/update_normalisation", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> updateNormalisation(@RequestParam("modelId") String modelId,
                                              @RequestParam("normalisationId") String normalisationId,
                                              @RequestParam("name") String name,
                                              @RequestParam(value = "config", required=false) String config,
                                              @RequestParam(value = "file", required=false) MultipartFile file,
                                              HttpServletRequest request) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            InputStream quantileMapping = null;
            if (file != null) {
                try {
                    if (!validateCSV(file.getInputStream())) {
                        throw new IllegalArgumentException("Invalid CSV file");
                    }
                    quantileMapping = file.getInputStream();
                } catch (Exception ex) {
                    logger.error("Error while validating csv file: " + ex);
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                    apiOutput.setMsg("Validation Error: Not a valid CSV file for quantile mappings");
                    responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.BAD_REQUEST);
                    return responseEntity;
                }
            }

            scala.util.Either<ErrorResponse, String> result = getFromFuture(
                modelManagementClientV2.updateNormalisation(
                    modelId,
                    normalisationId,
                    name,
                    Option.apply(config),
                    Option.apply(quantileMapping)
                )
            );

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Normalisation updated successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error updating normalisation: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/activate_normalisation", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> activateNormalisation(@RequestParam("modelId") String modelId,
                                                @RequestParam("normalisationId") String normalisationId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(
                modelManagementClientV2.activateNormalisation(modelId, normalisationId)
            );

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Normalisation activated successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error activating normalisation: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/deactivate_normalisation", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> deactivateNormalisation(@RequestParam("modelId") String modelId,
                                                  @RequestParam("normalisationId") String normalisationId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(
                modelManagementClientV2.deactivateNormalisation(modelId, normalisationId)
            );

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Normalisation deactivated successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error deactivating normalisation: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    @RequestMapping(value = "/set_default_normalisation", method = RequestMethod.POST)
    public
    @ResponseBody
    ResponseEntity<Object> setDefaultNormalisation(@RequestParam("modelId") String modelId,
                                                  @RequestParam("normalisationId") String normalisationId) {
        return handleExceptions(() -> {
            ResponseEntity<Object> responseEntity = null;
            APIOutput apiOutput = new APIOutput();

            scala.util.Either<ErrorResponse, String> result = getFromFuture(
                modelManagementClientV2.setDefaultNormalisation(modelId, normalisationId)
            );

            if (result.isRight()) {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK);
                apiOutput.setMsg("Normalisation set as default successfully.");
                apiOutput.setData(result.right().get());
            } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR);
                apiOutput.setMsg("Error setting normalisation as default: " + result.left().get().message());
            }

            responseEntity = new ResponseEntity<Object>(apiOutput, SpringAPIResultUtil.getResponseHeaders(), HttpStatus.OK);
            return responseEntity;
        });
    }

    private boolean validateCSV(InputStream inputStream) throws IOException {
        CSVReader csvReader = null;
        try {
            csvReader = new CSVReader(new InputStreamReader(inputStream));
            String[] header = csvReader.readNext();

            if (header == null || header.length != 2 || !header[0].trim().equals("quantile") || !header[1].trim().equals("score")) {
                throw new IllegalArgumentException("Invalid headers for Quantile mapping");
            }

            String[] row;
            while ((row = csvReader.readNext()) != null) {
                if (row.length != 2) {
                    throw new IllegalArgumentException("CSV file must have exactly two columns in each row.");
                }
                validateRow(row[0]);
                validateRow(row[1]);
            }
            return true;
        } catch (Exception ex) {
            logger.error("Error while validating csv file: " + ex);
            return false;
        } finally {
            if (csvReader != null) {
                csvReader.close();
            }
        }
    }

    private boolean validateRow(String value) {
        try {
            Double.parseDouble(value);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Value '" + value + "' is not a valid number.");
        }
        return true;
    }

    private ResponseEntity<Object> handleExceptions(UnsafeSupplier<ResponseEntity<Object>> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Full exception details: ", e);
            logger.error("Exception message: " + e.getMessage());
            if (e.getCause() != null) {
                logger.error("Cause: " + e.getCause().getMessage());
            }
            return error(e.getMessage());
        }
    }

    private ResponseEntity<Object> error(String msg) {
        APIOutput apiOutput = new APIOutput(IConstants.API_OUTPUT_STATUS_ERROR, msg);
        return new ResponseEntity<Object>(apiOutput, HttpStatus.OK);
    }

    private <T> T getFromFuture(scala.concurrent.Future<T> future) throws Exception {
        return Await.result(future, timeout);
    }

    private static final FiniteDuration timeout = new FiniteDuration(120, TimeUnit.SECONDS);
}
