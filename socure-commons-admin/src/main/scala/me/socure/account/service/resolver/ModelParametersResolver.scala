package me.socure.account.service.resolver

import java.util.concurrent.TimeUnit

import dispatch.Future
import me.socure.datasci.model.domain.{ModelWithoutUrl, ModelWithoutUrlAndParams, ModelWithoutUrlAndParamsV2, ModelWithoutUrlV2}
import me.socure.h2opredictor.client.H2OPredictorClient
import me.socure.h2omlpredictor.client.H2OMLPredictorClient
import me.socure.h2omlpredictor.model.PredictorDescription
import me.socure.model.ErrorResponse
import org.json4s.JsonAST.{JField, JInt, JNull, JObject}
import org.json4s.jackson.JsonMethods._

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

/**
  * Created by alexand<PERSON> on 1/23/17.
  */
class ModelParametersResolver(h2oPredictorClient: H2OPredictorClient, h2oMLPredictorClient: H2OMLPredictorClient, h2oPredictorBase: String, h2oMLPredictorBase: String)(implicit ec: ExecutionContext) {

  private def formatParameters(parameters: Seq[String]): String = {
    val jsonParameters = parameters.map { p =>
      val regex = """(I|R|V|i|r|v)\d{3}"""
      JField(p, if ( p.matches(regex)) JInt(0) else JNull)
    }
    compact(render(JObject(jsonParameters: _*)))
  }

  def parameters(url: String): Option[String] = {
    val modelName = url.split("""/""").lastOption

    val futureModels = h2oPredictorClient.list()
    val futureParameters = futureModels.map {
      case Left(_) => None
      case Right(models) =>
        modelName.flatMap(models.get).map { modelDescription =>
          formatParameters(modelDescription.parameters)
        }
    }

    Await.result(futureParameters, Duration(40, TimeUnit.SECONDS))
  }

  def parametersV2(model: ModelWithoutUrlAndParamsV2): Option[String] = {
    if ("pojo".equals(model.h2oModelFormat)) {
      val url = s"${h2oPredictorBase.stripSuffix("/")}/${model.h2oIdentifier}"
      val modelName = url.split("""/""").lastOption

      val futureModels = h2oPredictorClient.list()
      val futureParameters = futureModels.map {
        case Left(_) => None
        case Right(models) =>
          modelName.flatMap(models.get).map { modelDescription =>
            formatParameters(modelDescription.parameters)
          }
      }

      Await.result(futureParameters, Duration(40, TimeUnit.SECONDS))

    } else {
      val url = s"${h2oMLPredictorBase.stripSuffix("/")}/${model.h2oIdentifier}"
      val modelName = url.split("""/""").lastOption

      val futureModels = h2oMLPredictorClient.list()
      val futureParameters = futureModels.map {
        case Left(_) => None
        case Right(models) =>
          modelName.flatMap(models.get).map { modelDescription =>
            formatParameters(modelDescription.parameters)
          }
      }

      Await.result(futureParameters, Duration(40, TimeUnit.SECONDS))

    }
  }

  def updateModelWithParameters(model: ModelWithoutUrlAndParams): ModelWithoutUrl = {
    val url = h2oPredictorBase.stripSuffix("/") + "/" + model.identifier
    val jsonParameters = parameters(url)
    jsonParameters match {
      case Some(p) => model.toModelWithoutUrl().copy(params = p)
      case None => throw new Exception("Invalid parameters")
    }
  }

  def updateModelWithParametersV2(model: ModelWithoutUrlAndParamsV2): ModelWithoutUrlV2 = {
    val jsonParameters = parametersV2(model)
    jsonParameters match {
      case Some(p) => model.toModelWithoutUrlV2().copy(params = p)
      case None => throw new Exception("Invalid parameters")
    }
  }
}
