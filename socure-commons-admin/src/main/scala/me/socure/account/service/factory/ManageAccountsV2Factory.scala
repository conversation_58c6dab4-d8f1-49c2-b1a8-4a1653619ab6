package me.socure.account.service.factory

import me.socure.account.client.{ManageAccountsV2Client, ManageAccountsV2ClientFactory}
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.Autowired

import scala.concurrent.ExecutionContext
class ManageAccountsV2Factory extends FactoryBean[ManageAccountsV2Client]{

  @Autowired
  var envUtil : EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: ManageAccountsV2Client = {
    val config = envUtil.getParsedEnvProperties.getConfig("account.service")
    ManageAccountsV2ClientFactory.create(config)(executionContext)
  }

  override def getObjectType: Class[ManageAccountsV2Client] = classOf[ManageAccountsV2Client]

  override def isSingleton: Boolean = true
}