package me.socure.util.model

import java.net.URL

import com.socure.common.ConfigExtensionsNoCrypt._
import com.socure.common.enrichments.RichAnything
import com.typesafe.config.ConfigFactory._
import com.typesafe.config.{ConfigParseOptions, ConfigSyntax}
import me.socure.common.clock.{Clock, RealClock}
import me.socure.common.model.config.QuantileMappingValidation
import me.socure.datasci.model.domain.Features.Feature
import me.socure.datasci.model.domain.ModelTypes.ModelType
import me.socure.datasci.model.domain._
import me.socure.json.controllerv1.bean.ModelWrapper
import me.socure.service.constants.CorrelationConfigKeys
import org.apache.commons.lang3.StringUtils._
import scalaz.Scalaz._
import scalaz.{Failure => SFailure, Success => SSuccess, _}

import scala.util.{Failure, Success, Try}

/**
  * Created by jamesan<PERSON> on 7/25/16.
  */
object ModelValidator {

  val publicId = "publicId"
  val engine = "engine"
  val apiVersion = "apiVersion"
  val modelFormat = "modelFormat"
  val scoreName = "scoreName"

  def validateName(name: Option[String]): ValidationNel[String, String] = {
    validateString(name, "name")
  }

  def validateUrl(url: Option[String]): ValidationNel[String, URL] = {
    validateString(url, "url").flatMap { url =>
      Try(new URL(url)) match {
        case Success(u) => u.successNel[String]
        case Failure(_) => "Invalid URL".failureNel[URL]
      }
    }
  }

  def validateIdentifier(identifier: Option[String]): ValidationNel[String, String] = {
    validateString(identifier, "identifier")
  }

  def validateVersion(version: Option[String]): ValidationNel[String, String] = {
    validateString(version, "version")
  }

  def validateParams(paramsOpt: Option[String]): ValidationNel[String, String] = {
    paramsOpt.orNull.successNel[String]
  }

  def validateFeature(featureInt: Integer): ValidationNel[String, Feature] = {
    Try(Features.byId(featureInt)) match {
      case Success(feature) => feature.successNel[String]
      case Failure(_) => s"Invalid feature with id : $featureInt".failureNel[Feature]
    }
  }

  def validateModelType(modelTypeStr: String): ValidationNel[String, ModelType] = {
    ModelTypes.byName(modelTypeStr) match {
      case Some(modelType) => modelType.successNel[String]
      case None => s"Invalid modelType with name : $modelTypeStr".failureNel[ModelType]
    }
  }

  def validateConfig(feature: Feature, configOpt: Option[String]): ValidationNel[String, String] = {
    feature match {
      case _ if feature.category == FeatureCategories.CORRELATION =>
        validateString(configOpt, "config").flatMap { config =>
          Try(parseString(config, ConfigParseOptions.defaults().setSyntax(ConfigSyntax.JSON))) match {
            case Success(c) =>
              val isValidPositiveThreshold = validateDouble(c.double(CorrelationConfigKeys.positiveThreshold), s"config.${CorrelationConfigKeys.positiveThreshold}")
              val isValidNegativeThreshold = validateDouble(c.double(CorrelationConfigKeys.negativeThreshold), s"config.${CorrelationConfigKeys.negativeThreshold}")
              (isValidPositiveThreshold |@| isValidNegativeThreshold) { (_, _) =>
                config
              }
            case Failure(_) => "Invalid config. Please provide a valid json config".failureNel[String]
          }
        }
      case _ => configOpt.orNull.successNel[String]
    }
  }

  def validateQuantileMapping(quantileMappingOpt: Option[String]): ValidationNel[String, Option[String]] = {
    quantileMappingOpt match {
      case Some(quantileMapping) if !quantileMapping.isEmpty =>
        validateString(Some(quantileMapping), "quantileMapping").flatMap { qm =>
          if (QuantileMappingValidation.validate(qm)) Some(qm).successNel[String] else {
            s"Invalid quantileMapping. Please provide a valid csv table with quantile and score".failureNel[Option[String]]
          }
        }
      case _ => None.successNel[String]
    }
  }

  def validate(model: ModelWrapper, clock: Clock = new RealClock): ValidationNel[String, ModelWithoutUrlAndParams] = {
    val featureVal = validateFeature(model.getFeature)
    val modelTypeVal = validateModelType(model.getModelType)

    featureVal.flatMap { feature =>
        modelTypeVal.flatMap { modelType =>
          val now = clock.now()
          (validateName(model.getName.opt)
            |@| validateIdentifier(model.getIdentifier.opt)
            |@| validateVersion(model.getVersion.opt)
            |@| validateConfig(feature, model.getConfig.opt)
            |@| validateQuantileMapping(model.getQuantileMapping.opt)) { (name, identifier, version, config, quantileMapping) =>
            ModelWithoutUrlAndParams(
              id = Option(model.getId).map(Long2long).getOrElse(-1L),
              feature = feature,
              name = Some(name),
              identifier = identifier,
              version = Some(version),
              config = config.opt,
              createdTime = now,
              lastUpdatedTime = now,
              modelType = modelType,
              quantileMapping = quantileMapping
            )
          }
        }
    }
  }

  def validateV2(model: ModelWrapper, clock: Clock = new RealClock): ValidationNel[String, ModelWithoutUrlAndParamsV2] = {
    val featureVal = validateFeature(model.getFeature)
    val modelTypeVal = validateModelType(model.getModelType)

    featureVal.flatMap { feature =>
      modelTypeVal.flatMap { modelType =>
        val now = clock.now()
        (validateString(model.getPublicId.opt, publicId)
          |@| validateName(model.getName.opt)
          |@| validateString(model.getScoreName.opt, scoreName)
          |@| validateVersion(model.getVersion.opt)
          |@| validateVersion(model.getRealVersion.opt)
          |@| validateIdentifier(model.getIdentifier.opt)
          |@| validateString(model.getEngine.opt, engine)
          |@| validateString(model.getModelFormat.opt, modelFormat)
          |@| validateString(model.getApiVersion.opt, apiVersion)
          |@| validateConfig(feature, model.getConfig.opt)
          |@| validateQuantileMapping(model.getQuantileMapping.opt)) { (publicId, name, scoreName, version,realVersion, identifier, engine, modelFormat, apiVersion, config,  quantileMapping) =>
          ModelWithoutUrlAndParamsV2(
            id = Option(model.getId).map(Long2long).getOrElse(-1L),
            publicId = publicId,
            name = name,
            scoreName = scoreName,
            scoreVersion = version,
            realVersion = Some(realVersion),
            h2oIdentifier = identifier,
            engine = engine,
            h2oModelFormat = modelFormat,
            apiVersion = apiVersion,
            feature = feature,
            config = config.opt,
            quantileMapping = quantileMapping
          )
        }
      }
    }
  }

  def validateEither(model: ModelWrapper, clock: Clock = new RealClock): Either[String, ModelWithoutUrlAndParams] = {
    validationToEither(validate(model, clock))
  }

  def validateEitherV2(model: ModelWrapper, clock: Clock = new RealClock): Either[String, ModelWithoutUrlAndParamsV2] = {
    validationToEitherV2(validateV2(model, clock))
  }

  def validationToEither(validation: ValidationNel[String, ModelWithoutUrlAndParams]): Either[String, ModelWithoutUrlAndParams] = {
    validation match {
      case SSuccess(model) => Right(model)
      case SFailure(error) => Left(String.valueOf(error))
    }
  }

  def validationToEitherV2(validation: ValidationNel[String, ModelWithoutUrlAndParamsV2]): Either[String, ModelWithoutUrlAndParamsV2] = {
    validation match {
      case SSuccess(model) => Right(model)
      case SFailure(error) => Left(String.valueOf(error))
    }
  }

  def validateString(value: Option[String], field: String): ValidationNel[String, String] = {
    value match {
      case None => s"$field cannot be NULL".failureNel[String]
      case Some(s) if isBlank(s) => s"$field cannot be empty".failureNel[String]
      case Some(v) => v.trim.successNel[String]
    }
  }

  private def validateDouble(value: Option[Double], field: String): ValidationNel[String, Double] = {
    value match {
      case None => s"$field cannot be NULL".failureNel[Double]
      case Some(d) if d.isNaN => s"$field is not a number".failureNel[Double]
      case Some(d) if d.isInfinity => s"$field is Infinity".failureNel[Double]
      case Some(v) => v.successNel[String]
    }
  }
}
