package com.socure.dataplugin.caching
// Created by idan on 7/23/15.

import scala.concurrent.{ Future, ExecutionContext }

trait NoopCaching[K, V] extends Caching[K, V] {
  override def cache: Cache[K, V] = NoopCache.fail[K, V]
}

object NoopCache {
  def fail[K, V] = new NoopFailCache[K, V]
  def succeed[K, V](v: V) = new NoopSuccessCache[K, V](v)
}

class NoopFailCache[K, V] extends Cache[K, V] {
  override def get(key: K)(implicit exe: ExecutionContext = null): Future[Option[V]] = Future.successful(None)
  override def put(k: K, v: V)(implicit exe: ExecutionContext = null): Future[Boolean] = Future.successful(true)
}

class NoopSuccessCache[K, V](v: V) extends Cache[K, V] {
  override def get(key: K)(implicit exe: ExecutionContext): Future[Option[V]] = Future.successful(Some(v))
  override def put(k: K, v: V)(implicit exe: ExecutionContext): Future[Boolean] = Future.successful(true)
}
