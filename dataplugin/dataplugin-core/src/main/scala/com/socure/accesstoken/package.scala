package com.socure

import com.socure.accesstoken.Network.Network

// Created by <PERSON><PERSON><PERSON><PERSON> on 17/11/15.

package object accesstoken {

  case class AccessTokenCredentials(network: Option[Network] = None, clientId: Option[String] = None, clientSecret: Option[String] = None, accessToken: Option[String] = None, accessTokenSecret: Option[String] = None, apiKey: Option[String] = None, expired: Option[Boolean] = None) {
    def equalsCred(c: AccessTokenCredentials) = clientId == c.clientId &&
      clientSecret == c.clientSecret &&
      accessToken == c.accessToken &&
      accessTokenSecret == c.accessTokenSecret &&
      apiKey == c.apiKey
  }

  case class AccProfile(accessToken: Option[String] = None, refreshToken: Option[String] = None, accessTokenSecret: Option[String] = None, accessTokenExpired: Option[Boolean] = None, refreshTokenExpired: Option[Boolean] = None)

  object Network extends Enumeration {
    type Network = Value

    val FACEBOOK = Value(1, "fb")
    val TWITTER = Value(2, "tw")
    val LINKEDIN = Value(3, "in")
    val GOOGLE = Value(4, "gp")
  }
}
