# dataplugin
Pluggable framework for data sources

## Setup
This repo uses [SBT](http://www.scala-sbt.org). The easiest way to get everything in the world working is the following:
```brew install sbt```

(in root dir of repo) just run **sbt** then you can

```sbt> update```

It will then take slightly less than forever to download all the dependencies. Once it's done you can do awesome stuff like
```sbt
sbt> compile
sbt> test
sbt> console (which puts you in a Scala REPL with access to all of the projects classpath and dependencies)
sbt> package
```
You can specify a project for any of the above tasks like this:
```
sbt> [project-name]/test
```

and example:
```
sbt> domain/test
```


Anyone developing in this repo, please install git-hooks. It is a handy-dandy tool for managing git hooks. 
Currently we have only one hook, `pre-commit/format`, but it's important everyone uses it in order to avoid polluting git history with format related changes. To install, merely:
```
brew install git-hooks
```

Really, that's it.

## Structure
There are 5 sbt projects:
- **common**:  package[ com.socure.common ] 
 - This is potentially useful in any project.
- **domain**:  package[ com.socure.dataplugin.domain ] 
 - Comprises all domain specific structures. Separated so it can be a singular dependency of clients. 
- **database**: package[ com.socure.databse ]
 - DAOs
- **core**:    package[ com.socure.dataplugin.core ] 
 - Comprises the basic functionality of the dataplugin repo
- **plugins**: package[ com.socure.dataplugin.<some datasource> ] 
 - Comprises functionality for a specific data source, e.g. pipl.
- **server**: package[ com.socure.dataplugin ]
 - Functionality for the server as a whole including, but not limited to: booting, health checks, plugin management, etc...

where the dependency chain is as listed above, or
> common <~ domain <~ database <~ core <~ plugins <~ server

There is an argument to be made that the last three could be merged, but the thought was to enforce the dependency chain in the hopes of maintaining clarity.

## Tour
To start at the Start, check out `com.socure.akka.Main`

The primary element is the `dataplugin.ExternalDataSource` which defines the basis for all the plugins. There is also the Communicator, but 
it is subclassed by DispatchCommunicator which is sufficient to handle any basic HTTP APIs I anticipate its usage in most DataSource 
implementations.

To see an example implementation take a look at `dataplugin.pipl.PiplDataSource`, however, please note that it does have some extra functionality that is not shared among all plugins as Pipl involves *Entity Resolution Lite™*.

An interesting section is `dataplugin.metrics` which implements the aforementioned *ER Lite*. It is easier to understand if I point out that it uses chained implicits to automatically<sup>\*</sup> generate complex Metrics from simpler metrics. For example, generating a `Metric[Sequence[T]]` given a `Metric[T]`.

One major point that is and may for some time be in flux is what the request/response type will be. There currently exists `SocureRequest` which is subtyped by datasource. The response type is currently a `Scoreable` which is just a container for an `Identity` and `ReasonCodes`. As the code integrates further this should stabilize.


<sup>*</sup> There is a point where it's not so automatic and it's driving me insane so if you can figure it out, please let me know.

## Implementing a Data Plugin
Let's look at basic example. Let's say you want to produce ExampleDataSource. Let's further pretend we have the following relevant types:
```scala
trait ExampleRequest extends SocureRequest
trait ExampleResponse
trait CommRequest
trait CommResponse
```
If you then go and create a stub:
```scala
val exampleDS = new ExternalDataSource[ExampleRequest, ExampleResponse, CommRequest, CommResponse]
```
you will see that it must be abstract. If you then have your fancy IDE produce stubs for all the items that need to be implemented you will see this huge list (without the comments that I'm adding here):
```scala
  // To Be Implemented by individual data sources ///////////////////
  override def validate(r: SocureRequest): Op[ExampleRequest] = ???
  override def marshal: (ExampleRequest) ⇒ CommRequest = ???
  override def postProcess: (ExampleRequest, ExampleResponse) ⇒ Future[Scoreable] = ???
  override def unmarshal: (TxId) ⇒ (CommResponse) ⇒ Future[ExampleResponse] = ???
  
  // From Communicator
  override def simpleSend(r: CommRequest): Future[CommResponse] = ???
  
  // From HasServiceId
  override def serviceId: ServiceId = ???

  // From ExeContext
  override implicit def exe: ExecutionContext = ???
  
  // From Caching
  override def cache: Cache[ExampleRequest, ExampleResponse] = ???
  
  // From CommunicationsAuditing
  override def auditSoftFailure(meta: MetaData, req: CommRequest, ex: HttpException): Unit = ???
  override def auditSuccess(meta: MetaData, req: CommRequest, res: CommResponse): Unit = ???
  override def auditRequest(meta: MetaData, req: CommRequest): Unit = ???
  override def auditHardFailure(meta: MetaData, req: CommRequest, t: Throwable): Unit = ???
```
Of course, this all comes from the definitions:
```scala
trait ExternalDataSource[REQ <: SocureRequest, RES, COMM_REQ, COMM_RES]
    extends Communicator[COMM_REQ, COMM_RES]
    with Caching[REQ, RES]
    with ExeContext
    with HasServiceId
    
trait Communicator[COMM_REQ, COMM_RES]
  extends CommunicationsAuditing[COMM_REQ, COMM_RES]
  with ExeContext with HasServiceId
```
Ultimately you are left with the following items to implement:
```scala
// These support caching
implicit val serializeKey: ExampleRequest ⇒ String = ???
implicit val responseCodec: Codec[ExampleResponse] = ???

// These are trivial
override def serviceId: ServiceId = ???
override implicit def exe: ExecutionContext = ???

// This is the real work
override def validate(r: SocureRequest): Op[ExampleRequest] = ???
override def marshal: (ExampleRequest) ⇒ Req = ???
override def unmarshal: (TxId) ⇒ (Response) ⇒ Future[ExampleResponse] = ???
override def postProcess: (ExampleRequest, ExampleResponse) ⇒ Future[Scoreable] = ???
```
Of the final four, validate is only as complex as you want to make it and postProcessing may be non-existent. PostProcessing was created just to support Pipl partial squishing. In fact, it would probably make more sense tp move it out of the ExternalDataSource class now that I think about it.
That's it!

## Scaladoc
run
> sbt> unidoc

and open the file `<git-repo>/target/scala-2.11/unidoc/index.html`. I recommend a bookmark, obvs. 

Currently, the code is conspicuously devoid of scaladoc commentary. Should probably change that...

## Maven

### Updating version

To update maven version use the **versions** plugin like below on the parent pom.

```bash
$ mvn versions:set -DnewVersion=0.2.4-SNAPSHOT
```