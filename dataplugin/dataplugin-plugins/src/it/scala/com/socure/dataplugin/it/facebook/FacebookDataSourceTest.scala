package com.socure.dataplugin.it.facebook

import java.net.URL

import com.socure.common.enrichments._
import com.socure.common.errors._
import com.socure.common.struct._
import com.socure.dataplugin.facebook.{FacebookRequest, _}
import com.socure.dataplugin.it.support.{MockSupport, ScoringTestSupport}
import com.socure.domain.socure.Sources.FBANL
import com.socure.domain.socure.dob._
import com.socure.domain.socure.postal._
import com.socure.domain.socure.scoring.RuleCodes._
import com.socure.domain.socure.scoring._
import com.socure.domain.socure.{request, _}
import org.joda.time.{LocalDate, Partial}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FlatSpec, Matchers}

import scala.collection.immutable.{Seq => iSeq}
import scala.concurrent.{ExecutionContext, Future}
import scalaz.syntax.std.option._


/**
 * Created by jam<PERSON><PERSON> on 10/9/15.
 */
class FacebookDataSourceTest extends FlatSpec with Matchers with ScalaFutures with MockFactory with MockSupport with ScoringTestSupport {

  implicit val defaultPatience = PatienceConfig(timeout = Span(10, Seconds), interval = Span(50, Millis))

  import com.socure.dataplugin.it.fixtures.TestFacebookServer._

  import ExecutionContext.Implicits.global

  val req = FacebookRequest("me", "AT", new request.MetaData("dev", Apis.EmailAuthScore.V2, "whatever", TxId.gen, null))
  implicit val exe = implicitly[ExecutionContext]

  "facebook user profile data source" should "properly parse my profile" in {
    mockFacebookUserProfile.communicator.mockDispatchResponse = stubUserProfile
    whenReady(mockFacebookUserProfile.process(req)) { res ⇒
      res.id should be("100002829424957")
    }
  }

  "facebook user profile data source" should "throw error with invalid access token and should parse error properly" in {
    checkFBError(facebookUserProfile.process(req).failed)
  }

  "facebook feed data source" should "properly parse feed information" in {
    mockFacebookFeed.communicator.mockDispatchResponse = stubFeed
    whenReady(mockFacebookFeed.process(req)) { res ⇒
      res.data should not be empty
      res.data.get.size should be(3)
    }
  }

  "facebook feed data source" should "throw error with invalid access token and should parse error properly" in {
    checkFBError(facebookFeed.process(req).failed)
  }

  "facebook friends data source" should "properly parse friends information" in {
    mockFacebookFriends.communicator.mockDispatchResponse = stubFriends
    whenReady(mockFacebookFriends.process(req)) { res ⇒
      res.data should not be empty
      res.data.get.size should be(5)
    }
  }

  "facebook friends data source" should "throw error with invalid access token and should parse error properly" in {
    checkFBError(facebookFriends.process(req).failed)
  }

  "facebook uploaded photos data source" should "properly parse uploaded photos information" in {
    mockFacebookUploadedPhotos.communicator.mockDispatchResponse = stubPhotosUploaded
    whenReady(mockFacebookUploadedPhotos.process(req)) { res ⇒
      res.data should not be empty
      res.data.get.size should be(6)
    }
  }

  "facebook uploaded photos data source" should "throw error with invalid access token and should parse error properly" in {
    checkFBError(facebookUploadedPhotos.process(req).failed)
  }

  ignore should "properly parse my profile" in {
    mockFacebookUserProfile.communicator.mockDispatchResponse = stubUserProfile
    mockFacebookFeed.communicator.mockDispatchResponse = stubFeed
    mockFacebookFriends.communicator.mockDispatchResponse = stubFriends
    mockFacebookUploadedPhotos.communicator.mockDispatchResponse = stubPhotosUploaded

    whenReady(mockFacebook.process(req)) { res ⇒
      res.identityOpt should not be empty
      res.identityOpt.get.email should be(iSeq(Email.optFromStr("<EMAIL>").get))
      checkScore(res.rules, FBVAL_PERFECT_MATCH_COUNT, 1.0.some)
      checkScore(res.rules, FBANL_NO_POST, 3.0.some)
      checkScore(res.rules, FBANL_NO_FRIENDS, 5.0.some)
      checkScore(res.rules, FBANL_NO_PHOTOS, 6.0.some)
    }
  }

  "facebook" should "throw error with invalid access token and should parse error properly" in {
    whenReady(facebook.process(req)) { res ⇒
      res.identityOpt should be(empty)
      checkScore(res.rules, FBVAL_INTERNAL_ERROR, 1.0.some)
    }
  }

  "facebook" should "convert to identity properly" in {
    val user = getUser("/fb/identity.json")
    user.isSuccess should be(right = true)
    val identity = user.get.toIdentity

    {
      import identity._
      //Name parsing
      name should be(iSeq(Name("James", "Anto")))
      gender should be(iSeq(Genders.Male))
      dob should be(iSeq(BirthDate(new LocalDate(1991, 4, 26))))
      email should be(iSeq(Email.optFromStr("<EMAIL>").get))
      addresses should be(iSeq(RawPostalAddress(
        line1 = "Add My Street".some,
        city = "Add London".some,
        state = "Add State".some,
        country = "United Kingdom".some,
        zip = "98765".some).parse.copy(coordinates = GeographicCoordinates(Latitude(51.5072), Longitude(-0.1275)).some),
        RawPostalAddress(
        line1 = "HT My Street1".some,
        line2 = "HT Apt 1111".some,
        city = "HT London".some,
        state = "HT NY".some,
        country = "United Kingdom".some,
        zip = "54321".some).parse,
        RawPostalAddress(
          line1 = "HT My Street".some,
          city = "HT Villupuram".some,
          state = "HT State".some,
          country = "India".some,
          zip = "56789".some).parse.copy(coordinates = GeographicCoordinates(Latitude(11.9333), Longitude(79.4833)).some),
        RawPostalAddress(
          line1 = "Loc My Street1".some,
          line2 = "Loc Apt 1111".some,
          city = "Loc London".some,
          state = "Loc NY".some,
          country = "United Kingdom".some,
          zip = "12345".some).parse,
        RawPostalAddress(
          line1 = "Loc My Street".some,
          city = "Loc London".some,
          state = "Loc State".some,
          country = "United Kingdom".some,
          zip = "98765".some).parse.copy(coordinates = GeographicCoordinates(Latitude(51.5072), Longitude(-0.1275)).some)))
      companies should be(iSeq("Aequalis Software Solutions Pvt ltd", "Bosco ITS"))
      images should be(iSeq(new URL("https://fbcdn-profile-a.akamaihd.net/hprofile-ak-xpt1/v/t1.0-1/p50x50/10945667_640203229417319_574751314704351103_n.jpg?oh=f1b2bff5cc2464f6d2dca0401ce9ce81&oe=5685D572&__gda__=1456139055_8e5d17d4db0c156c6224f1d07c50b8d4")))
      userNames should be(iSeq("javajamesb08"))
      userIds should be(iSeq(ProfileId(ProfileProviders.Facebook, "100002829424957")))
      coordinates should be(iSeq(GeographicCoordinates(Latitude(51.5072), Longitude(-0.1275)), GeographicCoordinates(Latitude(11.9333), Longitude(79.4833)), GeographicCoordinates(Latitude(51.5072), Longitude(-0.1275))))
      urls should be(iSeq(new URL("https://www.facebook.com/javajamesb08"), new URL("https://www.facebook.com/100002829424957")))
      education should be(iSeq(
        Education("High School", None, None, "https://www.facebook.com/110392995647643".some),
        Education("High School", None, 2008.some, "https://www.facebook.com/617877738229226".some),
        Education("College", None, 2011.some, "https://www.facebook.com/136839306342875".some)))
      employment should be(iSeq(
        Employment(
          organization = "Aequalis Software Solutions Pvt ltd".some,
          organizationUrl = new URL("https://www.facebook.com/1472759483026406").some,
          title = "Software Engineer".some,
          description = "I love my job!!!".some,
          location = "Chennai, India".some,
          start = new Partial(new LocalDate(2015, 1, 1)).some
        ),
        Employment(
          organization = "Bosco ITS".some,
          organizationUrl = new URL("https://www.facebook.com/332657020203621").some,
          title = "Software Engineer".some,
          location = "Vellore".some,
          start = new Partial(new LocalDate(2011, 11, 2)).some,
          end = new Partial(new LocalDate(2014, 12, 31)).some)
      ))
    }
  }

  //Basic Rules Test

  "facebook" should "fire 200127" in {
    checkRule("/fb/rules/200127.json", FBVAL_PERFECT_MATCH_COUNT)
  }

  ignore should "fire 300001" in {
    checkRule("/fb/rules/300001_pos.json", FBANL_ACC_AGE_MONTHS, 49.0.some)
  }

  "facebook" should "not fire 300001" in {
    checkRule("/fb/rules/300001_neg.json", FBANL_ACC_AGE_MONTHS, None)
  }

  "facebook" should "fire 300002" in {
    checkRule("/fb/rules/300002_pos.json", FBANL_NO_FRIENDS, 665.0.some)
  }

  "facebook" should "fire 300003" in {
    checkRule("/fb/rules/300003_pos.json", FBANL_NO_POST, 100.0.some)
  }

  "facebook" should "fire 300005" in {
    checkRule("/fb/rules/300005_pos.json", FBANL_NO_PESONAL_POST, 2.0.some)
  }

  "facebook" should "fire 300006" in {
    checkRule("/fb/rules/300006_pos.json", FBANL_NO_PERSONAL_COMMENTS)
  }

  "facebook" should "fire 300007" in {
    checkRule("/fb/rules/300007_pos.json", FBANL_AVE_POST_PER_DAY, 8.0.some)
  }

  "facebook" should "fire 300008" in {
    checkRule("/fb/rules/300008_pos.json", FBANL_STD_DEVIATION_NO_POST, 9.56.some)
  }

  "facebook" should "fire 300009" in {
    checkRule("/fb/rules/300009_pos.json", FBANL_NO_USER_LIKE, 2.0.some)
  }

  "facebook" should "fire 300010" in {
    checkRule("/fb/rules/300010_pos.json", FBANL_AVG_DAYS_USER_LIKE, 5.98.some)
  }

  "facebook" should "fire 300011" in {
    checkRule("/fb/rules/300011_pos.json", FBANL_STD_DEVIATION, 80.65.some)
  }

  "facebook" should "fire 300013 and it should be true" in {
    checkRule("/fb/rules/300013_true.json", FBANL_IS_VERIFIED)
  }

  "facebook" should "fire 300013 and it should be false" in {
    checkRule("/fb/rules/300013_false.json", FBANL_IS_VERIFIED, 0.0.some)
  }

  "facebook" should "fire 300014" in {
    checkRule("/fb/rules/300014_pos.json", FBANL_EDUCATE_SAME, 2.0.some)
  }

  "facebook" should "fire 300015" in {
    checkRule("/fb/rules/300015_pos.json", FBANL_WORK_SAME, 4.0.some)
  }

  "facebook" should "fire 300016" in {
    checkRule("/fb/rules/300016_pos.json", FBANL_LIVES_SAME, 3.0.some)
  }

  "facebook" should "fire 300017" in {
    checkRule("/fb/rules/300017_pos.json", FBANL_HOME_TOWN_SAME, 3.0.some)
  }

  "facebook" should "fire 300020" in {
    checkRule("/fb/rules/300020_pos.json", FBANL_NO_PHOTOS, 6.0.some)
  }

  "facebook" should "fire 300021" in {
    checkRule("/fb/rules/300021_pos.json", FBANL_PHOTO_TAG, 3.0.some)
  }

  "facebook" should "fire 300022" in {
    checkRule("/fb/rules/300022_pos.json", FBANL_COVER_PHOTOS)
  }

  "facebook" should "fire 300023 with score 1.0 for female" in {
    checkRule("/fb/rules/300023_female.json", FBANL_GENDER)
  }

  "facebook" should "fire 300023 with score 0.0 for male" in {
    checkRule("/fb/rules/300023_male.json", FBANL_GENDER, 0.0.some)
  }

  "facebook" should "fire 300024" in {
    checkRule("/fb/rules/300024_pos.json", FBANL_MALE_FRI_COUNT, 2.0.some)
  }

  "facebook" should "fire 200013" in {
    checkRule("/fb/rules/200013_pos.json", FBANL_NAME_MATCH)
  }

  "facebook" should "fire 100016" in {
    checkRule("/fb/rules/100016_pos.json", FBANL_NAME_DOES_NOT_MATCH)
  }

  //Inferred Rules Test

  "facebook" should "fire 100001" in {
    checkIRule("/fb/rules/100001_pos.csv", FBANL_INSUFFICIENT_ACC)
  }

  "facebook" should "not fire 100001" in {
    checkIRule("/fb/rules/100001_neg.csv", FBANL_INSUFFICIENT_ACC, None)
  }

  "facebook" should "fire 200002" in {
    checkIRule("/fb/rules/200002_pos.csv", FBANL_VERIFIED_POSITIVE)
  }

  "facebook" should "fire 200003" in {
    checkIRule("/fb/rules/200003_pos.csv", FBANL_EDUCATE_SAME_POSITIVE)
  }

  "facebook" should "fire 100003" in {
    checkIRule("/fb/rules/100003_pos.csv", FBANL_EDUCATE_SAME_NEGATIVE)
  }

  "facebook" should "fire 200004" in {
    checkIRule("/fb/rules/200004_pos.csv", FBANL_WORK_SAME_POSITIVE)
  }

  "facebook" should "fire 100004" in {
    checkIRule("/fb/rules/100004_pos.csv", FBANL_WORK_SAME_NEGATIVE)
  }

  "facebook" should "fire 200005" in {
    checkIRule("/fb/rules/200005_pos.csv", FBANL_LIVES_SAME_POSITIVE)
  }

  "facebook" should "fire 100005" in {
    checkIRule("/fb/rules/100005_pos.csv", FBANL_LIVES_SAME_NEGATIVE)
  }

  "facebook" should "fire 200006" in {
    checkIRule("/fb/rules/200006_pos.csv", FBANL_HOME_TOWN_SAME_POSITIVE)
  }

  "facebook" should "fire 100006" in {
    checkIRule("/fb/rules/100006_pos.csv", FBANL_HOME_TOWN_SAME_NEGATIVE)
  }

  "facebook" should "fire 200011" in {
    checkIRule("/fb/rules/200011_pos.csv", FBANL_HAS_COVER_PHOTO_TRUE)
  }

  "facebook" should "fire 200014" in {
    checkIRule("/fb/rules/200014_pos.csv", FBANL_FRIEND_SHARE_GREATER)
  }

  "facebook" should "fire 100014" in {
    checkIRule("/fb/rules/100014_pos.csv", FBANL_FRIEND_SHARE_LESS)
  }

  "facebook" should "fire 200019" in {
    checkIRule("/fb/rules/200019_pos.csv", FBANL_HAS_ONE_CLOSE_FRIEND)
  }

  "facebook" should "fire 100007" in {
    checkIRule("/fb/rules/100007_pos.csv", FBANL_NOT_ENOUGH_POST_BDAY)
  }

  "facebook" should "fire 100008" in {
    checkIRule("/fb/rules/100008_pos.csv", FBANL_INSUFFICIENT_TAG_PROFILE)
  }

  "facebook" should "fire 100010" in {
    checkIRule("/fb/rules/100010_pos.csv", FBANL_TO_MANY_TAG_PHOTO)
  }

  "facebook" should "fire 100012" in {
    checkIRule("/fb/rules/100012_pos.csv", FBANL_POST_FRQ_GREATER_8)
  }

  "facebook" should "fire 100013 when female friends is less than 10%" in {
    checkIRule("/fb/rules/100013_female_10.csv", FBANL_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS)
  }

  "facebook" should "fire 100013 when female friends is greater than 75%" in {
    checkIRule("/fb/rules/100013_female_75.csv", FBANL_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS)
  }

  "facebook" should "fire 100013 when male friends is less than 25%" in {
    checkIRule("/fb/rules/100013_male_25.csv", FBANL_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS)
  }

  "facebook" should "fire 100013 when male friends is less than 90%" in {
    checkIRule("/fb/rules/100013_male_90.csv", FBANL_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS)
  }

  "facebook" should "fire 100015" in {
    checkIRule("/fb/rules/100015_pos.csv", FBANL_AGE_GREATER)
  }

  "facebook" should "fire 100018 when no personal posts" in {
    checkIRule("/fb/rules/100018_post.csv", FBANL_TIMELINE_POST)
  }

  "facebook" should "fire 100018 when no personal comments" in {
    checkIRule("/fb/rules/100018_comments.csv", FBANL_TIMELINE_POST)
  }

  "facebook" should "fire 100020" in {
    checkIRule2("/fb/rules/100020_pos.csv", FBANL_NEGATIVE_COUNT_LESSER)
  }

  "facebook" should "fire 200007" in {
    checkIRule("/fb/rules/200007_pos.csv", FBANL_ENOUGH_POST_BDAY)
  }

  "facebook" should "fire 200008" in {
    checkIRule("/fb/rules/200008_pos.csv", FBANL_SUFFICIANT_TAG_PROFILE)
  }

  "facebook" should "fire 200009" in {
    checkIRule("/fb/rules/200009_pos.csv", FBANL_AVE_TAG_PHOTO)
  }

  "facebook" should "fire 200010" in {
    checkIRule("/fb/rules/200010_pos.csv", FBANL_POST_FRQ_LESS_8)
  }

  "facebook" should "fire 200020" in {
    checkIRule2("/fb/rules/200020_pos.csv", FBANL_POSSIVE_COUNT_GREATER)
  }

  //Utility methods

  def getUser(jsonFilePath: String) = cpRes(jsonFilePath).mkString.asUser

  def parseRulesFromCSV(csvPath: String) = cpRes(csvPath).getLines().filter(_.hasVal).map(_.split(",")).flatMap(arr ⇒ RuleCodes.bySourceCode((FBANL, arr(0).trim.toInt)).map((_, arr(1).trim.toDouble.toScore)).toList).toMap

  def checkRule(jsonFilePath: String, ruleCode: RuleCode, score: Option[Double] = 1.0.some) = {
    val user = getUser(jsonFilePath)
    user.isSuccess should be(right = true)
    val ruleCodes = user.get.extractBasicRules
    checkScore(ruleCodes, ruleCode, score)
  }

  def checkIRule(csvPath: String, ruleCode: RuleCode, score: Option[Double] = 1.0.some) = {
    //Checking inferred rule
    val basicRules = parseRulesFromCSV(csvPath)
    val rules = extractInferredRulesV1(basicRules)
    checkScore(rules, ruleCode, score)
  }

  def checkIRule2(csvPath: String, ruleCode: RuleCode, score: Option[Double] = 1.0.some) = {
    //Checking inferred rule
    val inferredRulesV1 = parseRulesFromCSV(csvPath)
    val rules = extractInferredRulesV2(inferredRulesV1)
    checkScore(rules, ruleCode, score)
  }

  def checkFBError(f: Future[Throwable]) = whenReady(f) {
    case SocureExceptionWithData(m, d) ⇒
      d shouldBe an[FBError]
      val fbError = d.asInstanceOf[FBError]
      fbError.error should be(defined)
      fbError.error.get.code should be(defined)
    case _ =>
  }

  def stubUserProfile = stubResponse("/fb/profile.json")

  def stubFeed = stubResponse("/fb/feed.json")

  def stubFriends = stubResponse("/fb/friends.json")

  def stubPhotosUploaded = stubResponse("/fb/photos_uploaded.json")

}
