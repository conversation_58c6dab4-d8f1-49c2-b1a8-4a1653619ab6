package com.socure.dataplugin.infutor.phoneowneridvalidation

import org.scalatest.{FunSuite, Matchers}

import scala.io.Source

class InfutorPhoneOwnerIDValidationResponseParserTest extends FunSuite with Matchers {

  val parser = new InfutorPhoneOwnerIDValidationResponseParser

  test("should parse errors correctly") {

    val text = Source.fromInputStream(getClass.getResourceAsStream("/infutor/infutor-error.xml")).mkString
    val actual = parser.parse(text)
    actual shouldBe 'left
  }

  test("should parse success correctly") {

    val text = Source.fromInputStream(getClass.getResourceAsStream("/infutor/infutor-phone-owner-id-validation-success.xml")).mkString
    val actual = parser.parse(text)
    actual shouldBe 'right
    val person = actual.right.get.person
    person.name.get.firstName should contain("A_FIRSTNAME")
    person.name.get.lastName should contain("A_LASTNAME")
    person.name.get.middleName shouldBe empty
    person.name.get.businessName shouldBe empty
    person.name.get.cNam should contain("A_CNAM")

    person.address.get.house should contain("A_HOUSE")
    person.address.get.preDir should contain("A_PREDIR")
    person.address.get.street should contain("A_STREET")
    person.address.get.streetType should contain("A_STREET_TYPE")
    person.address.get.postDir should contain("A_POSTDIR")
    person.address.get.city should contain("A_CITY")
    person.address.get.state should contain("A_STATE")
    person.address.get.zip should contain("A_ZIP")
    person.address.get.z4 should contain("A_Z4")
    person.address.get.deliverable should contain(true)

    person.phone.phone should contain("5555555555")
    person.phone.phoneType should contain("W")
    person.phone.recType should contain("R")
    person.phone.phv should contain("2")
    person.phone.telCoName should contain("OMNIPOINT COMM NY")

    person.categoryPhoneMatch should contain("N")
    person.score should contain(900)
    person.category should contain("P")
    person.mcd should contain("900P")
    person.prePaid should contain("Y")
  }
}
