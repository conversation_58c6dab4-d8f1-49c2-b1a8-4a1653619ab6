package com.socure.dataplugin.pipl

import com.pipl.api.data.containers.Person
import com.pipl.api.data.fields.Job
import com.socure.domain.socure.scoring.{RuleCodes, Score}
import org.joda.time.LocalDate
import com.socure.dataplugin.pipl.JavaDateToJodaLocalDate._
import scala.collection.JavaConversions._

/**
  * Created by alexand<PERSON> on 7/24/16.
  */
class PiplPremiumJobRuleCodeProducer(ageCalculator: AgeCalculator) {

  private def getOldestJob(jobs: Set[Job]) = {
    val date = jobs.minBy(_.getValidSince).getValidSince
    ageCalculator.days(javaDateToJodaLocalDate(date))
  }

  private def getNewestJob(jobs: Set[Job]) = {
    val date = jobs.maxBy(_.getValidSince).getValidSince
    ageCalculator.days(javaDateToJodaLocalDate(date))
  }

  def get(persons: Seq[Person]) = {
    val allJobs = persons.flatMap(_.getJobs).toSet

    if (allJobs.nonEmpty) {
      Map(
        RuleCodes.PP_AGE_OF_OLDEST_JOB -> Score(getOldestJob(allJobs)),
        RuleCodes.PP_AGE_OF_NEWEST_JOB -> Score(getNewestJob(allJobs))
      )
    } else {
      Map.empty
    }
  }
}
