package me.socure.service.audit.bean;

import me.socure.service.constants.IConstants.APIName;

import java.io.Serializable;

public class LBAggregator implements Serializable{
	private static final long serialVersionUID = -6753364918125350240L;
	private Long accountId;
	private APIName apiName;
	private Long count;
	private Long kyc;
	private Long error;
	
	public LBAggregator() {
	}

	public LBAggregator(Object accountId, Object apiName, Object count) {
		this.accountId = (accountId != null && accountId instanceof Long ? (Long) accountId : accountId != null ? Long.parseLong(accountId.toString()) : 0L);
		this.apiName = (apiName != null ? APIName.getByCode(Integer.valueOf(apiName.toString())) : APIName.ALL);
		this.count = (count != null && count instanceof Long ? (Long) count : count != null ? Long.parseLong(count.toString()) : 0L);
	}
	
	public LBAggregator(Object accountId, Object apiName, Object count, Object kyc) {
		this.accountId = (accountId != null && accountId instanceof Long ? (Long) accountId : accountId != null ? Long.parseLong(accountId.toString()) : 0L);
		this.apiName = (apiName != null ? APIName.getByCode(Integer.valueOf(apiName.toString())) : APIName.ALL);
		this.count = (count != null && count instanceof Long ? (Long) count : count != null ? Long.parseLong(count.toString()) : 0L);
		this.kyc = (count != null && count instanceof Long ? (Long) kyc : kyc != null ? Long.parseLong(kyc.toString()) : 0L);
	}

	public LBAggregator(Object accountId, Object apiName, Object count, Object kyc, Object error) {
		this.accountId = (accountId != null && accountId instanceof Long ? (Long) accountId : accountId != null ? Long.parseLong(accountId.toString()) : 0L);
		this.apiName = (apiName != null ? APIName.getByCode(Integer.valueOf(apiName.toString())) : APIName.ALL);
		this.count = (count != null && count instanceof Long ? (Long) count : count != null ? Long.parseLong(count.toString()) : 0L);
		this.kyc = (kyc != null && kyc instanceof Long ? (Long) kyc : kyc != null ? Long.parseLong(kyc.toString()) : 0L);
		this.error = (error != null && error instanceof Long ? (Long) error : error != null ? Long.parseLong(error.toString()) : 0L);
	}

	public Long getAccountId() {
		return accountId;
	}

	public void setAccountId(Long accountId) {
		this.accountId = accountId;
	}

	public APIName getApiName() {
		return apiName;
	}

	public void setApiName(APIName apiName) {
		this.apiName = apiName;
	}

	public Long getCount() {
		return count;
	}

	public void setCount(Long count) {
		this.count = count;
	}

	public Long getKyc() {
		return kyc;
	}

	public void setKyc(Long kyc) {
		this.kyc = kyc;
	}

	public Long getError() {
		return error;
	}

	public void setError(Long error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "LBAggregator{" +
				"accountId=" + accountId +
				", apiName=" + apiName +
				", count=" + count +
				", kyc=" + kyc +
				", error=" + error +
				'}';
	}
}
