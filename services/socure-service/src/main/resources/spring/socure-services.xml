<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	 http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.1.xsd">

	<bean id="springApplicationContext"
		class="me.socure.service.factory.refactorScala.SpringApplicationContext" />
	<bean id="cacheScoreUtil" class="me.socure.util.CacheScoreUtil" />

	<!--  <bean id="acxiomService" class="me.socure.acxiom.identify.service.AcxiomServiceImpl" />

	<bean id="webServiceTemplate" class="org.springframework.ws.client.core.WebServiceTemplate">
		<constructor-arg ref="soapMessageFactory" />
		<property name="marshaller" ref="marshaller" />
		<property name="unmarshaller" ref="marshaller" />
		<property name="defaultUri"
			value="https://identifyuat.insightgateways.com/identify/v1_0/Identify" />
	</bean> -->

	<!-- HttpClient configuration for FullContact -->

	<bean id="NonBlockingFutureTimeout" class="me.socure.service.futuretimeout.NonBlockingFutureTimeoutFactory" />
	<bean id="NonBlockingCompletableFutureTimeout" class="me.socure.service.futuretimeout.NonBlockingCompletableFutureTimeoutFactory" />
	<bean id="AccountServiceIdPlusClient" class="me.socure.account.AccountServiceIdPlusClientFactory" />
	<bean id="AccountServiceIdPlusClientV3" class="me.socure.service.account.AccountServiceIdPlusClientFactoryV3" />

	<bean id="blacklistClient" class="me.socure.service.account.BlacklistClientFactory" />

	<bean id="stepupClient" class="me.socure.docV.client.StepupServiceClientFactory" />
	<bean id="didClient" class="me.socure.docV.client.factory.DecentralizedIDClientFactory" />
	<bean id="transactionStatusValidator" class="me.socure.docV.service.TransactionStatusValidatorFactory" />

	<bean id="documentService" class="me.socure.docV.service.DocumentServiceFactory" />
	<bean id="transactionAuditingClientSns" class="me.socure.service.factory.auditing.client.TransactionAuditingClientSnsFactory"/>
	<bean id="VendorCallAuditingSnsClient" class="me.socure.service.factory.auditing.vendor.VendorCallDataAuditingSnsClientFactory"/>
	<bean id="configureAccountServiceClient" class="me.socure.service.ccm.factory.ConfigureAccountServiceClientFactory" />
	<bean id="configureAccountService" class="me.socure.service.ccm.ConfigureAccountService"/>
	<bean id="requestConfigBuilder" class="org.apache.http.client.config.RequestConfig"
		factory-method="custom">
		<!-- Ed/Giacmo/Gopal, please review this timeout -->
		<property name="socketTimeout" value="8000" />
		<property name="connectTimeout" value="10000" />
	</bean>

	<bean id="requestConfig" factory-bean="requestConfigBuilder"
		factory-method="build" />

	<bean id="poolingClientConnectionManager"
		class="org.apache.http.impl.conn.PoolingHttpClientConnectionManager">
		<property name="maxTotal" value="100"></property>
		<property name="defaultMaxPerRoute" value="20"></property>
	</bean>

	<bean id="connectionHeader" class="org.apache.http.message.BasicHeader">
		<constructor-arg index="0" value="Connection"></constructor-arg>
		<constructor-arg index="1" value="Connection"></constructor-arg>
	</bean>

	<bean id="httpClientBuilder" class="org.apache.http.impl.client.HttpClientBuilder"
		factory-method="create">
		<property name="defaultRequestConfig" ref="requestConfig" />
		<property name="defaultHeaders">
			<list>
				<ref bean="connectionHeader" />
			</list>
		</property>
		<property name="connectionManager" ref="poolingClientConnectionManager"></property>
	</bean>

	<bean id="httpClient" factory-bean="httpClientBuilder"
		factory-method="build" />

    <!-- PIPL SearchConfiguration Starts Here -->
    <bean id="piplClient" class="com.pipl.api.search.SearchConfiguration">
        <property name="apiKey" value="${pipl.key}"/>
        <property name="showSources" value="${pipl.match.criteria}"/>
    </bean>

	<bean id="piplPremiumClient" class="com.pipl.api.search.SearchConfiguration">
		<property name="apiKey" value="${pipl.premium.key}"/>
		<property name="showSources" value="${pipl.match.criteria}"/>
	</bean>

	<bean id="watchlistInfoService" class="me.socure.service.factory.auditing.client.WatchlistInfoServiceFactory"/>
	<bean id="transactionIdValidator" class="me.socure.service.factory.validation.TransactionIdValidatorFactory"/>
	<bean id="controlCenterSettingsServiceClient" class="me.socure.service.factory.settings.ControlCenterSettingsFactory" />
	<bean id="messageEncoder" class="me.socure.service.factory.payload.encryption.MessageEncoderFactory" />
	<bean id="accountPayloadKeysResolver" class="me.socure.service.factory.payload.encryption.AccountPayloadKeysResolverFactory" />
	<bean id="idplusPublicClient" class="me.socure.service.factory.IdplusPublicClientFactory" />
	<bean id="dynamicControlCenterSettingsServiceClient" class="me.socure.service.factory.settings.DynamicControlCenterSettingsFactory" />
	<bean id="dynamicControlCenterServiceClient" class="me.socure.service.factory.settings.DynamicControlCenterSettingsFactory" />
	<bean id="dynamicControlCenterV2EvaluateFactory" class="me.socure.service.factory.settings.DynamicControlCenterV2EvaluateFactory" />
</beans>
