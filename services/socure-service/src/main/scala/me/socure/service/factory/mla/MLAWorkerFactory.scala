package me.socure.service.factory.mla

import me.socure.common.client.kyc.KYCServiceClient
import me.socure.common.client.kyc.factory.KYCServiceClientFactory
import me.socure.common.clock.RealClock
import me.socure.mla.rest.client.MLAClient
import me.socure.mla.rest.client.factory.MLAClientFactory
import me.socure.service.audit.TPAuditService
import me.socure.service.mla.MLAWorker
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.Autowired

import scala.concurrent.ExecutionContext

class MLAWorkerFactory extends FactoryBean[MLAWorker] {
  @Autowired
  var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  @Autowired
  private var tpService: TPAuditService = _

  override def getObject: MLAWorker = {
    new MLAWorker(
      mlaClient = createMLAClient(),
      kYCServiceClient = createKYCClient()(executionContext),
      tpAuditService = tpService,
      clock = new RealClock
    )(executionContext)
  }


  private def createMLAClient(): MLAClient = {
    MLAClientFactory.create(envUtil.getParsedEnvProperties)(executionContext)
  }

  private def createKYCClient()(implicit ec: ExecutionContext): KYCServiceClient = {
    val config = envUtil.getParsedEnvProperties.getConfig("kyc.service")
    KYCServiceClientFactory.createClient(config)
  }
  
  override def getObjectType: Class[MLAWorker] = classOf[MLAWorker]

  override def isSingleton: Boolean = true
}
