package me.socure.service.model.piirisk

import java.util.{HashMap => JHashMap}
import java.util.{List => JList}

import com.socure.common.Util
import com.socure.common.enrichments.RichDouble
import com.socure.domain.socure.Sources.Source
import com.socure.domain.socure.scoring.{RuleCodes, RuleScores, RulesValues}
import me.socure.common.json.bean.debug.{ModelScoreInfo, PiiRiskScoreDebug, PiiRiskMultipleScoreDebug}
import me.socure.common.json.bean.v2_5.PiiRiskScores
import me.socure.common.json.bean.v3_0.RiskScoreDetails
import me.socure.common.request.api.RequestScore
import me.socure.common.transaction.id.TrxId
import me.socure.idplus.rulecode.resolver.util.LegacyConversions
import me.socure.datasci.model.domain.AssociationTypes
import me.socure.model.AccountInformation
import me.socure.service.factory.refactorScala.ScoringComponentMapCreator
import me.socure.service.model.ModelScoreInfoCreator
import me.socure.service.util.DataPluginsAdapter
import me.socure.model.idplus.v3_0.IdPlusFeatures
import me.socure.model.BusinessUserRoles
import me.socure.service.constants.ComponentTypeUtil

import scala.collection.JavaConverters._
import scala.collection.immutable.Map
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 3/17/17.
  */
class PiiRiskServiceIdPlusBridge(
                                  piisRiskService: PiisRiskService
                                )(implicit exe: ExecutionContext) {
  def invoke(
              requestScore: RequestScore,
              scoreComponentMap: Map[String, Map[String, Double]],
              entityResolutionScores: Map[String, Map[String, Double]],
              ruleValues: Map[String, Map[RuleCodes.RuleCode, String]]
            ): Future[PiisRiskOutput] = {

    val ruleValuesPrimitives = ruleValues.mapValues(_.map {
      case (ruleCode, value) => ruleCode.code.toString -> value
    })

    val metaData = DataPluginsAdapter.createMetaData(requestScore)
    piisRiskService.process(PiisRiskInput(
      piiRiskModels = requestScore.queryPlan.idPlusModels.piiRiskModels,
      piiRiskFlags = PiiRiskFlags(
        address = requestScore.queryPlan.allFeatures.contains(IdPlusFeatures.AddressRiskScore),
        email = requestScore.queryPlan.allFeatures.contains(IdPlusFeatures.EmailRiskScore),
        phone = requestScore.queryPlan.allFeatures.contains(IdPlusFeatures.PhoneRiskScore)
      ),
      ruleScores = scoreComponentMap,
      entityResolutionScores = entityResolutionScores,
      ruleValues = ruleValuesPrimitives,
      metaData = metaData
    ))
  }
}

object PiiRiskServiceIdPlusBridge {
  def updateScoringComponentMap(
                                 scoreComponentMap: java.util.Map[String, java.util.Map[String, java.lang.Double]],
                                 ruleScoresBySource: Map[Source, RuleScores]
                               )(implicit trxId: TrxId): java.util.Map[String, java.util.Map[String, java.lang.Double]] = {
    val ruleScoresJava = ruleScoresBySource.map {
      case (source, ruleScores) =>
        ComponentTypeUtil.getComponentTypeKey(LegacyConversions.sourceToComponent(source)) -> ruleScores.map {
          case (ruleCode, score) => ruleCode.code.toString -> double2Double(score.value)
        }.asJava
    }.asJava
    ScoringComponentMapCreator.mergeRules(scoreComponentMap, ruleScoresJava)
    scoreComponentMap
  }

  def extendRuleValues(
                        existingRuleValues: Map[String, RulesValues],
                        ruleValuesBySource: Map[Source, RulesValues]
                      )(implicit trxId: TrxId): Map[String, RulesValues] = {
    Util.merge(
      existingRuleValues,
      ruleValuesBySource.map {
        case (source, ruleValues) => ComponentTypeUtil.getComponentTypeKey(LegacyConversions.sourceToComponent(source)) -> ruleValues
      }
    )
  }

  def getMissingPermissions(
                             accountInformation: AccountInformation,
                             piiRiskFlags: PiiRiskFlags
                           ): Set[String] = {
    def validatePermission(role: Int, requestFlag: Boolean): Option[String] = {
      if (requestFlag && !accountInformation.roles.contains(role)) {
        BusinessUserRoles.values.find(_.id == role).map(_.label)
      } else None
    }

    Set(
      validatePermission(BusinessUserRoles.ADDRESS_RISK_SCORE.id, piiRiskFlags.address),
      validatePermission(BusinessUserRoles.EMAIL_RISK_SCORE.id, piiRiskFlags.email),
      validatePermission(BusinessUserRoles.PHONE_RISK_SCORE.id, piiRiskFlags.phone)
    ).flatten
  }

  def getMissingPermissionsJava(
                                 accountInformation: AccountInformation,
                                 piiRiskFlags: PiiRiskFlags
                               ): java.util.List[String] = {
    getMissingPermissions(
      accountInformation = accountInformation,
      piiRiskFlags = piiRiskFlags
    ).toList.asJava
  }

  def createRiskScores(piisRiskOutput: PiisRiskOutput): Option[PiiRiskScores] = {
    if (piisRiskOutput.isEmpty)
      None
    else {
      val piiRiskScores = new PiiRiskScores()
      piisRiskOutput.address.filter(a => a.associationType.equals(AssociationTypes.Primary)).foreach(o => piiRiskScores.setAddress(extractPiiScore(o).toStr4Digits))
      piisRiskOutput.email.filter(a => a.associationType.equals(AssociationTypes.Primary)).foreach(o => piiRiskScores.setEmail(extractPiiScore(o).toStr4Digits))
      piisRiskOutput.phone.filter(a => a.associationType.equals(AssociationTypes.Primary)).foreach(o => piiRiskScores.setPhone(extractPiiScore(o).toStr4Digits))

      val addressScores = piisRiskOutput.address.filter(a => a.associationType.equals(AssociationTypes.Custom)).map{a => RiskScoreDetails(a.model.name.getOrElse(""), a.model.version.getOrElse(""), extractPiiScore(a))}.asJava
      val emailScores = piisRiskOutput.email.filter(a => a.associationType.equals(AssociationTypes.Custom)).map{a => RiskScoreDetails(a.model.name.getOrElse(""), a.model.version.getOrElse(""), extractPiiScore(a))}.asJava
      val phoneScores = piisRiskOutput.phone.filter(a => a.associationType.equals(AssociationTypes.Custom)).map{a => RiskScoreDetails(a.model.name.getOrElse(""), a.model.version.getOrElse(""), extractPiiScore(a))}.asJava

      piiRiskScores.setAddressScores(addressScores)
      piiRiskScores.setEmailScores(emailScores)
      piiRiskScores.setPhoneScores(phoneScores)

      Some(piiRiskScores)
    }
  }

  def createModelScoreInfo(piiRiskOutput: PiiRiskOutput): ModelScoreInfo = {
    ModelScoreInfoCreator.create(
      model = piiRiskOutput.model,
      score = Option(piiRiskOutput.score.value),
      params = piiRiskOutput.requestBody,
      response = piiRiskOutput.responseBody
    )
  }

  def createDebugInfo(piisRiskOutput: PiisRiskOutput): Option[PiiRiskScoreDebug] = {
    if (piisRiskOutput.isEmpty)
      None
    else {
      val piiRiskScoreDebug = new PiiRiskScoreDebug()
      piisRiskOutput.address.filter(a => a.associationType.equals(AssociationTypes.Primary)).map(createModelScoreInfo).foreach(piiRiskScoreDebug.setAddress)
      piisRiskOutput.email.filter(a => a.associationType.equals(AssociationTypes.Primary)).map(createModelScoreInfo).foreach(piiRiskScoreDebug.setEmail)
      piisRiskOutput.phone.filter(a => a.associationType.equals(AssociationTypes.Primary)).map(createModelScoreInfo).foreach(piiRiskScoreDebug.setPhone)

      val custom = new PiiRiskMultipleScoreDebug()
      val cAddress = new JHashMap[String, ModelScoreInfo]()
      val cEmail = new JHashMap[String, ModelScoreInfo]()
      val cPhone = new JHashMap[String, ModelScoreInfo]()
      custom.setAddress(cAddress)
      custom.setEmail(cEmail)
      custom.setPhone(cPhone)
      piiRiskScoreDebug.setCustom(custom)

      val shadow = new PiiRiskMultipleScoreDebug()
      val sAddress = new JHashMap[String, ModelScoreInfo]()
      val sEmail = new JHashMap[String, ModelScoreInfo]()
      val sPhone = new JHashMap[String, ModelScoreInfo]()
      shadow.setAddress(sAddress)
      shadow.setEmail(sEmail)
      shadow.setPhone(sPhone)
      piiRiskScoreDebug.setShadow(shadow)

      getCustomModelDebug(piisRiskOutput.address.filter(a => a.associationType.equals(AssociationTypes.Custom)), cAddress)
      getCustomModelDebug(piisRiskOutput.email.filter(a => a.associationType.equals(AssociationTypes.Custom)), cEmail)
      getCustomModelDebug(piisRiskOutput.phone.filter(a => a.associationType.equals(AssociationTypes.Custom)), cPhone)
      getCustomModelDebug(piisRiskOutput.address.filter(a => a.associationType.equals(AssociationTypes.Shadow)), sAddress)
      getCustomModelDebug(piisRiskOutput.email.filter(a => a.associationType.equals(AssociationTypes.Shadow)), sEmail)
      getCustomModelDebug(piisRiskOutput.phone.filter(a => a.associationType.equals(AssociationTypes.Shadow)), sPhone)

      Some(piiRiskScoreDebug)
    }
  }

  private def getCustomModelDebug(piiRiskOutput: Seq[PiiRiskOutput], map: JHashMap[String, ModelScoreInfo]) {
    piiRiskOutput.map { op =>
      val modelName = if(op.associationType.equals(AssociationTypes.Shadow)) op.model.identifier + "__" + op.model.version.getOrElse("unknown_version") else op.model.name.getOrElse("unknown_model") + "__" + op.model.version.getOrElse("unknown_version")
      val shadow = createModelScoreInfo(op)
      map.put(modelName, shadow)
    }
  }

  private def extractPiiScore(output: PiiRiskOutput): Double = {
    output.model.quantileMapping match {
      case Some(mapping) => mapping.get(output.score.value).roundTo(4)
      case None => output.score.value.roundTo(4)
    }
  }

}
