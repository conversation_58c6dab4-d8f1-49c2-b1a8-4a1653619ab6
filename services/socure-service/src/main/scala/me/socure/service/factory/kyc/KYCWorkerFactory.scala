package me.socure.service.factory.kyc

import me.socure.common.client.kyc.KycEntityServiceClient
import me.socure.common.clock.RealClock
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.service.audit.TPAuditService
import me.socure.service.kyc.KycEntityWorker
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.Autowired

import scala.concurrent.ExecutionContext

class KYCWorkerFactory extends FactoryBean[KycEntityWorker] {
  @Autowired
  private var executionContext: ExecutionContext = _

  @Autowired
  private var tpAuditService: TPAuditService = _

  @Autowired
  private var kycEntityServiceClient: KycEntityServiceClient = _

  @Autowired
  var dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate = _


  override def getObject: KycEntityWorker = {
    new KycEntityWorker(
      kycEntityClient = kycEntityServiceClient,
      tpAuditService = tpAuditService,
      clock = new RealClock,
      dynamicControlCenter = dynamicControlCenterV2Evaluate
    )(executionContext)
  }

  override def isSingleton: Boolean = true

  override def getObjectType: Class[_] = classOf[KycEntityWorker]
}
