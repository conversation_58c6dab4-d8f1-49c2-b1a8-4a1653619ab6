package me.socure.service.syntheticscore

import com.socure.domain.socure.scoring.RuleCodes
import me.socure.common.request.api.RequestScore
import me.socure.datasci.model.domain.Model
import me.socure.datasci.model.synthetic.SyntheticModels
import me.socure.service.model.syntheticscore.SyntheticScoreResponse

import scala.collection.immutable.Map
import scala.concurrent.Future

trait SyntheticScoreService {

  def getSyntheticScore(requestScore: RequestScore,
                        syntheticModels: SyntheticModels,
                        scoreComponentMap: Map[String, Map[String, Double]],
                        entityResolutionScores: Map[String, Map[String, Double]],
                        ruleValues: Map[String, Map[RuleCodes.RuleCode, String]]) : Future[Map[Model, SyntheticScoreResponse]]

}