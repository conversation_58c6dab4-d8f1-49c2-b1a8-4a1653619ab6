package me.socure.service.factory.dsmodel

import me.socure.datasci.model.correlation.{CorrelationModelResolver, PiiRiskModelResolver}
import me.socure.datasci.model.device.DeviceRiskModelResolver
import me.socure.datasci.model.fraud.FraudModelService
import me.socure.datasci.model.synthetic.SyntheticModelResolver
import me.socure.datasci.model.fpf.FpfModelResolver
import me.socure.queryplanner.models.{IdPlusModelsResolver, IdPlusModelsResolverImpl}
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.Autowired

import scala.concurrent.ExecutionContext

/**
 * Created by jamesanto on 5/23/17 at 12:15 PM.
 */
class IdPlusModelResolverFactory extends FactoryBean[IdPlusModelsResolver] {

  @Autowired
  private var fraudModelResolver: FraudModelService = _

  @Autowired
  private var correlationModelResolver: CorrelationModelResolver = _

  @Autowired
  private var piiRiskModelResolver: PiiRiskModelResolver = _

  @Autowired
  private var executionContext: ExecutionContext = _

  @Autowired
  private var syntheticModelResolver: SyntheticModelResolver = _

  @Autowired
  private var deviceRiskModelResolver: DeviceRiskModelResolver = _

  @Autowired
  private var fpfModelResolver: FpfModelResolver = _

  override def getObject: IdPlusModelsResolver = {
    new IdPlusModelsResolverImpl(
      fraudModelResolver = fraudModelResolver,
      correlationModelResolver = correlationModelResolver,
      piiRiskModelResolver = piiRiskModelResolver,
      syntheticModelResolver = syntheticModelResolver,
      deviceRiskModelResolver = deviceRiskModelResolver,
      fpfModelResolver = fpfModelResolver
    )(executionContext)
  }

  override def isSingleton: Boolean = true

  override def getObjectType: Class[_] = classOf[IdPlusModelsResolver]
}
