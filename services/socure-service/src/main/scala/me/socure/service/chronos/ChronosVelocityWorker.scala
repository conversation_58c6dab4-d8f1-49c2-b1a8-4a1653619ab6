package me.socure.service.chronos

import com.socure.common.enrichments.RichStringSplit
import com.socure.domain.socure.Sources
import com.socure.domain.socure.scoring.RuleCodes
import me.socure.address.client.model.AddressFailure
import me.socure.address.common.data.model.SmartyStreetResponseAndCodes
import me.socure.chronos.olap.client.ChronosOlapClient
import me.socure.chronos.olap.common.model._
import me.socure.common.clock.Clock
import me.socure.common.json.bean.entity.ScoringComponent
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.request.api.RequestScore
import me.socure.common.transaction.id.TrxId
import me.socure.idplus.rulecode.resolver.util.LegacyConversions
import me.socure.model.BusinessUserRoles
import me.socure.service.audit.TPAuditService
import me.socure.service.constants.IConstants.Component
import me.socure.service.factory.refactorScala.WorkContainer
import me.socure.service.util.TPAuditInfoUtil
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds

import java.util.{Map => JMap}
import scala.collection.JavaConversions._
import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class ChronosVelocityWorker(chronosOlapClient: ChronosOlapClient,
                            tpAuditService: TPAuditService,
                            clock: Clock)(implicit ec: ExecutionContext) {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("vendor.worker")

  private val vendor = Sources.CVSCV
  private val component = LegacyConversions.sourceToComponent(vendor)(TrxId("startup"))

  private val SuccessRuleCode = "100135"
  private val FailureRuleCode = "100130"


  def work(msg: WorkContainer): Future[ScoringComponent] = {
    val req = msg.request.get
    implicit val trxId: TrxId = TrxId(req.transactionId)

    val scoringComponent = msg.scoringComponent
    scoringComponent.setProviderType(component.getCode.shortValue())
    callChronosOlapService(req, msg.smsReqFuture.value).map { configBasedResponse =>
      val ruleCodes = configBasedResponse.groupBy { case (key, _) => key.split("\\.")(0)
      }.map { case (vendor, rcMap) =>
        vendor -> rcMap.collect { case (rc, value) if value.isDefined =>
          rc.split("\\.")(1) -> double2Double(value.get)
        }.asJava
      }

      scoringComponent.setExtraRules(ruleCodes.asJava)
      scoringComponent.setExtraCatRules(Map.empty[String, JMap[RuleCodes.RuleCode, String]].asJava)
      scoringComponent.addInternalProvider(Component.SMSVL.getCode)
      scoringComponent.setIsVendorCallsMade(true)

      scoringComponent // Return the updated scoringComponent
    }

  }

  private def callChronosOlapService(req: RequestScore,
                                     smsFuture: Future[Either[AddressFailure, SmartyStreetResponseAndCodes]]
                                    )(implicit trxId: TrxId): Future[Map[String, Option[Double]]] = {
    val requestStartTime = clock.now()
    val accountId = req.account.getAccountId
    val environmentName = req.account.environment.name
    val maskPii = req.account.roles.contains(BusinessUserRoles.MASK_PII.id)

    val auditedResponse = for {
      chronosOlapRequest <- createRequest(req, smsFuture)
      chronosOlapResponse <- chronosOlapClient.getV2(chronosOlapRequest)
    } yield chronosOlapResponse

    auditedResponse.map { response =>
      val auditInfo = response match {
        case Left(e) => {
          val auditingInfo = e.auditingInfo
          TPAuditInfoUtil.buildAuditInfo(
            accountId = Some(accountId),
            trxId = Some(trxId.value),
            startTime = Some(requestStartTime.toDate()),
            processingTime = Some(auditingInfo.processingTime),
            isCache = Some(false),
            request = Some(auditingInfo.url),
            requestBody = auditingInfo.requestBody,
            response = auditingInfo.responseBody,
            isError = Option(auditingInfo.httpStatusCode).map(_ != 200),
            uuid = None,
            serviceName = ThirdPartyServiceIds.ChronosOlapService.name)
        }
        case Right(r) => {
          val auditingInfo = r.auditingInfo
          TPAuditInfoUtil.buildAuditInfo(
            accountId = Some(accountId),
            trxId = Some(trxId.value),
            startTime = Some(requestStartTime.toDate()),
            processingTime = Some(auditingInfo.processingTime),
            isCache = Some(false),
            request = Some(auditingInfo.url),
            requestBody = auditingInfo.requestBody,
            response = auditingInfo.responseBody,
            isError = Option(auditingInfo.httpStatusCode).map(_ != 200),
            uuid = None,
            serviceName = ThirdPartyServiceIds.ChronosOlapService.name)
        }
      }
      tpAuditService.auditN(auditInfo, maskPii)
      response match {
        case Left(err) =>
          logger.error("error occurred while calling config based chronos velocity", err)
          ChronosUtils.ChronosVendors.flatMap { comp =>
            Map(comp + "." + FailureRuleCode -> Option(1.0))
          }.toMap
        case Right(velocityMap) =>
          velocityMap.result ++ ChronosUtils.ChronosVendors.flatMap { comp =>
            Map(comp + "." + SuccessRuleCode -> Option(1.0))
          }.toMap
      }
    }.recover {
      case NonFatal(e) =>
        val auditInfo = TPAuditInfoUtil.buildAuditInfo(
          accountId = Some(accountId),
          trxId = Some(trxId.value),
          startTime = Some(requestStartTime.toDate()),
          processingTime = Some(clock.now().getMillis - requestStartTime.getMillis),
          isCache = None,
          request = Some("/chronosVelocity"),
          requestBody = None,
          response = Some(e.getMessage),
          isError = Option(true),
          uuid = None,
          serviceName = ThirdPartyServiceIds.ChronosOlapService.name)
        tpAuditService.auditN(auditInfo, maskPii)
        metrics.increment("client.v2.error", s"class:${e.getClass.getSimpleName}", s"environmentName:$environmentName")
        logger.error(s"Unknown error occurred while calling chronos velocity /v2 for ${vendor.name}", e)
        ChronosUtils.ChronosVendors.flatMap { comp =>
          Map(comp.getScoringKey + "." + FailureRuleCode -> Option(1.0))
        }.toMap
    }
  }

  private def createRequest(req: RequestScore,
                            smsFuture: Future[Either[AddressFailure, SmartyStreetResponseAndCodes]])
                           (implicit trxId: TrxId): Future[ChronosOlapRequest] = {

    val maskPii = req.account.roles.contains(BusinessUserRoles.MASK_PII.id)

    val preprocessedInputs = req.preprocessedStreetAddress


    val chronosOlapRequest = ChronosOlapRequest(
      accountId = req.account.getAccountId,
      environmentName = Option(req.account.environment.name),
      transactionId = req.transactionId,
      transactionDate = req.transactionDate.getMillis,
      maskPii = maskPii,
      deviceSessionId = req.deviceSessionId,
      customerInputs = Some(CustomerInputs(
        dob = req.dob,
        email = req.email,
        firstName = req.firstName,
        surName = req.surName,
        ipAddress = req.ipAddress,
        mobileNumber = Option(req.userInfo.get("originalmobile")).clean(),
        nationalId = req.nationalId,
        address = Some(Address(
          city = req.city,
          country = req.country,
          physicalAddress = req.physicalAddress,
          physicalAddress2 = req.physicalAddress2,
          state = req.state,
          zip = req.zip,
          token = None,
          plus4Code = None
        ))
      )),
      preprocessedInputs = Some(PreprocessedInputs(
        address = Address(
          city = preprocessedInputs.city,
          country = preprocessedInputs.country,
          physicalAddress = preprocessedInputs.street,
          physicalAddress2 = preprocessedInputs.street2,
          state = preprocessedInputs.state,
          zip = preprocessedInputs.zipcode,
          token = None,
          plus4Code = None
        )
      )),
      normalizedOutput = None
    )

    NormalizedSmsAddress.parse(smsFuture, req.country).map { nop =>
      chronosOlapRequest.copy(normalizedOutput = nop)
    }
  }

}
