package me.socure.service.idplus.scoring.adjustment.impl

import com.socure.domain.socure.scoring.RuleCodes
import me.socure.service.constants.IConstants.Component
import me.socure.service.idplus.scoring.ScoringVariables
import me.socure.service.idplus.scoring.adjustment.TimeSensitiveVariableAdjuster
import org.joda.time.{DateTimeZone, Days, LocalDate}
import org.joda.time.format.DateTimeFormat

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 9/26/17.
  */
object TimeSensitiveVariableAdjusterFmval300016 extends TimeSensitiveVariableAdjuster {
  private val ruleDateFormat = DateTimeFormat.forPattern("yyyyMMdd")

  override def adjust(scoringVariables: ScoringVariables, driftDays: Int): ScoringVariables = {
    getScoringVariable(scoringVariables = scoringVariables, driftDays = driftDays, localDate = LocalDate.now(DateTimeZone.UTC))
  }

  def getScoringVariable(scoringVariables: ScoringVariables, driftDays: Int, localDate: LocalDate): ScoringVariables ={
    scoringVariables.categoricalRules.get(Component.FORM_VAL.getScoringKey).flatMap(_.get(RuleCodes.FM_ACCOUNT_CREATION_DATE)) match {
      case Some(score) =>
        driftDays match {
          case (x) if x <= 0 =>
            val noOfDays = Days.daysBetween(LocalDate.parse(score.toString, ruleDateFormat), localDate).getDays
            scoringVariables.withNumericScore(group = Component.FORM_VAL.getScoringKey,
              code = RuleCodes.FM_NUMBER_DAYS_SINCE_ACCOUNT_CREATION_DATE.code.toString,
              value = if (noOfDays > 0) noOfDays else 0)
          case (x) if x > 0 =>
            val noOfDays = Days.daysBetween(LocalDate.parse(score.toString, ruleDateFormat), localDate.minusDays(x)).getDays
            scoringVariables.withNumericScore(group = Component.FORM_VAL.getScoringKey,
              code = RuleCodes.FM_NUMBER_DAYS_SINCE_ACCOUNT_CREATION_DATE.code.toString,
              value = if (noOfDays > 0) noOfDays else 0)
          case _ => scoringVariables
        }
      case None => scoringVariables
    }
  }
}
