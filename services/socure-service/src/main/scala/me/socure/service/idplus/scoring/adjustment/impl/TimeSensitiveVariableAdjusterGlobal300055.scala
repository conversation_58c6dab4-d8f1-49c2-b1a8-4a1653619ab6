package me.socure.service.idplus.scoring.adjustment.impl

import com.socure.domain.socure.scoring.RuleCodes
import me.socure.service.constants.IConstants.Component
import me.socure.service.idplus.scoring.ScoringVariables
import me.socure.service.idplus.scoring.adjustment.TimeSensitiveVariableAdjuster

/**
  * Created by jamesanto on 7/3/17.
  */
object TimeSensitiveVariableAdjusterGlobal300055 extends TimeSensitiveVariableAdjuster {

  private[impl] val expectedValues = Set("A1","A2","A3")

  override def adjust(scoringVariables: ScoringVariables, driftDays: Int): ScoringVariables = {
    scoringVariables.categoricalRules.get(Component.NSVAL.getScoringKey).flatMap(_.get(RuleCodes.NS_PHONE_IN_SERVICE_INDICATOR_ORIG)) match {
      case Some(value) if expectedValues.contains(value) => scoringVariables.withNumericScore(Component.GLOBAL.getScoringKey, RuleCodes.GLOBAL_PHONE_IN_SERVICE_SHORTER_ORIG.code.toString, 1.0)
      case _ => scoringVariables
    }
  }
}
