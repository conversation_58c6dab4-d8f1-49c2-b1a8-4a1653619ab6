package me.socure.util.test;

import me.socure.service.SocureCacheService;
import me.socure.service.factory.refactorScala.SpringApplicationContext;
import me.socure.util.EnvUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.IOException;
import java.util.Properties;

/**
 * Created by joonkang on 10/12/15.
 */
@Configuration
@PropertySource( "classpath:properties/dev_joon.environment.properties")
public class TestSpringConfiguration {
    @Autowired
    Environment env;


    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor t = new ThreadPoolTaskExecutor();
        t.setCorePoolSize(3);
        t.setMaxPoolSize(6);
        t.setQueueCapacity(3);
        return t;
    }

    @Bean
    public SocureCacheService socureCacheService() {
        return new MockCacheService();
    }

    @Bean(name="socureApplicationContext")
    public SpringApplicationContext springApplicationContext() {
        return  new SpringApplicationContext();
    }

    @Bean(name="envUtil")
    public EnvUtil envUtil() {
        EnvUtil retval = new EnvUtil();
        retval.setEnvProperties(this.envProperties());
        return retval;
    }
    @Bean(name="envProperties")
    public Properties envProperties() {
        PropertiesFactoryBean props = new PropertiesFactoryBean();
        props.setLocation(new ClassPathResource("classpath:/properties/dev_joon.environment.properties"));
        try {
            Properties retval = props.getObject();
            //System.out.println("number of properties loaded: "  + retval.size());
            return retval;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new Properties();
    }



}
