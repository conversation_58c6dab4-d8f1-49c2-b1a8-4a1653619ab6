package me.socure.service.idplus.scoring.adjustment.impl

import com.socure.domain.socure.scoring.RuleCodes
import me.socure.service.constants.IConstants.Component


/**
  * Created by jamesanto on 7/4/17.
  */
class TimeSensitiveVariableAdjusterWpval300046Test extends TimeSensitiveVariableAdjusterTestSupport {
  private implicit val adjuster = TimeSensitiveVariableAdjusterWpval300046

  test(Component.WPVAL, RuleCodes.WP_EMAIL_FIRST_SEEN_DAYS_ORIG, RuleCodes.WP_EMAIL_FIRST_SEEN_DAYS)(
    (10, 9.0, Some(9.0)),
    (0, 9.0, Some(9.0)),
    (10, 15.0, Some(5.0))
  )
}
