package me.socure.service.refactor.refactorScala

import com.socure.common.struct.Countries
import me.socure.common.tracer.DisabledTransactionTracerServiceFactory
import me.socure.common.transaction.id.TrxId
import me.socure.datasci.model.correlation.CorrelationModels
import me.socure.datasci.model.domain.{Features, Model, PrimarySecondaryModel}
import me.socure.service.factory.refactorScala.CorrelationPluginsFactory
import me.socure.service.factory.refactorScala.correlation._
import me.socure.service.factory.refactorScala.fv.CorrelationScoresInput._
import me.socure.service.fraudscore.ModelFixture
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by jamesanto on 5/27/16.
  */
class CorrelationPluginsFactoryTest extends FreeSpec with Matchers with MockFactory with ScalaFutures with CorrelationDataSourcesMock{

  override implicit val patienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(10, Milliseconds))

  implicit val exe = ExecutionContext.Implicits.global
  implicit val trxId = TrxId("test")

  val pluginsFactory = new CorrelationPluginsFactory(
    h2oDataSource = h2oDataSource,
    config = config,
    tracer = DisabledTransactionTracerServiceFactory.get()
  )

  val nameAddress = ModelFixture.create(
    url = "http://h2o-predict-datasci.us-east-vpc.socure.be/api/v1/570daee92ba5b4256464c4ea/570daf3c2ba5b4257b01c3c3/predict",
    name = Some("name"),
    identifier = "default",
    feature = Features.CS_NAME_ADDRESS,
    params = """{"FCVAL.100117":null,"FCvFM.name":null,"PBvFM.email":null,"PBvFM.name":null,"WPVAL.100050":null,"WPVAL.100051":null,"country":null}""",
    config = Some(
      """
        |{
        | "negative_threshold" : 0.01,
        | "positive_threshold" : 0.99
        |}
      """.stripMargin)
  )

  val nameEmail = nameAddress.copy(feature = Features.CS_NAME_EMAIL)
  val namePhone = nameAddress.copy(feature = Features.CS_NAME_PHONE)
  val nameAddressInt = nameAddress.copy(feature = Features.CS_NAME_ADDRESS_INT)
  val nameEmailInt = nameAddress.copy(feature = Features.CS_NAME_EMAIL_INT)
  val namePhoneInt = nameAddress.copy(feature = Features.CS_NAME_PHONE_INT)

  val usModels = CorrelationModels(
    nameAddress = PrimarySecondaryModel(Some(nameAddress), None, Set.empty),
    nameEmail = PrimarySecondaryModel(Some(nameEmail), None, Set.empty),
    namePhone = PrimarySecondaryModel(Some(namePhone), None, Set.empty)
  )

  val intModels = CorrelationModels(
    nameAddress = PrimarySecondaryModel(Some(nameAddressInt), None, Set.empty),
    nameEmail = PrimarySecondaryModel(Some(nameEmailInt), None, Set.empty),
    namePhone = PrimarySecondaryModel(Some(namePhoneInt), None, Set.empty)
  )

  "Correlation Plugins Factory" - {
    "should find relevant plugins for US model" in {
      val pluginsFuture = pluginsFactory.findRelevantPlugins(dataPluginRequest, usModels)
      whenReady(pluginsFuture) { plugins =>
        checkPlugins(plugins, "US", isUS = true)
      }
    }

    "should find relevant plugins for International model" in {
      val internationalDataPluginRequest = dataPluginRequest.copyWith(
        _form = dataPluginRequest.form.copy(
          postalAddress = dataPluginRequest.form.postalAddress.map { postalAddress =>
            postalAddress.copy(
              country = Some(Countries.CANADA)
            )
          }
        )
      )
      val pluginsFuture = pluginsFactory.findRelevantPlugins(internationalDataPluginRequest, intModels)
      whenReady(pluginsFuture) { plugins =>
        checkPlugins(plugins, "International", isUS = false)
      }
    }

    "should convert model to Config" - {
      "and override all from custom config" in {
        val fvConf = pluginsFactory.modelToConfig(nameEmail)
        fvConf.url shouldBe "http://h2o-predict-datasci.us-east-vpc.socure.be/api/v1/570daee92ba5b4256464c4ea/570daf3c2ba5b4257b01c3c3/predict"
        fvConf.positiveThreshold shouldBe 0.99
        fvConf.negativeThreshold shouldBe 0.01
      }
    }
  }

  private def checkPlugins(plugins: Map[CorrelationSupport, Option[Model]], name: String, isUS: Boolean) = {
    plugins.keys.map(_.getClass) should contain theSameElementsAs  List(
      classOf[NamePhoneCorrelationPlugin],
      classOf[NameEmailCorrelationPlugin],
      classOf[NameAddressCorrelationPlugin],

      classOf[CorrelationNameAddrEmailCalc],
      classOf[CorrelationNameAddrPhCalc],
      classOf[CorrelationNamePhEmailCalc],
      classOf[CorrelationNameAddrPhEmailCalc]
    )

    plugins
      .keySet
      .filter(_.isBase)
      .map(_.name)
      .forall(_.contains(name)) shouldBe true

    plugins.find(_._1.correlationType.exists(_ == CorrelationTypes.NAME_EMAIL)).flatMap(_._2) shouldBe Some(if(isUS) nameEmail else nameEmailInt)
    plugins.find(_._1.correlationType.exists(_ == CorrelationTypes.NAME_ADDRESS)).flatMap(_._2) shouldBe Some(if(isUS) nameAddress else nameAddressInt)
    plugins.find(_._1.correlationType.exists(_ == CorrelationTypes.NAME_PHONE)).flatMap(_._2) shouldBe Some(if(isUS) namePhone else namePhoneInt)
  }
}
