package me.socure.service.idplus.v3_0

import me.socure.datasci.model.correlation.{CorrelationModels, PiiRiskModels}
import me.socure.datasci.model.device.DeviceRiskModels
import me.socure.datasci.model.domain.PrimarySecondaryModel
import me.socure.datasci.model.fpf.FpfModels
import me.socure.datasci.model.fraud.FraudModels
import me.socure.datasci.model.synthetic.SyntheticModels
import me.socure.queryplanner.models.IdPlusModels
import org.scalatest.{FreeSpec, Matchers}

/**
  * Created by jamesanto on 5/29/17 at 6:08 PM.
  */
class RuleCodesExtractorTest extends FreeSpec with Matchers {

  import me.socure.datasci.model.test.TestObjects._

  val fraudModels = FraudModels(
    primary = Some(fraud.copy(params = """{"RULE_FRAUD1" : null, "RULE_FRAUD2" : null}""")),
    sigma = Some(fraud.copy(params = """{"RULE_FRAUD1_SIGMA" : null, "RULE_FRAUD2_SIGMA" : null}""")),
    secondary = Set(
      fraud1.copy(params = """{"RULE_FRAUD_SEC_1_1" : null, "RULE_FRAUD_SEC_1_2" : null}"""),
      fraud2.copy(params = """{"RULE_FRAUD_SEC_2_1" : null, "RULE_FRAUD_SEC_2_2" : null}""")
    )
  )

  val fraudModelsNoPrimary = FraudModels(
    primary = None,
    sigma = None,
    secondary = Set(
      fraud1.copy(params = """{"RULE_FRAUD_SEC_1_1" : null, "RULE_FRAUD_SEC_1_2" : null}"""),
      fraud2.copy(params = """{"RULE_FRAUD_SEC_2_1" : null, "RULE_FRAUD_SEC_2_2" : null}""")
    )
  )

  val correlationModels = CorrelationModels(
    nameAddress = PrimarySecondaryModel(Some(csNameAddress.copy(params = """{"CS_NAME_ADDRESS_1" : null, "CS_NAME_ADDRESS_2" : null}""")), None, Set.empty),
    nameEmail = PrimarySecondaryModel(Some(csNameEmail.copy(params = """{"CS_NAME_EMAIL_1" : null, "CS_NAME_EMAIL_2" : null}""")), None, Set.empty),
    namePhone = PrimarySecondaryModel(Some(csNamePhone.copy(params = """{"CS_NAME_PHONE_1" : null, "CS_NAME_PHONE_2" : null}""")), None, Set.empty)
  )

  val piiRiskModels = PiiRiskModels(
    address = PrimarySecondaryModel(Some(prAddressUs.copy(params = """{"PR_ADDRESS_1" : null, "PR_ADDRESS_2" : null}""")), None, Set.empty),
    email = PrimarySecondaryModel(Some(prEmailUs.copy(params = """{"PR_EMAIL_1" : null, "PR_EMAIL_2" : null}""")), None, Set.empty),
    phone = PrimarySecondaryModel(Some(prPhoneUs.copy(params = """{"PR_PHONE_1" : null, "PR_PHONE_2" : null}""")), None, Set.empty)
  )

  val syntheticModels = SyntheticModels(
    secondary = Set(synthetic)
  )

  val fpfModels: FpfModels = FpfModels(
    secondary = Set(firstPartyFraud)
  )

  val idPlusModels = IdPlusModels(
    fraudModels = Some(fraudModels),
    correlationModels = correlationModels,
    piiRiskModels = piiRiskModels,
    syntheticModels = syntheticModels,
    deviceRiskModels = DeviceRiskModels.Empty,
    fpfModels = fpfModels
  )

  val idPlusModelsNoPrimaryFraudModel = IdPlusModels(
    fraudModels = Some(fraudModelsNoPrimary),
    correlationModels = correlationModels,
    piiRiskModels = piiRiskModels,
    syntheticModels = syntheticModels,
    deviceRiskModels = DeviceRiskModels.Empty,
    fpfModels = fpfModels
  )

  "RuleCodesExtractor" - {
    "should extract rule codes from models" in {
      val expectedRuleCodes = Set(
        "RULE_FRAUD1",
        "RULE_FRAUD2",
        "RULE_FRAUD1_SIGMA",
        "RULE_FRAUD2_SIGMA",
        "RULE_FRAUD_SEC_1_1",
        "RULE_FRAUD_SEC_1_2",
        "RULE_FRAUD_SEC_2_1",
        "RULE_FRAUD_SEC_2_2",
        "CS_NAME_ADDRESS_1",
        "CS_NAME_ADDRESS_2",
        "CS_NAME_EMAIL_1",
        "CS_NAME_EMAIL_2",
        "CS_NAME_PHONE_1",
        "CS_NAME_PHONE_2",
        "PR_ADDRESS_1",
        "PR_ADDRESS_2",
        "PR_EMAIL_1",
        "PR_EMAIL_2",
        "PR_PHONE_1",
        "PR_PHONE_2"
      )

      RuleCodesExtractor.fromModels(idPlusModels = idPlusModels) shouldBe expectedRuleCodes
    }

    "should extract rule codes from models when no primary fraud model is defined" in {
      val expectedRuleCodes = Set(
        "RULE_FRAUD_SEC_1_1",
        "RULE_FRAUD_SEC_1_2",
        "RULE_FRAUD_SEC_2_1",
        "RULE_FRAUD_SEC_2_2",
        "CS_NAME_ADDRESS_1",
        "CS_NAME_ADDRESS_2",
        "CS_NAME_EMAIL_1",
        "CS_NAME_EMAIL_2",
        "CS_NAME_PHONE_1",
        "CS_NAME_PHONE_2",
        "PR_ADDRESS_1",
        "PR_ADDRESS_2",
        "PR_EMAIL_1",
        "PR_EMAIL_2",
        "PR_PHONE_1",
        "PR_PHONE_2"
      )

      RuleCodesExtractor.fromModels(idPlusModels = idPlusModelsNoPrimaryFraudModel) shouldBe expectedRuleCodes
    }
  }
}
