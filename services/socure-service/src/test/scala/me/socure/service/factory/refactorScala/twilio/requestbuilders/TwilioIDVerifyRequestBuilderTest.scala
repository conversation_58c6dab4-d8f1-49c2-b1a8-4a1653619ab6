package me.socure.service.factory.refactorScala.twilio.requestbuilders

import java.util

import me.socure.service.factory.refactorScala.RequestScoreValueGenerator
import me.socure.service.factory.refactorScala.twilio.requestBuilders.TwilioPhoneValidationRequestBuilder
import org.scalatest.{FunSuite, Matchers}

class TwilioPhoneValidateRequestBuilderTest extends FunSuite with Matchers {

  private def requestScoreWithPhone(phone: Option[String]) = {
    val userData = new util.HashMap[String, String]()
    for (p <- phone) {
      userData.put("mobilenumber", p)
    }
    RequestScoreValueGenerator.aRequestScore(userInfo = userData)
  }

  test("should clean phone properly with spaces") {
    val requestScore = requestScoreWithPhone(Some("************"))

    val twilioRequest = TwilioPhoneValidationRequestBuilder.build(requestScore)

    twilioRequest.phone shouldBe "3476120436"
  }

  test("should clean phone properly with international notation") {
    val requestScore = requestScoreWithPhone(Some("+1************"))

    val twilioRequest = TwilioPhoneValidationRequestBuilder.build(requestScore)

    twilioRequest.phone shouldBe "+13476120436"
  }

  test("should not touch already clean phones") {
    val requestScore = requestScoreWithPhone(Some("3476120436"))

    val twilioRequest = TwilioPhoneValidationRequestBuilder.build(requestScore)

    twilioRequest.phone shouldBe "3476120436"
  }

  test("should not touch already cleaned international notation") {
    val requestScore = requestScoreWithPhone(Some("+13476120436"))

    val twilioRequest = TwilioPhoneValidationRequestBuilder.build(requestScore)

    twilioRequest.phone shouldBe "+13476120436"
  }
}
