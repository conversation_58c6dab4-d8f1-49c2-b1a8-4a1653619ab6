<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"


       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd













         http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util-3.1.xsd">
        

	<!-- JCE Config -->
	<!-- The will be the encryptor used for decrypting configuration values. -->
	
	<bean id="configurationEncryptor" class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
		<!-- Refers to me.socure.config.SocurePBEConfig -->
		<property name="config" ref="socurePBEConfig" />
	</bean>


	<bean id="envProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="locations">
			<list>
				<value>classpath:properties/${PARAM1}.environment.properties</value>
			</list>
		</property>
	</bean>
	
	<bean class="org.jasypt.spring31.properties.EncryptablePropertyPlaceholderConfigurer">
		<constructor-arg ref="configurationEncryptor" />
		<property name="properties" ref="envProperties" />
	</bean>

	<bean id="mailProperties"
		class="org.jasypt.spring31.xml.encryption.EncryptablePropertiesFactoryBean">
		<property name="encryptor" ref="configurationEncryptor" ></property>
		<property name="locations">
			<list>
				<value>classpath:/properties/mail.properties</value>
			</list>
		</property>
	</bean>

	<!-- <bean id="memcachedProperties" class="org.springframework.beans.factory.config.PropertiesFactoryBean"> 
		<property name="locations"> <list> <value>classpath:/properties/memcached.properties</value> 
		</list> </property> </bean> -->

	<bean id="piplProperties"
		class="org.jasypt.spring31.xml.encryption.EncryptablePropertiesFactoryBean">
		<property name="encryptor" ref="configurationEncryptor" ></property>
		<property name="locations">
			<list>
				<value>classpath:/properties/pipl.properties</value>
			</list>
		</property>
	</bean>
	
	<util:properties id="producerConfigProperties" location="classpath:/properties/${PARAM1}.environment.properties" />

</beans>
