package me.socure.service.scoring.impl

import org.scalatest.{FreeSpec, Matchers}

import java.util
import scala.collection.JavaConverters._

class ScoringServiceImplTest extends FreeSpec with Matchers {

  "test throws UnsupportedOperationException when updating immutable java collection" in {
    val scalaSeq: Seq[String] = Seq("a", "b")
    val javaList: java.util.List[String] = scalaSeq.asJava
    intercept[UnsupportedOperationException] {
      javaList.add("c")
    }
    javaList.size() shouldBe 2
  }

  "test adding an element to mutable java collection" in {
    val scalaSeq: Seq[String] = Seq("a", "b")
    val javaList: java.util.List[String] = ScoringServiceImpl.convertScalaSeqToMutableJavaList(scalaSeq)
    javaList.add("c")
    javaList.size() shouldBe 3
  }

  "test adding multiple elements to mutable java collection" in {
    val scalaSeq: Seq[String] = Seq("a", "b")
    val javaList: java.util.List[String] = ScoringServiceImpl.convertScalaSeqToMutableJavaList(scalaSeq)
    val javaHashSet: java.util.Set[String] = new util.HashSet[String]();
    javaHashSet.add("x")
    javaHashSet.add("y")
    javaList.addAll(javaHashSet)
    javaList.size() shouldBe 4
  }

  "test adding an element to empty mutable java collection" in {
    val scalaSeq: Seq[String] = Seq()
    val javaList: java.util.List[String] = ScoringServiceImpl.convertScalaSeqToMutableJavaList(scalaSeq)
    javaList.add("c")
    javaList.size() shouldBe 1
  }

  "test adding multiple elements to empty mutable java collection" in {
    val scalaSeq: Seq[String] = Seq()
    val javaList: java.util.List[String] = ScoringServiceImpl.convertScalaSeqToMutableJavaList(scalaSeq)
    val javaHashSet: java.util.Set[String] = new util.HashSet[String]();
    javaHashSet.add("x")
    javaHashSet.add("y")
    javaList.addAll(javaHashSet)
    javaList.size() shouldBe 2
  }
}
