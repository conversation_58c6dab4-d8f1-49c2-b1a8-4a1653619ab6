package me.socure.json.controllerv1.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CreateModelRequestDto {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("commonName")
    private String commonName;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("h2oIdentifier")
    private String h2oIdentifier;
    
    @JsonProperty("engine")
    private String engine;
    
    @JsonProperty("feature")
    private String feature;
    
    @JsonProperty("config")
    private String config;
    
    @JsonProperty("isImplicit")
    private Boolean isImplicit;
    
    @JsonProperty("isDefault")
    private Boolean isDefault;
    
    @JsonProperty("defaultResponseName")
    private String defaultResponseName;
    
    @JsonProperty("defaultResponseVersion")
    private String defaultResponseVersion;
    
    @JsonProperty("productId")
    private Integer productId;
    
    @JsonProperty("isActive")
    private Boolean isActive;

    // Default constructor
    public CreateModelRequestDto() {
    }

    // Getters and setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getH2oIdentifier() {
        return h2oIdentifier;
    }

    public void setH2oIdentifier(String h2oIdentifier) {
        this.h2oIdentifier = h2oIdentifier;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Boolean getIsImplicit() {
        return isImplicit;
    }

    public void setIsImplicit(Boolean isImplicit) {
        this.isImplicit = isImplicit;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public String getDefaultResponseName() {
        return defaultResponseName;
    }

    public void setDefaultResponseName(String defaultResponseName) {
        this.defaultResponseName = defaultResponseName;
    }

    public String getDefaultResponseVersion() {
        return defaultResponseVersion;
    }

    public void setDefaultResponseVersion(String defaultResponseVersion) {
        this.defaultResponseVersion = defaultResponseVersion;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    // Method to convert to Scala CreateModelRequest
    public me.socure.model.management.client.dto.CreateModelRequest toScalaCreateModelRequest() {
        return new me.socure.model.management.client.dto.CreateModelRequest(
            this.name,
            this.commonName,
            scala.Option.apply(this.description),
            this.h2oIdentifier,
            this.engine,
            this.feature,
            scala.Option.apply(this.config),
            scala.Option.apply(this.isImplicit),
            scala.Option.apply(this.isDefault),
            this.defaultResponseName,
            this.defaultResponseVersion,
            this.productId,
            this.isActive != null ? this.isActive : true
        );
    }
}
