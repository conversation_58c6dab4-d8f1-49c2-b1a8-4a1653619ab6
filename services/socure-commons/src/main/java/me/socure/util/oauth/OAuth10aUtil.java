package me.socure.util.oauth;

import oauth.signpost.OAuthConsumer;
import oauth.signpost.basic.DefaultOAuthConsumer;
import oauth.signpost.exception.OAuthCommunicationException;
import oauth.signpost.exception.OAuthExpectationFailedException;
import oauth.signpost.exception.OAuthMessageSignerException;
import oauth.signpost.http.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import java.net.URI;
import static me.socure.util.log.SocureMarkers.*;

/**
 * Created by jamesanto on 7/22/15.
 */
public final class OAuth10aUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(OAuth10aUtil.class);

    private OAuth10aUtil() {
        //Prevent initialization
    }

    public static RestTemplateRequest sign(HttpMethod httpMethod, URI uri, HttpHeaders httpHeaders, String contentType, String consumerKey, String consumerSecret, String accessToken, String accessTokenSecret) throws OAuthCommunicationException, OAuthExpectationFailedException, OAuthMessageSignerException {
        OAuthConsumer oAuthConsumer = new DefaultOAuthConsumer(consumerKey, consumerSecret);
        oAuthConsumer.setTokenWithSecret(accessToken, accessTokenSecret);
        RestTemplateRequest request = new RestTemplateRequest(httpMethod, uri, httpHeaders, contentType);
        oAuthConsumer.sign(request);
        return request;
    }

    public static RestTemplateRequest sign(RestTemplateRequest request, String consumerKey, String consumerSecret, String accessToken, String accessTokenSecret) throws OAuthCommunicationException, OAuthExpectationFailedException, OAuthMessageSignerException {
        OAuthConsumer oAuthConsumer = new DefaultOAuthConsumer(consumerKey, consumerSecret);
        oAuthConsumer.setTokenWithSecret(accessToken, accessTokenSecret);
        oAuthConsumer.sign(request);
        return request;
    }

    public static RestTemplateRequest signSuppressed(HttpMethod httpMethod, URI uri, HttpHeaders httpHeaders, String contentType, String consumerKey, String consumerSecret, String accessToken, String accessTokenSecret) {
        try {
            return sign(httpMethod, uri, httpHeaders, contentType, consumerKey, consumerSecret, accessToken, accessTokenSecret);
        } catch (Throwable ex) {
            LOGGER.error(ENGINEERING, "Unable to sign oauth request", ex);
        }
        return null;
    }

    public static RestTemplateRequest signSuppressed(RestTemplateRequest request, String consumerKey, String consumerSecret, String accessToken, String accessTokenSecret) {
        try {
            return sign(request, consumerKey, consumerSecret, accessToken, accessTokenSecret);
        } catch (Throwable ex) {
            LOGGER.error(ENGINEERING, "Unable to sign oauth request", ex);
        }
        return null;
    }
}
