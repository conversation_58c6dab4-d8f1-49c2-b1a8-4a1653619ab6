package me.socure.service.doc.decision

/**
 * <AUTHOR>
 */
object DecisionResults extends Enumeration {
  type DecisionResult = Value

  val Accept: DecisionResult = Value("accept")
  val Reject: DecisionResult = Value("reject")
  val Review: DecisionResult = Value("review")
  val Resubmit: DecisionResult = Value("resubmit")

  def findByName(str: String): Option[DecisionResult] = values.find(_.toString.equalsIgnoreCase(str))

}
